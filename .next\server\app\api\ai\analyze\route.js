/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/analyze/route";
exports.ids = ["app/api/ai/analyze/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fanalyze%2Froute&page=%2Fapi%2Fai%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fanalyze%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fanalyze%2Froute&page=%2Fapi%2Fai%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fanalyze%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_User_Downloads_factorygame_src_app_api_ai_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/analyze/route.ts */ \"(rsc)/./src/app/api/ai/analyze/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/analyze/route\",\n        pathname: \"/api/ai/analyze\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/analyze/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\app\\\\api\\\\ai\\\\analyze\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_User_Downloads_factorygame_src_app_api_ai_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fanalyze%2Froute&page=%2Fapi%2Fai%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fanalyze%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/ai/gameStateSerializer.ts":
/*!***************************************!*\
  !*** ./src/ai/gameStateSerializer.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GameStateSerializer: () => (/* binding */ GameStateSerializer)\n/* harmony export */ });\n/* harmony import */ var _types_game__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/game */ \"(rsc)/./src/types/game.ts\");\n\nclass GameStateSerializer {\n    static serialize(gameState, analytics) {\n        const serialized = {\n            metadata: {\n                version: '1.0.0',\n                timestamp: Date.now(),\n                gridSize: gameState.gridSize,\n                gameTime: gameState.gameTime,\n                isRunning: gameState.isRunning\n            },\n            components: this.serializeComponents(gameState.components),\n            resources: this.serializeResources(gameState.resources),\n            statistics: {\n                totalProduction: this.mapToRecord(gameState.statistics.totalProduction),\n                totalConsumption: this.mapToRecord(gameState.statistics.totalConsumption),\n                efficiency: gameState.statistics.efficiency,\n                bottlenecks: gameState.statistics.bottlenecks\n            }\n        };\n        if (analytics) {\n            serialized.analytics = this.serializeAnalytics(analytics);\n        }\n        return serialized;\n    }\n    static deserialize(serialized) {\n        const components = new Map();\n        for (const comp of serialized.components){\n            const component = {\n                id: comp.id,\n                type: comp.type,\n                position: comp.position,\n                direction: comp.direction,\n                recipe: comp.recipe ? _types_game__WEBPACK_IMPORTED_MODULE_0__.RECIPES[comp.recipe] : undefined,\n                inventory: new Map(Object.entries(comp.inventory).map(([k, v])=>[\n                        k,\n                        v\n                    ])),\n                connections: comp.connections,\n                isActive: comp.isActive,\n                lastProcessTime: comp.lastProcessTime\n            };\n            components.set(comp.id, component);\n        }\n        return {\n            components,\n            gridSize: serialized.metadata.gridSize,\n            gameTime: serialized.metadata.gameTime,\n            isRunning: serialized.metadata.isRunning,\n            resources: new Map(Object.entries(serialized.resources).map(([k, v])=>[\n                    k,\n                    v\n                ])),\n            statistics: {\n                totalProduction: this.recordToMap(serialized.statistics.totalProduction),\n                totalConsumption: this.recordToMap(serialized.statistics.totalConsumption),\n                efficiency: serialized.statistics.efficiency,\n                bottlenecks: serialized.statistics.bottlenecks\n            }\n        };\n    }\n    static serializeComponents(components) {\n        return Array.from(components.values()).map((component)=>{\n            const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n            return {\n                id: component.id,\n                type: component.type,\n                position: component.position,\n                direction: component.direction,\n                recipe: component.recipe?.id,\n                inventory: this.mapToRecord(component.inventory),\n                connections: component.connections,\n                isActive: component.isActive,\n                lastProcessTime: component.lastProcessTime,\n                metadata: {\n                    name: definition.name,\n                    description: definition.description,\n                    maxInputs: definition.maxInputs,\n                    maxOutputs: definition.maxOutputs,\n                    speed: definition.speed,\n                    size: definition.size\n                }\n            };\n        });\n    }\n    static serializeResources(resources) {\n        return this.mapToRecord(resources);\n    }\n    static serializeAnalytics(analytics) {\n        const componentAnalytics = {};\n        analytics.componentAnalytics.forEach((value, key)=>{\n            componentAnalytics[key] = {\n                utilization: value.utilization,\n                throughput: value.throughput,\n                efficiency: value.efficiency,\n                bottleneckScore: value.bottleneckScore,\n                inputStarvation: value.inputStarvation,\n                outputBlocked: value.outputBlocked\n            };\n        });\n        const resourceFlows = {};\n        analytics.resourceFlows.forEach((value, key)=>{\n            resourceFlows[key] = {\n                totalProduction: value.totalProduction,\n                totalConsumption: value.totalConsumption,\n                netFlow: value.netFlow,\n                flowEfficiency: value.flowEfficiency,\n                bottleneckComponents: value.bottleneckComponents\n            };\n        });\n        return {\n            overallEfficiency: analytics.overallEfficiency,\n            totalThroughput: analytics.totalThroughput,\n            performanceScore: analytics.performanceScore,\n            bottlenecks: analytics.bottlenecks,\n            recommendations: analytics.recommendations,\n            componentAnalytics,\n            resourceFlows\n        };\n    }\n    static mapToRecord(map) {\n        const record = {};\n        map.forEach((value, key)=>{\n            record[key] = value;\n        });\n        return record;\n    }\n    static recordToMap(record) {\n        return new Map(Object.entries(record));\n    }\n    static generateAIPrompt(serialized) {\n        const prompt = `\n# Factory Builder Game State Analysis\n\n## Current Factory Status\n- **Grid Size**: ${serialized.metadata.gridSize.width}x${serialized.metadata.gridSize.height}\n- **Game Time**: ${Math.round(serialized.metadata.gameTime / 1000)}s\n- **Status**: ${serialized.metadata.isRunning ? 'Running' : 'Paused'}\n- **Overall Efficiency**: ${(serialized.statistics.efficiency * 100).toFixed(1)}%\n\n## Components (${serialized.components.length} total)\n${serialized.components.map((comp)=>`\n- **${comp.metadata.name}** (${comp.id.slice(0, 8)})\n  - Position: (${comp.position.x}, ${comp.position.y})\n  - Direction: ${[\n                'North',\n                'East',\n                'South',\n                'West'\n            ][comp.direction]}\n  - Active: ${comp.isActive ? 'Yes' : 'No'}\n  - Recipe: ${comp.recipe || 'None'}\n  - Inventory: ${Object.entries(comp.inventory).map(([r, a])=>`${r}: ${a}`).join(', ') || 'Empty'}\n  - Connections: ${comp.connections.inputs.length} inputs, ${comp.connections.outputs.length} outputs\n`).join('')}\n\n## Resources\n${Object.entries(serialized.resources).map(([resource, amount])=>`\n- **${resource.replace('_', ' ')}**: ${amount}\n`).join('')}\n\n## Production Statistics\n${Object.entries(serialized.statistics.totalProduction).map(([resource, amount])=>`\n- **${resource.replace('_', ' ')} Production**: ${amount}/min\n`).join('')}\n\n${Object.entries(serialized.statistics.totalConsumption).map(([resource, amount])=>`\n- **${resource.replace('_', ' ')} Consumption**: ${amount}/min\n`).join('')}\n\n${serialized.analytics ? `\n## Performance Analytics\n- **Performance Score**: ${serialized.analytics.performanceScore}/100\n- **Total Throughput**: ${serialized.analytics.totalThroughput.toFixed(1)} items/min\n- **Bottlenecks**: ${serialized.analytics.bottlenecks.length} components\n\n### Recommendations\n${serialized.analytics.recommendations.map((rec)=>`- ${rec}`).join('\\n')}\n\n### Component Performance\n${Object.entries(serialized.analytics.componentAnalytics).map(([id, analytics])=>`\n- **${id.slice(0, 8)}**: ${(analytics.utilization * 100).toFixed(1)}% utilization, ${analytics.throughput.toFixed(1)} items/min\n`).join('')}\n` : ''}\n\n## Game Mechanics Reference\n\n### Component Types\n- **Conveyor Belt**: Transports items (15 items/sec)\n- **Mining Drill**: Extracts resources (0.5 items/sec)\n- **Assembling Machine**: Crafts items (0.75x speed multiplier)\n- **Storage Chest**: Stores items (30 items/sec throughput)\n- **Splitter**: Divides input into 2 outputs (15 items/sec)\n- **Merger**: Combines 2 inputs into 1 output (15 items/sec)\n\n### Available Recipes\n- **Iron Plate**: 1 Iron Ore → 1 Iron Plate (3.2s)\n- **Copper Plate**: 1 Copper Ore → 1 Copper Plate (3.2s)\n- **Iron Gear**: 2 Iron Plates → 1 Gear (0.5s)\n- **Electronic Circuit**: 1 Iron Plate + 3 Copper Plates → 1 Circuit (0.5s)\n\n### Resource Types\n- **Raw Materials**: Iron Ore, Copper Ore, Coal\n- **Intermediate Products**: Iron Plate, Copper Plate\n- **Advanced Products**: Gear, Electronic Circuit\n\nPlease analyze this factory and provide suggestions for optimization, efficiency improvements, or problem identification.\n`;\n        return prompt.trim();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYWkvZ2FtZVN0YXRlU2VyaWFsaXplci50cyIsIm1hcHBpbmdzIjoiOzs7OztBQVNzQjtBQXFFZixNQUFNRTtJQUNYLE9BQWNDLFVBQVVDLFNBQW9CLEVBQUVDLFNBQTRCLEVBQXVCO1FBQy9GLE1BQU1DLGFBQWtDO1lBQ3RDQyxVQUFVO2dCQUNSQyxTQUFTO2dCQUNUQyxXQUFXQyxLQUFLQyxHQUFHO2dCQUNuQkMsVUFBVVIsVUFBVVEsUUFBUTtnQkFDNUJDLFVBQVVULFVBQVVTLFFBQVE7Z0JBQzVCQyxXQUFXVixVQUFVVSxTQUFTO1lBQ2hDO1lBQ0FDLFlBQVksSUFBSSxDQUFDQyxtQkFBbUIsQ0FBQ1osVUFBVVcsVUFBVTtZQUN6REUsV0FBVyxJQUFJLENBQUNDLGtCQUFrQixDQUFDZCxVQUFVYSxTQUFTO1lBQ3RERSxZQUFZO2dCQUNWQyxpQkFBaUIsSUFBSSxDQUFDQyxXQUFXLENBQUNqQixVQUFVZSxVQUFVLENBQUNDLGVBQWU7Z0JBQ3RFRSxrQkFBa0IsSUFBSSxDQUFDRCxXQUFXLENBQUNqQixVQUFVZSxVQUFVLENBQUNHLGdCQUFnQjtnQkFDeEVDLFlBQVluQixVQUFVZSxVQUFVLENBQUNJLFVBQVU7Z0JBQzNDQyxhQUFhcEIsVUFBVWUsVUFBVSxDQUFDSyxXQUFXO1lBQy9DO1FBQ0Y7UUFFQSxJQUFJbkIsV0FBVztZQUNiQyxXQUFXRCxTQUFTLEdBQUcsSUFBSSxDQUFDb0Isa0JBQWtCLENBQUNwQjtRQUNqRDtRQUVBLE9BQU9DO0lBQ1Q7SUFFQSxPQUFjb0IsWUFBWXBCLFVBQStCLEVBQWE7UUFDcEUsTUFBTVMsYUFBYSxJQUFJWTtRQUV2QixLQUFLLE1BQU1DLFFBQVF0QixXQUFXUyxVQUFVLENBQUU7WUFDeEMsTUFBTWMsWUFBMkI7Z0JBQy9CQyxJQUFJRixLQUFLRSxFQUFFO2dCQUNYQyxNQUFNSCxLQUFLRyxJQUFJO2dCQUNmQyxVQUFVSixLQUFLSSxRQUFRO2dCQUN2QkMsV0FBV0wsS0FBS0ssU0FBUztnQkFDekJDLFFBQVFOLEtBQUtNLE1BQU0sR0FBR2pDLGdEQUFPLENBQUMyQixLQUFLTSxNQUFNLENBQUMsR0FBR0M7Z0JBQzdDQyxXQUFXLElBQUlULElBQUlVLE9BQU9DLE9BQU8sQ0FBQ1YsS0FBS1EsU0FBUyxFQUFFRyxHQUFHLENBQUMsQ0FBQyxDQUFDQyxHQUFHQyxFQUFFLEdBQUs7d0JBQUNEO3dCQUFtQkM7cUJBQUU7Z0JBQ3hGQyxhQUFhZCxLQUFLYyxXQUFXO2dCQUM3QkMsVUFBVWYsS0FBS2UsUUFBUTtnQkFDdkJDLGlCQUFpQmhCLEtBQUtnQixlQUFlO1lBQ3ZDO1lBQ0E3QixXQUFXOEIsR0FBRyxDQUFDakIsS0FBS0UsRUFBRSxFQUFFRDtRQUMxQjtRQUVBLE9BQU87WUFDTGQ7WUFDQUgsVUFBVU4sV0FBV0MsUUFBUSxDQUFDSyxRQUFRO1lBQ3RDQyxVQUFVUCxXQUFXQyxRQUFRLENBQUNNLFFBQVE7WUFDdENDLFdBQVdSLFdBQVdDLFFBQVEsQ0FBQ08sU0FBUztZQUN4Q0csV0FBVyxJQUFJVSxJQUFJVSxPQUFPQyxPQUFPLENBQUNoQyxXQUFXVyxTQUFTLEVBQUVzQixHQUFHLENBQUMsQ0FBQyxDQUFDQyxHQUFHQyxFQUFFLEdBQUs7b0JBQUNEO29CQUFtQkM7aUJBQUU7WUFDOUZ0QixZQUFZO2dCQUNWQyxpQkFBaUIsSUFBSSxDQUFDMEIsV0FBVyxDQUFDeEMsV0FBV2EsVUFBVSxDQUFDQyxlQUFlO2dCQUN2RUUsa0JBQWtCLElBQUksQ0FBQ3dCLFdBQVcsQ0FBQ3hDLFdBQVdhLFVBQVUsQ0FBQ0csZ0JBQWdCO2dCQUN6RUMsWUFBWWpCLFdBQVdhLFVBQVUsQ0FBQ0ksVUFBVTtnQkFDNUNDLGFBQWFsQixXQUFXYSxVQUFVLENBQUNLLFdBQVc7WUFDaEQ7UUFDRjtJQUNGO0lBRUEsT0FBZVIsb0JBQW9CRCxVQUFzQyxFQUF5QjtRQUNoRyxPQUFPZ0MsTUFBTUMsSUFBSSxDQUFDakMsV0FBV2tDLE1BQU0sSUFBSVYsR0FBRyxDQUFDVixDQUFBQTtZQUN6QyxNQUFNcUIsYUFBYWxELDhEQUFxQixDQUFDNkIsVUFBVUUsSUFBSSxDQUFDO1lBRXhELE9BQU87Z0JBQ0xELElBQUlELFVBQVVDLEVBQUU7Z0JBQ2hCQyxNQUFNRixVQUFVRSxJQUFJO2dCQUNwQkMsVUFBVUgsVUFBVUcsUUFBUTtnQkFDNUJDLFdBQVdKLFVBQVVJLFNBQVM7Z0JBQzlCQyxRQUFRTCxVQUFVSyxNQUFNLEVBQUVKO2dCQUMxQk0sV0FBVyxJQUFJLENBQUNmLFdBQVcsQ0FBQ1EsVUFBVU8sU0FBUztnQkFDL0NNLGFBQWFiLFVBQVVhLFdBQVc7Z0JBQ2xDQyxVQUFVZCxVQUFVYyxRQUFRO2dCQUM1QkMsaUJBQWlCZixVQUFVZSxlQUFlO2dCQUMxQ3JDLFVBQVU7b0JBQ1I0QyxNQUFNRCxXQUFXQyxJQUFJO29CQUNyQkMsYUFBYUYsV0FBV0UsV0FBVztvQkFDbkNDLFdBQVdILFdBQVdHLFNBQVM7b0JBQy9CQyxZQUFZSixXQUFXSSxVQUFVO29CQUNqQ0MsT0FBT0wsV0FBV0ssS0FBSztvQkFDdkJDLE1BQU1OLFdBQVdNLElBQUk7Z0JBQ3ZCO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsT0FBZXRDLG1CQUFtQkQsU0FBb0MsRUFBMEI7UUFDOUYsT0FBTyxJQUFJLENBQUNJLFdBQVcsQ0FBQ0o7SUFDMUI7SUFFQSxPQUFlUSxtQkFBbUJwQixTQUEyQixFQUF1QjtRQUNsRixNQUFNb0QscUJBQTBDLENBQUM7UUFDakRwRCxVQUFVb0Qsa0JBQWtCLENBQUNDLE9BQU8sQ0FBQyxDQUFDQyxPQUFPQztZQUMzQ0gsa0JBQWtCLENBQUNHLElBQUksR0FBRztnQkFDeEJDLGFBQWFGLE1BQU1FLFdBQVc7Z0JBQzlCQyxZQUFZSCxNQUFNRyxVQUFVO2dCQUM1QnZDLFlBQVlvQyxNQUFNcEMsVUFBVTtnQkFDNUJ3QyxpQkFBaUJKLE1BQU1JLGVBQWU7Z0JBQ3RDQyxpQkFBaUJMLE1BQU1LLGVBQWU7Z0JBQ3RDQyxlQUFlTixNQUFNTSxhQUFhO1lBQ3BDO1FBQ0Y7UUFFQSxNQUFNQyxnQkFBcUMsQ0FBQztRQUM1QzdELFVBQVU2RCxhQUFhLENBQUNSLE9BQU8sQ0FBQyxDQUFDQyxPQUFPQztZQUN0Q00sYUFBYSxDQUFDTixJQUFJLEdBQUc7Z0JBQ25CeEMsaUJBQWlCdUMsTUFBTXZDLGVBQWU7Z0JBQ3RDRSxrQkFBa0JxQyxNQUFNckMsZ0JBQWdCO2dCQUN4QzZDLFNBQVNSLE1BQU1RLE9BQU87Z0JBQ3RCQyxnQkFBZ0JULE1BQU1TLGNBQWM7Z0JBQ3BDQyxzQkFBc0JWLE1BQU1VLG9CQUFvQjtZQUNsRDtRQUNGO1FBRUEsT0FBTztZQUNMQyxtQkFBbUJqRSxVQUFVaUUsaUJBQWlCO1lBQzlDQyxpQkFBaUJsRSxVQUFVa0UsZUFBZTtZQUMxQ0Msa0JBQWtCbkUsVUFBVW1FLGdCQUFnQjtZQUM1Q2hELGFBQWFuQixVQUFVbUIsV0FBVztZQUNsQ2lELGlCQUFpQnBFLFVBQVVvRSxlQUFlO1lBQzFDaEI7WUFDQVM7UUFDRjtJQUNGO0lBRUEsT0FBZTdDLFlBQWVrQixHQUFtQixFQUFxQjtRQUNwRSxNQUFNbUMsU0FBNEIsQ0FBQztRQUNuQ25DLElBQUltQixPQUFPLENBQUMsQ0FBQ0MsT0FBT0M7WUFDbEJjLE1BQU0sQ0FBQ2QsSUFBSSxHQUFHRDtRQUNoQjtRQUNBLE9BQU9lO0lBQ1Q7SUFFQSxPQUFlNUIsWUFBZTRCLE1BQXlCLEVBQWtCO1FBQ3ZFLE9BQU8sSUFBSS9DLElBQUlVLE9BQU9DLE9BQU8sQ0FBQ29DO0lBQ2hDO0lBRUEsT0FBY0MsaUJBQWlCckUsVUFBK0IsRUFBVTtRQUN0RSxNQUFNc0UsU0FBUyxDQUFDOzs7O2lCQUlILEVBQUV0RSxXQUFXQyxRQUFRLENBQUNLLFFBQVEsQ0FBQ2lFLEtBQUssQ0FBQyxDQUFDLEVBQUV2RSxXQUFXQyxRQUFRLENBQUNLLFFBQVEsQ0FBQ2tFLE1BQU0sQ0FBQztpQkFDNUUsRUFBRUMsS0FBS0MsS0FBSyxDQUFDMUUsV0FBV0MsUUFBUSxDQUFDTSxRQUFRLEdBQUcsTUFBTTtjQUNyRCxFQUFFUCxXQUFXQyxRQUFRLENBQUNPLFNBQVMsR0FBRyxZQUFZLFNBQVM7MEJBQzNDLEVBQUUsQ0FBQ1IsV0FBV2EsVUFBVSxDQUFDSSxVQUFVLEdBQUcsR0FBRSxFQUFHMEQsT0FBTyxDQUFDLEdBQUc7O2VBRWpFLEVBQUUzRSxXQUFXUyxVQUFVLENBQUNtRSxNQUFNLENBQUM7QUFDOUMsRUFBRTVFLFdBQVdTLFVBQVUsQ0FBQ3dCLEdBQUcsQ0FBQ1gsQ0FBQUEsT0FBUSxDQUFDO0lBQ2pDLEVBQUVBLEtBQUtyQixRQUFRLENBQUM0QyxJQUFJLENBQUMsSUFBSSxFQUFFdkIsS0FBS0UsRUFBRSxDQUFDcUQsS0FBSyxDQUFDLEdBQUcsR0FBRztlQUNwQyxFQUFFdkQsS0FBS0ksUUFBUSxDQUFDb0QsQ0FBQyxDQUFDLEVBQUUsRUFBRXhELEtBQUtJLFFBQVEsQ0FBQ3FELENBQUMsQ0FBQztlQUN0QyxFQUFFO2dCQUFDO2dCQUFTO2dCQUFRO2dCQUFTO2FBQU8sQ0FBQ3pELEtBQUtLLFNBQVMsQ0FBQyxDQUFDO1lBQ3hELEVBQUVMLEtBQUtlLFFBQVEsR0FBRyxRQUFRLEtBQUs7WUFDL0IsRUFBRWYsS0FBS00sTUFBTSxJQUFJLE9BQU87ZUFDckIsRUFBRUcsT0FBT0MsT0FBTyxDQUFDVixLQUFLUSxTQUFTLEVBQUVHLEdBQUcsQ0FBQyxDQUFDLENBQUMrQyxHQUFHQyxFQUFFLEdBQUssR0FBR0QsRUFBRSxFQUFFLEVBQUVDLEdBQUcsRUFBRUMsSUFBSSxDQUFDLFNBQVMsUUFBUTtpQkFDbkYsRUFBRTVELEtBQUtjLFdBQVcsQ0FBQytDLE1BQU0sQ0FBQ1AsTUFBTSxDQUFDLFNBQVMsRUFBRXRELEtBQUtjLFdBQVcsQ0FBQ2dELE9BQU8sQ0FBQ1IsTUFBTSxDQUFDO0FBQzdGLENBQUMsRUFBRU0sSUFBSSxDQUFDLElBQUk7OztBQUdaLEVBQUVuRCxPQUFPQyxPQUFPLENBQUNoQyxXQUFXVyxTQUFTLEVBQUVzQixHQUFHLENBQUMsQ0FBQyxDQUFDb0QsVUFBVUMsT0FBTyxHQUFLLENBQUM7SUFDaEUsRUFBRUQsU0FBU0UsT0FBTyxDQUFDLEtBQUssS0FBSyxJQUFJLEVBQUVELE9BQU87QUFDOUMsQ0FBQyxFQUFFSixJQUFJLENBQUMsSUFBSTs7O0FBR1osRUFBRW5ELE9BQU9DLE9BQU8sQ0FBQ2hDLFdBQVdhLFVBQVUsQ0FBQ0MsZUFBZSxFQUFFbUIsR0FBRyxDQUFDLENBQUMsQ0FBQ29ELFVBQVVDLE9BQU8sR0FBSyxDQUFDO0lBQ2pGLEVBQUVELFNBQVNFLE9BQU8sQ0FBQyxLQUFLLEtBQUssZUFBZSxFQUFFRCxPQUFPO0FBQ3pELENBQUMsRUFBRUosSUFBSSxDQUFDLElBQUk7O0FBRVosRUFBRW5ELE9BQU9DLE9BQU8sQ0FBQ2hDLFdBQVdhLFVBQVUsQ0FBQ0csZ0JBQWdCLEVBQUVpQixHQUFHLENBQUMsQ0FBQyxDQUFDb0QsVUFBVUMsT0FBTyxHQUFLLENBQUM7SUFDbEYsRUFBRUQsU0FBU0UsT0FBTyxDQUFDLEtBQUssS0FBSyxnQkFBZ0IsRUFBRUQsT0FBTztBQUMxRCxDQUFDLEVBQUVKLElBQUksQ0FBQyxJQUFJOztBQUVaLEVBQUVsRixXQUFXRCxTQUFTLEdBQUcsQ0FBQzs7eUJBRUQsRUFBRUMsV0FBV0QsU0FBUyxDQUFDbUUsZ0JBQWdCLENBQUM7d0JBQ3pDLEVBQUVsRSxXQUFXRCxTQUFTLENBQUNrRSxlQUFlLENBQUNVLE9BQU8sQ0FBQyxHQUFHO21CQUN2RCxFQUFFM0UsV0FBV0QsU0FBUyxDQUFDbUIsV0FBVyxDQUFDMEQsTUFBTSxDQUFDOzs7QUFHN0QsRUFBRTVFLFdBQVdELFNBQVMsQ0FBQ29FLGVBQWUsQ0FBQ2xDLEdBQUcsQ0FBQ3VELENBQUFBLE1BQU8sQ0FBQyxFQUFFLEVBQUVBLEtBQUssRUFBRU4sSUFBSSxDQUFDLE1BQU07OztBQUd6RSxFQUFFbkQsT0FBT0MsT0FBTyxDQUFDaEMsV0FBV0QsU0FBUyxDQUFDb0Qsa0JBQWtCLEVBQUVsQixHQUFHLENBQUMsQ0FBQyxDQUFDVCxJQUFJekIsVUFBVSxHQUFLLENBQUM7SUFDaEYsRUFBRXlCLEdBQUdxRCxLQUFLLENBQUMsR0FBRyxHQUFHLElBQUksRUFBRSxDQUFDOUUsVUFBVXdELFdBQVcsR0FBRyxHQUFFLEVBQUdvQixPQUFPLENBQUMsR0FBRyxlQUFlLEVBQUU1RSxVQUFVeUQsVUFBVSxDQUFDbUIsT0FBTyxDQUFDLEdBQUc7QUFDckgsQ0FBQyxFQUFFTyxJQUFJLENBQUMsSUFBSTtBQUNaLENBQUMsR0FBRyxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUF3QlAsQ0FBQztRQUVHLE9BQU9aLE9BQU9tQixJQUFJO0lBQ3BCO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcc3JjXFxhaVxcZ2FtZVN0YXRlU2VyaWFsaXplci50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBHYW1lU3RhdGUsXG4gIEdhbWVDb21wb25lbnQsXG4gIENvbXBvbmVudFR5cGUsXG4gIFJlc291cmNlVHlwZSxcbiAgRGlyZWN0aW9uLFxuICBQb3NpdGlvbixcbiAgQ09NUE9ORU5UX0RFRklOSVRJT05TLFxuICBSRUNJUEVTLFxufSBmcm9tICdAL3R5cGVzL2dhbWUnO1xuaW1wb3J0IHsgRmFjdG9yeUFuYWx5dGljcyB9IGZyb20gJ0AvYW5hbHl0aWNzL3BlcmZvcm1hbmNlQW5hbHl6ZXInO1xuXG5leHBvcnQgaW50ZXJmYWNlIFNlcmlhbGl6ZWRHYW1lU3RhdGUge1xuICBtZXRhZGF0YToge1xuICAgIHZlcnNpb246IHN0cmluZztcbiAgICB0aW1lc3RhbXA6IG51bWJlcjtcbiAgICBncmlkU2l6ZTogeyB3aWR0aDogbnVtYmVyOyBoZWlnaHQ6IG51bWJlciB9O1xuICAgIGdhbWVUaW1lOiBudW1iZXI7XG4gICAgaXNSdW5uaW5nOiBib29sZWFuO1xuICB9O1xuICBjb21wb25lbnRzOiBTZXJpYWxpemVkQ29tcG9uZW50W107XG4gIHJlc291cmNlczogUmVjb3JkPHN0cmluZywgbnVtYmVyPjtcbiAgc3RhdGlzdGljczoge1xuICAgIHRvdGFsUHJvZHVjdGlvbjogUmVjb3JkPHN0cmluZywgbnVtYmVyPjtcbiAgICB0b3RhbENvbnN1bXB0aW9uOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+O1xuICAgIGVmZmljaWVuY3k6IG51bWJlcjtcbiAgICBib3R0bGVuZWNrczogc3RyaW5nW107XG4gIH07XG4gIGFuYWx5dGljcz86IFNlcmlhbGl6ZWRBbmFseXRpY3M7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU2VyaWFsaXplZENvbXBvbmVudCB7XG4gIGlkOiBzdHJpbmc7XG4gIHR5cGU6IENvbXBvbmVudFR5cGU7XG4gIHBvc2l0aW9uOiBQb3NpdGlvbjtcbiAgZGlyZWN0aW9uOiBEaXJlY3Rpb247XG4gIHJlY2lwZT86IHN0cmluZzsgLy8gcmVjaXBlIElEXG4gIGludmVudG9yeTogUmVjb3JkPHN0cmluZywgbnVtYmVyPjtcbiAgY29ubmVjdGlvbnM6IHtcbiAgICBpbnB1dHM6IHN0cmluZ1tdO1xuICAgIG91dHB1dHM6IHN0cmluZ1tdO1xuICB9O1xuICBpc0FjdGl2ZTogYm9vbGVhbjtcbiAgbGFzdFByb2Nlc3NUaW1lOiBudW1iZXI7XG4gIC8vIEFkZGl0aW9uYWwgbWV0YWRhdGEgZm9yIEFJIHVuZGVyc3RhbmRpbmdcbiAgbWV0YWRhdGE6IHtcbiAgICBuYW1lOiBzdHJpbmc7XG4gICAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgICBtYXhJbnB1dHM6IG51bWJlcjtcbiAgICBtYXhPdXRwdXRzOiBudW1iZXI7XG4gICAgc3BlZWQ6IG51bWJlcjtcbiAgICBzaXplOiB7IHdpZHRoOiBudW1iZXI7IGhlaWdodDogbnVtYmVyIH07XG4gIH07XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU2VyaWFsaXplZEFuYWx5dGljcyB7XG4gIG92ZXJhbGxFZmZpY2llbmN5OiBudW1iZXI7XG4gIHRvdGFsVGhyb3VnaHB1dDogbnVtYmVyO1xuICBwZXJmb3JtYW5jZVNjb3JlOiBudW1iZXI7XG4gIGJvdHRsZW5lY2tzOiBzdHJpbmdbXTtcbiAgcmVjb21tZW5kYXRpb25zOiBzdHJpbmdbXTtcbiAgY29tcG9uZW50QW5hbHl0aWNzOiBSZWNvcmQ8c3RyaW5nLCB7XG4gICAgdXRpbGl6YXRpb246IG51bWJlcjtcbiAgICB0aHJvdWdocHV0OiBudW1iZXI7XG4gICAgZWZmaWNpZW5jeTogbnVtYmVyO1xuICAgIGJvdHRsZW5lY2tTY29yZTogbnVtYmVyO1xuICAgIGlucHV0U3RhcnZhdGlvbjogbnVtYmVyO1xuICAgIG91dHB1dEJsb2NrZWQ6IG51bWJlcjtcbiAgfT47XG4gIHJlc291cmNlRmxvd3M6IFJlY29yZDxzdHJpbmcsIHtcbiAgICB0b3RhbFByb2R1Y3Rpb246IG51bWJlcjtcbiAgICB0b3RhbENvbnN1bXB0aW9uOiBudW1iZXI7XG4gICAgbmV0RmxvdzogbnVtYmVyO1xuICAgIGZsb3dFZmZpY2llbmN5OiBudW1iZXI7XG4gICAgYm90dGxlbmVja0NvbXBvbmVudHM6IHN0cmluZ1tdO1xuICB9Pjtcbn1cblxuZXhwb3J0IGNsYXNzIEdhbWVTdGF0ZVNlcmlhbGl6ZXIge1xuICBwdWJsaWMgc3RhdGljIHNlcmlhbGl6ZShnYW1lU3RhdGU6IEdhbWVTdGF0ZSwgYW5hbHl0aWNzPzogRmFjdG9yeUFuYWx5dGljcyk6IFNlcmlhbGl6ZWRHYW1lU3RhdGUge1xuICAgIGNvbnN0IHNlcmlhbGl6ZWQ6IFNlcmlhbGl6ZWRHYW1lU3RhdGUgPSB7XG4gICAgICBtZXRhZGF0YToge1xuICAgICAgICB2ZXJzaW9uOiAnMS4wLjAnLFxuICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgIGdyaWRTaXplOiBnYW1lU3RhdGUuZ3JpZFNpemUsXG4gICAgICAgIGdhbWVUaW1lOiBnYW1lU3RhdGUuZ2FtZVRpbWUsXG4gICAgICAgIGlzUnVubmluZzogZ2FtZVN0YXRlLmlzUnVubmluZyxcbiAgICAgIH0sXG4gICAgICBjb21wb25lbnRzOiB0aGlzLnNlcmlhbGl6ZUNvbXBvbmVudHMoZ2FtZVN0YXRlLmNvbXBvbmVudHMpLFxuICAgICAgcmVzb3VyY2VzOiB0aGlzLnNlcmlhbGl6ZVJlc291cmNlcyhnYW1lU3RhdGUucmVzb3VyY2VzKSxcbiAgICAgIHN0YXRpc3RpY3M6IHtcbiAgICAgICAgdG90YWxQcm9kdWN0aW9uOiB0aGlzLm1hcFRvUmVjb3JkKGdhbWVTdGF0ZS5zdGF0aXN0aWNzLnRvdGFsUHJvZHVjdGlvbiksXG4gICAgICAgIHRvdGFsQ29uc3VtcHRpb246IHRoaXMubWFwVG9SZWNvcmQoZ2FtZVN0YXRlLnN0YXRpc3RpY3MudG90YWxDb25zdW1wdGlvbiksXG4gICAgICAgIGVmZmljaWVuY3k6IGdhbWVTdGF0ZS5zdGF0aXN0aWNzLmVmZmljaWVuY3ksXG4gICAgICAgIGJvdHRsZW5lY2tzOiBnYW1lU3RhdGUuc3RhdGlzdGljcy5ib3R0bGVuZWNrcyxcbiAgICAgIH0sXG4gICAgfTtcblxuICAgIGlmIChhbmFseXRpY3MpIHtcbiAgICAgIHNlcmlhbGl6ZWQuYW5hbHl0aWNzID0gdGhpcy5zZXJpYWxpemVBbmFseXRpY3MoYW5hbHl0aWNzKTtcbiAgICB9XG5cbiAgICByZXR1cm4gc2VyaWFsaXplZDtcbiAgfVxuXG4gIHB1YmxpYyBzdGF0aWMgZGVzZXJpYWxpemUoc2VyaWFsaXplZDogU2VyaWFsaXplZEdhbWVTdGF0ZSk6IEdhbWVTdGF0ZSB7XG4gICAgY29uc3QgY29tcG9uZW50cyA9IG5ldyBNYXA8c3RyaW5nLCBHYW1lQ29tcG9uZW50PigpO1xuICAgIFxuICAgIGZvciAoY29uc3QgY29tcCBvZiBzZXJpYWxpemVkLmNvbXBvbmVudHMpIHtcbiAgICAgIGNvbnN0IGNvbXBvbmVudDogR2FtZUNvbXBvbmVudCA9IHtcbiAgICAgICAgaWQ6IGNvbXAuaWQsXG4gICAgICAgIHR5cGU6IGNvbXAudHlwZSxcbiAgICAgICAgcG9zaXRpb246IGNvbXAucG9zaXRpb24sXG4gICAgICAgIGRpcmVjdGlvbjogY29tcC5kaXJlY3Rpb24sXG4gICAgICAgIHJlY2lwZTogY29tcC5yZWNpcGUgPyBSRUNJUEVTW2NvbXAucmVjaXBlXSA6IHVuZGVmaW5lZCxcbiAgICAgICAgaW52ZW50b3J5OiBuZXcgTWFwKE9iamVjdC5lbnRyaWVzKGNvbXAuaW52ZW50b3J5KS5tYXAoKFtrLCB2XSkgPT4gW2sgYXMgUmVzb3VyY2VUeXBlLCB2XSkpLFxuICAgICAgICBjb25uZWN0aW9uczogY29tcC5jb25uZWN0aW9ucyxcbiAgICAgICAgaXNBY3RpdmU6IGNvbXAuaXNBY3RpdmUsXG4gICAgICAgIGxhc3RQcm9jZXNzVGltZTogY29tcC5sYXN0UHJvY2Vzc1RpbWUsXG4gICAgICB9O1xuICAgICAgY29tcG9uZW50cy5zZXQoY29tcC5pZCwgY29tcG9uZW50KTtcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgY29tcG9uZW50cyxcbiAgICAgIGdyaWRTaXplOiBzZXJpYWxpemVkLm1ldGFkYXRhLmdyaWRTaXplLFxuICAgICAgZ2FtZVRpbWU6IHNlcmlhbGl6ZWQubWV0YWRhdGEuZ2FtZVRpbWUsXG4gICAgICBpc1J1bm5pbmc6IHNlcmlhbGl6ZWQubWV0YWRhdGEuaXNSdW5uaW5nLFxuICAgICAgcmVzb3VyY2VzOiBuZXcgTWFwKE9iamVjdC5lbnRyaWVzKHNlcmlhbGl6ZWQucmVzb3VyY2VzKS5tYXAoKFtrLCB2XSkgPT4gW2sgYXMgUmVzb3VyY2VUeXBlLCB2XSkpLFxuICAgICAgc3RhdGlzdGljczoge1xuICAgICAgICB0b3RhbFByb2R1Y3Rpb246IHRoaXMucmVjb3JkVG9NYXAoc2VyaWFsaXplZC5zdGF0aXN0aWNzLnRvdGFsUHJvZHVjdGlvbiksXG4gICAgICAgIHRvdGFsQ29uc3VtcHRpb246IHRoaXMucmVjb3JkVG9NYXAoc2VyaWFsaXplZC5zdGF0aXN0aWNzLnRvdGFsQ29uc3VtcHRpb24pLFxuICAgICAgICBlZmZpY2llbmN5OiBzZXJpYWxpemVkLnN0YXRpc3RpY3MuZWZmaWNpZW5jeSxcbiAgICAgICAgYm90dGxlbmVja3M6IHNlcmlhbGl6ZWQuc3RhdGlzdGljcy5ib3R0bGVuZWNrcyxcbiAgICAgIH0sXG4gICAgfTtcbiAgfVxuXG4gIHByaXZhdGUgc3RhdGljIHNlcmlhbGl6ZUNvbXBvbmVudHMoY29tcG9uZW50czogTWFwPHN0cmluZywgR2FtZUNvbXBvbmVudD4pOiBTZXJpYWxpemVkQ29tcG9uZW50W10ge1xuICAgIHJldHVybiBBcnJheS5mcm9tKGNvbXBvbmVudHMudmFsdWVzKCkpLm1hcChjb21wb25lbnQgPT4ge1xuICAgICAgY29uc3QgZGVmaW5pdGlvbiA9IENPTVBPTkVOVF9ERUZJTklUSU9OU1tjb21wb25lbnQudHlwZV07XG4gICAgICBcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGlkOiBjb21wb25lbnQuaWQsXG4gICAgICAgIHR5cGU6IGNvbXBvbmVudC50eXBlLFxuICAgICAgICBwb3NpdGlvbjogY29tcG9uZW50LnBvc2l0aW9uLFxuICAgICAgICBkaXJlY3Rpb246IGNvbXBvbmVudC5kaXJlY3Rpb24sXG4gICAgICAgIHJlY2lwZTogY29tcG9uZW50LnJlY2lwZT8uaWQsXG4gICAgICAgIGludmVudG9yeTogdGhpcy5tYXBUb1JlY29yZChjb21wb25lbnQuaW52ZW50b3J5KSxcbiAgICAgICAgY29ubmVjdGlvbnM6IGNvbXBvbmVudC5jb25uZWN0aW9ucyxcbiAgICAgICAgaXNBY3RpdmU6IGNvbXBvbmVudC5pc0FjdGl2ZSxcbiAgICAgICAgbGFzdFByb2Nlc3NUaW1lOiBjb21wb25lbnQubGFzdFByb2Nlc3NUaW1lLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgICAgICAgIG5hbWU6IGRlZmluaXRpb24ubmFtZSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogZGVmaW5pdGlvbi5kZXNjcmlwdGlvbixcbiAgICAgICAgICBtYXhJbnB1dHM6IGRlZmluaXRpb24ubWF4SW5wdXRzLFxuICAgICAgICAgIG1heE91dHB1dHM6IGRlZmluaXRpb24ubWF4T3V0cHV0cyxcbiAgICAgICAgICBzcGVlZDogZGVmaW5pdGlvbi5zcGVlZCxcbiAgICAgICAgICBzaXplOiBkZWZpbml0aW9uLnNpemUsXG4gICAgICAgIH0sXG4gICAgICB9O1xuICAgIH0pO1xuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgc2VyaWFsaXplUmVzb3VyY2VzKHJlc291cmNlczogTWFwPFJlc291cmNlVHlwZSwgbnVtYmVyPik6IFJlY29yZDxzdHJpbmcsIG51bWJlcj4ge1xuICAgIHJldHVybiB0aGlzLm1hcFRvUmVjb3JkKHJlc291cmNlcyk7XG4gIH1cblxuICBwcml2YXRlIHN0YXRpYyBzZXJpYWxpemVBbmFseXRpY3MoYW5hbHl0aWNzOiBGYWN0b3J5QW5hbHl0aWNzKTogU2VyaWFsaXplZEFuYWx5dGljcyB7XG4gICAgY29uc3QgY29tcG9uZW50QW5hbHl0aWNzOiBSZWNvcmQ8c3RyaW5nLCBhbnk+ID0ge307XG4gICAgYW5hbHl0aWNzLmNvbXBvbmVudEFuYWx5dGljcy5mb3JFYWNoKCh2YWx1ZSwga2V5KSA9PiB7XG4gICAgICBjb21wb25lbnRBbmFseXRpY3Nba2V5XSA9IHtcbiAgICAgICAgdXRpbGl6YXRpb246IHZhbHVlLnV0aWxpemF0aW9uLFxuICAgICAgICB0aHJvdWdocHV0OiB2YWx1ZS50aHJvdWdocHV0LFxuICAgICAgICBlZmZpY2llbmN5OiB2YWx1ZS5lZmZpY2llbmN5LFxuICAgICAgICBib3R0bGVuZWNrU2NvcmU6IHZhbHVlLmJvdHRsZW5lY2tTY29yZSxcbiAgICAgICAgaW5wdXRTdGFydmF0aW9uOiB2YWx1ZS5pbnB1dFN0YXJ2YXRpb24sXG4gICAgICAgIG91dHB1dEJsb2NrZWQ6IHZhbHVlLm91dHB1dEJsb2NrZWQsXG4gICAgICB9O1xuICAgIH0pO1xuXG4gICAgY29uc3QgcmVzb3VyY2VGbG93czogUmVjb3JkPHN0cmluZywgYW55PiA9IHt9O1xuICAgIGFuYWx5dGljcy5yZXNvdXJjZUZsb3dzLmZvckVhY2goKHZhbHVlLCBrZXkpID0+IHtcbiAgICAgIHJlc291cmNlRmxvd3Nba2V5XSA9IHtcbiAgICAgICAgdG90YWxQcm9kdWN0aW9uOiB2YWx1ZS50b3RhbFByb2R1Y3Rpb24sXG4gICAgICAgIHRvdGFsQ29uc3VtcHRpb246IHZhbHVlLnRvdGFsQ29uc3VtcHRpb24sXG4gICAgICAgIG5ldEZsb3c6IHZhbHVlLm5ldEZsb3csXG4gICAgICAgIGZsb3dFZmZpY2llbmN5OiB2YWx1ZS5mbG93RWZmaWNpZW5jeSxcbiAgICAgICAgYm90dGxlbmVja0NvbXBvbmVudHM6IHZhbHVlLmJvdHRsZW5lY2tDb21wb25lbnRzLFxuICAgICAgfTtcbiAgICB9KTtcblxuICAgIHJldHVybiB7XG4gICAgICBvdmVyYWxsRWZmaWNpZW5jeTogYW5hbHl0aWNzLm92ZXJhbGxFZmZpY2llbmN5LFxuICAgICAgdG90YWxUaHJvdWdocHV0OiBhbmFseXRpY3MudG90YWxUaHJvdWdocHV0LFxuICAgICAgcGVyZm9ybWFuY2VTY29yZTogYW5hbHl0aWNzLnBlcmZvcm1hbmNlU2NvcmUsXG4gICAgICBib3R0bGVuZWNrczogYW5hbHl0aWNzLmJvdHRsZW5lY2tzLFxuICAgICAgcmVjb21tZW5kYXRpb25zOiBhbmFseXRpY3MucmVjb21tZW5kYXRpb25zLFxuICAgICAgY29tcG9uZW50QW5hbHl0aWNzLFxuICAgICAgcmVzb3VyY2VGbG93cyxcbiAgICB9O1xuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgbWFwVG9SZWNvcmQ8VD4obWFwOiBNYXA8c3RyaW5nLCBUPik6IFJlY29yZDxzdHJpbmcsIFQ+IHtcbiAgICBjb25zdCByZWNvcmQ6IFJlY29yZDxzdHJpbmcsIFQ+ID0ge307XG4gICAgbWFwLmZvckVhY2goKHZhbHVlLCBrZXkpID0+IHtcbiAgICAgIHJlY29yZFtrZXldID0gdmFsdWU7XG4gICAgfSk7XG4gICAgcmV0dXJuIHJlY29yZDtcbiAgfVxuXG4gIHByaXZhdGUgc3RhdGljIHJlY29yZFRvTWFwPFQ+KHJlY29yZDogUmVjb3JkPHN0cmluZywgVD4pOiBNYXA8c3RyaW5nLCBUPiB7XG4gICAgcmV0dXJuIG5ldyBNYXAoT2JqZWN0LmVudHJpZXMocmVjb3JkKSk7XG4gIH1cblxuICBwdWJsaWMgc3RhdGljIGdlbmVyYXRlQUlQcm9tcHQoc2VyaWFsaXplZDogU2VyaWFsaXplZEdhbWVTdGF0ZSk6IHN0cmluZyB7XG4gICAgY29uc3QgcHJvbXB0ID0gYFxuIyBGYWN0b3J5IEJ1aWxkZXIgR2FtZSBTdGF0ZSBBbmFseXNpc1xuXG4jIyBDdXJyZW50IEZhY3RvcnkgU3RhdHVzXG4tICoqR3JpZCBTaXplKio6ICR7c2VyaWFsaXplZC5tZXRhZGF0YS5ncmlkU2l6ZS53aWR0aH14JHtzZXJpYWxpemVkLm1ldGFkYXRhLmdyaWRTaXplLmhlaWdodH1cbi0gKipHYW1lIFRpbWUqKjogJHtNYXRoLnJvdW5kKHNlcmlhbGl6ZWQubWV0YWRhdGEuZ2FtZVRpbWUgLyAxMDAwKX1zXG4tICoqU3RhdHVzKio6ICR7c2VyaWFsaXplZC5tZXRhZGF0YS5pc1J1bm5pbmcgPyAnUnVubmluZycgOiAnUGF1c2VkJ31cbi0gKipPdmVyYWxsIEVmZmljaWVuY3kqKjogJHsoc2VyaWFsaXplZC5zdGF0aXN0aWNzLmVmZmljaWVuY3kgKiAxMDApLnRvRml4ZWQoMSl9JVxuXG4jIyBDb21wb25lbnRzICgke3NlcmlhbGl6ZWQuY29tcG9uZW50cy5sZW5ndGh9IHRvdGFsKVxuJHtzZXJpYWxpemVkLmNvbXBvbmVudHMubWFwKGNvbXAgPT4gYFxuLSAqKiR7Y29tcC5tZXRhZGF0YS5uYW1lfSoqICgke2NvbXAuaWQuc2xpY2UoMCwgOCl9KVxuICAtIFBvc2l0aW9uOiAoJHtjb21wLnBvc2l0aW9uLnh9LCAke2NvbXAucG9zaXRpb24ueX0pXG4gIC0gRGlyZWN0aW9uOiAke1snTm9ydGgnLCAnRWFzdCcsICdTb3V0aCcsICdXZXN0J11bY29tcC5kaXJlY3Rpb25dfVxuICAtIEFjdGl2ZTogJHtjb21wLmlzQWN0aXZlID8gJ1llcycgOiAnTm8nfVxuICAtIFJlY2lwZTogJHtjb21wLnJlY2lwZSB8fCAnTm9uZSd9XG4gIC0gSW52ZW50b3J5OiAke09iamVjdC5lbnRyaWVzKGNvbXAuaW52ZW50b3J5KS5tYXAoKFtyLCBhXSkgPT4gYCR7cn06ICR7YX1gKS5qb2luKCcsICcpIHx8ICdFbXB0eSd9XG4gIC0gQ29ubmVjdGlvbnM6ICR7Y29tcC5jb25uZWN0aW9ucy5pbnB1dHMubGVuZ3RofSBpbnB1dHMsICR7Y29tcC5jb25uZWN0aW9ucy5vdXRwdXRzLmxlbmd0aH0gb3V0cHV0c1xuYCkuam9pbignJyl9XG5cbiMjIFJlc291cmNlc1xuJHtPYmplY3QuZW50cmllcyhzZXJpYWxpemVkLnJlc291cmNlcykubWFwKChbcmVzb3VyY2UsIGFtb3VudF0pID0+IGBcbi0gKioke3Jlc291cmNlLnJlcGxhY2UoJ18nLCAnICcpfSoqOiAke2Ftb3VudH1cbmApLmpvaW4oJycpfVxuXG4jIyBQcm9kdWN0aW9uIFN0YXRpc3RpY3NcbiR7T2JqZWN0LmVudHJpZXMoc2VyaWFsaXplZC5zdGF0aXN0aWNzLnRvdGFsUHJvZHVjdGlvbikubWFwKChbcmVzb3VyY2UsIGFtb3VudF0pID0+IGBcbi0gKioke3Jlc291cmNlLnJlcGxhY2UoJ18nLCAnICcpfSBQcm9kdWN0aW9uKio6ICR7YW1vdW50fS9taW5cbmApLmpvaW4oJycpfVxuXG4ke09iamVjdC5lbnRyaWVzKHNlcmlhbGl6ZWQuc3RhdGlzdGljcy50b3RhbENvbnN1bXB0aW9uKS5tYXAoKFtyZXNvdXJjZSwgYW1vdW50XSkgPT4gYFxuLSAqKiR7cmVzb3VyY2UucmVwbGFjZSgnXycsICcgJyl9IENvbnN1bXB0aW9uKio6ICR7YW1vdW50fS9taW5cbmApLmpvaW4oJycpfVxuXG4ke3NlcmlhbGl6ZWQuYW5hbHl0aWNzID8gYFxuIyMgUGVyZm9ybWFuY2UgQW5hbHl0aWNzXG4tICoqUGVyZm9ybWFuY2UgU2NvcmUqKjogJHtzZXJpYWxpemVkLmFuYWx5dGljcy5wZXJmb3JtYW5jZVNjb3JlfS8xMDBcbi0gKipUb3RhbCBUaHJvdWdocHV0Kio6ICR7c2VyaWFsaXplZC5hbmFseXRpY3MudG90YWxUaHJvdWdocHV0LnRvRml4ZWQoMSl9IGl0ZW1zL21pblxuLSAqKkJvdHRsZW5lY2tzKio6ICR7c2VyaWFsaXplZC5hbmFseXRpY3MuYm90dGxlbmVja3MubGVuZ3RofSBjb21wb25lbnRzXG5cbiMjIyBSZWNvbW1lbmRhdGlvbnNcbiR7c2VyaWFsaXplZC5hbmFseXRpY3MucmVjb21tZW5kYXRpb25zLm1hcChyZWMgPT4gYC0gJHtyZWN9YCkuam9pbignXFxuJyl9XG5cbiMjIyBDb21wb25lbnQgUGVyZm9ybWFuY2VcbiR7T2JqZWN0LmVudHJpZXMoc2VyaWFsaXplZC5hbmFseXRpY3MuY29tcG9uZW50QW5hbHl0aWNzKS5tYXAoKFtpZCwgYW5hbHl0aWNzXSkgPT4gYFxuLSAqKiR7aWQuc2xpY2UoMCwgOCl9Kio6ICR7KGFuYWx5dGljcy51dGlsaXphdGlvbiAqIDEwMCkudG9GaXhlZCgxKX0lIHV0aWxpemF0aW9uLCAke2FuYWx5dGljcy50aHJvdWdocHV0LnRvRml4ZWQoMSl9IGl0ZW1zL21pblxuYCkuam9pbignJyl9XG5gIDogJyd9XG5cbiMjIEdhbWUgTWVjaGFuaWNzIFJlZmVyZW5jZVxuXG4jIyMgQ29tcG9uZW50IFR5cGVzXG4tICoqQ29udmV5b3IgQmVsdCoqOiBUcmFuc3BvcnRzIGl0ZW1zICgxNSBpdGVtcy9zZWMpXG4tICoqTWluaW5nIERyaWxsKio6IEV4dHJhY3RzIHJlc291cmNlcyAoMC41IGl0ZW1zL3NlYylcbi0gKipBc3NlbWJsaW5nIE1hY2hpbmUqKjogQ3JhZnRzIGl0ZW1zICgwLjc1eCBzcGVlZCBtdWx0aXBsaWVyKVxuLSAqKlN0b3JhZ2UgQ2hlc3QqKjogU3RvcmVzIGl0ZW1zICgzMCBpdGVtcy9zZWMgdGhyb3VnaHB1dClcbi0gKipTcGxpdHRlcioqOiBEaXZpZGVzIGlucHV0IGludG8gMiBvdXRwdXRzICgxNSBpdGVtcy9zZWMpXG4tICoqTWVyZ2VyKio6IENvbWJpbmVzIDIgaW5wdXRzIGludG8gMSBvdXRwdXQgKDE1IGl0ZW1zL3NlYylcblxuIyMjIEF2YWlsYWJsZSBSZWNpcGVzXG4tICoqSXJvbiBQbGF0ZSoqOiAxIElyb24gT3JlIOKGkiAxIElyb24gUGxhdGUgKDMuMnMpXG4tICoqQ29wcGVyIFBsYXRlKio6IDEgQ29wcGVyIE9yZSDihpIgMSBDb3BwZXIgUGxhdGUgKDMuMnMpXG4tICoqSXJvbiBHZWFyKio6IDIgSXJvbiBQbGF0ZXMg4oaSIDEgR2VhciAoMC41cylcbi0gKipFbGVjdHJvbmljIENpcmN1aXQqKjogMSBJcm9uIFBsYXRlICsgMyBDb3BwZXIgUGxhdGVzIOKGkiAxIENpcmN1aXQgKDAuNXMpXG5cbiMjIyBSZXNvdXJjZSBUeXBlc1xuLSAqKlJhdyBNYXRlcmlhbHMqKjogSXJvbiBPcmUsIENvcHBlciBPcmUsIENvYWxcbi0gKipJbnRlcm1lZGlhdGUgUHJvZHVjdHMqKjogSXJvbiBQbGF0ZSwgQ29wcGVyIFBsYXRlXG4tICoqQWR2YW5jZWQgUHJvZHVjdHMqKjogR2VhciwgRWxlY3Ryb25pYyBDaXJjdWl0XG5cblBsZWFzZSBhbmFseXplIHRoaXMgZmFjdG9yeSBhbmQgcHJvdmlkZSBzdWdnZXN0aW9ucyBmb3Igb3B0aW1pemF0aW9uLCBlZmZpY2llbmN5IGltcHJvdmVtZW50cywgb3IgcHJvYmxlbSBpZGVudGlmaWNhdGlvbi5cbmA7XG5cbiAgICByZXR1cm4gcHJvbXB0LnRyaW0oKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIkNPTVBPTkVOVF9ERUZJTklUSU9OUyIsIlJFQ0lQRVMiLCJHYW1lU3RhdGVTZXJpYWxpemVyIiwic2VyaWFsaXplIiwiZ2FtZVN0YXRlIiwiYW5hbHl0aWNzIiwic2VyaWFsaXplZCIsIm1ldGFkYXRhIiwidmVyc2lvbiIsInRpbWVzdGFtcCIsIkRhdGUiLCJub3ciLCJncmlkU2l6ZSIsImdhbWVUaW1lIiwiaXNSdW5uaW5nIiwiY29tcG9uZW50cyIsInNlcmlhbGl6ZUNvbXBvbmVudHMiLCJyZXNvdXJjZXMiLCJzZXJpYWxpemVSZXNvdXJjZXMiLCJzdGF0aXN0aWNzIiwidG90YWxQcm9kdWN0aW9uIiwibWFwVG9SZWNvcmQiLCJ0b3RhbENvbnN1bXB0aW9uIiwiZWZmaWNpZW5jeSIsImJvdHRsZW5lY2tzIiwic2VyaWFsaXplQW5hbHl0aWNzIiwiZGVzZXJpYWxpemUiLCJNYXAiLCJjb21wIiwiY29tcG9uZW50IiwiaWQiLCJ0eXBlIiwicG9zaXRpb24iLCJkaXJlY3Rpb24iLCJyZWNpcGUiLCJ1bmRlZmluZWQiLCJpbnZlbnRvcnkiLCJPYmplY3QiLCJlbnRyaWVzIiwibWFwIiwiayIsInYiLCJjb25uZWN0aW9ucyIsImlzQWN0aXZlIiwibGFzdFByb2Nlc3NUaW1lIiwic2V0IiwicmVjb3JkVG9NYXAiLCJBcnJheSIsImZyb20iLCJ2YWx1ZXMiLCJkZWZpbml0aW9uIiwibmFtZSIsImRlc2NyaXB0aW9uIiwibWF4SW5wdXRzIiwibWF4T3V0cHV0cyIsInNwZWVkIiwic2l6ZSIsImNvbXBvbmVudEFuYWx5dGljcyIsImZvckVhY2giLCJ2YWx1ZSIsImtleSIsInV0aWxpemF0aW9uIiwidGhyb3VnaHB1dCIsImJvdHRsZW5lY2tTY29yZSIsImlucHV0U3RhcnZhdGlvbiIsIm91dHB1dEJsb2NrZWQiLCJyZXNvdXJjZUZsb3dzIiwibmV0RmxvdyIsImZsb3dFZmZpY2llbmN5IiwiYm90dGxlbmVja0NvbXBvbmVudHMiLCJvdmVyYWxsRWZmaWNpZW5jeSIsInRvdGFsVGhyb3VnaHB1dCIsInBlcmZvcm1hbmNlU2NvcmUiLCJyZWNvbW1lbmRhdGlvbnMiLCJyZWNvcmQiLCJnZW5lcmF0ZUFJUHJvbXB0IiwicHJvbXB0Iiwid2lkdGgiLCJoZWlnaHQiLCJNYXRoIiwicm91bmQiLCJ0b0ZpeGVkIiwibGVuZ3RoIiwic2xpY2UiLCJ4IiwieSIsInIiLCJhIiwiam9pbiIsImlucHV0cyIsIm91dHB1dHMiLCJyZXNvdXJjZSIsImFtb3VudCIsInJlcGxhY2UiLCJyZWMiLCJ0cmltIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/ai/gameStateSerializer.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/ai/analyze/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/ai/analyze/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _ai_gameStateSerializer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/ai/gameStateSerializer */ \"(rsc)/./src/ai/gameStateSerializer.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { gameState, analytics } = body;\n        if (!gameState) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Game state is required'\n            }, {\n                status: 400\n            });\n        }\n        // Serialize the game state for AI analysis\n        const serialized = _ai_gameStateSerializer__WEBPACK_IMPORTED_MODULE_1__.GameStateSerializer.serialize(gameState, analytics);\n        // Generate AI prompt\n        const prompt = _ai_gameStateSerializer__WEBPACK_IMPORTED_MODULE_1__.GameStateSerializer.generateAIPrompt(serialized);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                serializedState: serialized,\n                aiPrompt: prompt,\n                metadata: {\n                    componentCount: serialized.components.length,\n                    efficiency: serialized.statistics.efficiency,\n                    isRunning: serialized.metadata.isRunning,\n                    timestamp: serialized.metadata.timestamp\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Error analyzing game state:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to analyze game state'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Factory Builder AI Analysis API',\n        version: '1.0.0',\n        endpoints: {\n            analyze: {\n                method: 'POST',\n                description: 'Analyze factory state and generate AI prompt',\n                parameters: {\n                    gameState: 'Required. The current game state object',\n                    analytics: 'Optional. Factory analytics data'\n                }\n            },\n            import: {\n                method: 'POST',\n                description: 'Import and validate serialized game state',\n                parameters: {\n                    serializedState: 'Required. Serialized game state JSON'\n                }\n            }\n        },\n        gameInfo: {\n            componentTypes: [\n                'conveyor - Transport items between components',\n                'miner - Extract raw resources',\n                'assembler - Craft items from materials',\n                'storage - Store items',\n                'splitter - Split input into multiple outputs',\n                'merger - Merge multiple inputs into one output'\n            ],\n            resourceTypes: [\n                'iron_ore - Raw iron ore',\n                'copper_ore - Raw copper ore',\n                'coal - Raw coal',\n                'iron_plate - Processed iron',\n                'copper_plate - Processed copper',\n                'gear - Iron gear wheel',\n                'circuit - Electronic circuit'\n            ],\n            recipes: [\n                'iron_plate: 1 iron_ore → 1 iron_plate (3.2s)',\n                'copper_plate: 1 copper_ore → 1 copper_plate (3.2s)',\n                'gear: 2 iron_plate → 1 gear (0.5s)',\n                'circuit: 1 iron_plate + 3 copper_plate → 1 circuit (0.5s)'\n            ]\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9haS9hbmFseXplL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBd0Q7QUFDNEI7QUFFN0UsZUFBZUUsS0FBS0MsT0FBb0I7SUFDN0MsSUFBSTtRQUNGLE1BQU1DLE9BQU8sTUFBTUQsUUFBUUUsSUFBSTtRQUMvQixNQUFNLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFLEdBQUdIO1FBRWpDLElBQUksQ0FBQ0UsV0FBVztZQUNkLE9BQU9OLHFEQUFZQSxDQUFDSyxJQUFJLENBQ3RCO2dCQUFFRyxPQUFPO1lBQXlCLEdBQ2xDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSwyQ0FBMkM7UUFDM0MsTUFBTUMsYUFBYVQsd0VBQW1CQSxDQUFDVSxTQUFTLENBQUNMLFdBQVdDO1FBRTVELHFCQUFxQjtRQUNyQixNQUFNSyxTQUFTWCx3RUFBbUJBLENBQUNZLGdCQUFnQixDQUFDSDtRQUVwRCxPQUFPVixxREFBWUEsQ0FBQ0ssSUFBSSxDQUFDO1lBQ3ZCUyxTQUFTO1lBQ1RDLE1BQU07Z0JBQ0pDLGlCQUFpQk47Z0JBQ2pCTyxVQUFVTDtnQkFDVk0sVUFBVTtvQkFDUkMsZ0JBQWdCVCxXQUFXVSxVQUFVLENBQUNDLE1BQU07b0JBQzVDQyxZQUFZWixXQUFXYSxVQUFVLENBQUNELFVBQVU7b0JBQzVDRSxXQUFXZCxXQUFXUSxRQUFRLENBQUNNLFNBQVM7b0JBQ3hDQyxXQUFXZixXQUFXUSxRQUFRLENBQUNPLFNBQVM7Z0JBQzFDO1lBQ0Y7UUFDRjtJQUNGLEVBQUUsT0FBT2pCLE9BQU87UUFDZGtCLFFBQVFsQixLQUFLLENBQUMsK0JBQStCQTtRQUM3QyxPQUFPUixxREFBWUEsQ0FBQ0ssSUFBSSxDQUN0QjtZQUFFRyxPQUFPO1FBQStCLEdBQ3hDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRU8sZUFBZWtCO0lBQ3BCLE9BQU8zQixxREFBWUEsQ0FBQ0ssSUFBSSxDQUFDO1FBQ3ZCdUIsU0FBUztRQUNUQyxTQUFTO1FBQ1RDLFdBQVc7WUFDVEMsU0FBUztnQkFDUEMsUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtvQkFDVjVCLFdBQVc7b0JBQ1hDLFdBQVc7Z0JBQ2I7WUFDRjtZQUNBNEIsUUFBUTtnQkFDTkgsUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtvQkFDVmxCLGlCQUFpQjtnQkFDbkI7WUFDRjtRQUNGO1FBQ0FvQixVQUFVO1lBQ1JDLGdCQUFnQjtnQkFDZDtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLGVBQWU7Z0JBQ2I7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxTQUFTO2dCQUNQO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7UUFDSDtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcc3JjXFxhcHBcXGFwaVxcYWlcXGFuYWx5emVcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBHYW1lU3RhdGVTZXJpYWxpemVyLCBTZXJpYWxpemVkR2FtZVN0YXRlIH0gZnJvbSAnQC9haS9nYW1lU3RhdGVTZXJpYWxpemVyJztcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG4gICAgY29uc3QgeyBnYW1lU3RhdGUsIGFuYWx5dGljcyB9ID0gYm9keTtcblxuICAgIGlmICghZ2FtZVN0YXRlKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdHYW1lIHN0YXRlIGlzIHJlcXVpcmVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gU2VyaWFsaXplIHRoZSBnYW1lIHN0YXRlIGZvciBBSSBhbmFseXNpc1xuICAgIGNvbnN0IHNlcmlhbGl6ZWQgPSBHYW1lU3RhdGVTZXJpYWxpemVyLnNlcmlhbGl6ZShnYW1lU3RhdGUsIGFuYWx5dGljcyk7XG4gICAgXG4gICAgLy8gR2VuZXJhdGUgQUkgcHJvbXB0XG4gICAgY29uc3QgcHJvbXB0ID0gR2FtZVN0YXRlU2VyaWFsaXplci5nZW5lcmF0ZUFJUHJvbXB0KHNlcmlhbGl6ZWQpO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBkYXRhOiB7XG4gICAgICAgIHNlcmlhbGl6ZWRTdGF0ZTogc2VyaWFsaXplZCxcbiAgICAgICAgYWlQcm9tcHQ6IHByb21wdCxcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICAgICAgICBjb21wb25lbnRDb3VudDogc2VyaWFsaXplZC5jb21wb25lbnRzLmxlbmd0aCxcbiAgICAgICAgICBlZmZpY2llbmN5OiBzZXJpYWxpemVkLnN0YXRpc3RpY3MuZWZmaWNpZW5jeSxcbiAgICAgICAgICBpc1J1bm5pbmc6IHNlcmlhbGl6ZWQubWV0YWRhdGEuaXNSdW5uaW5nLFxuICAgICAgICAgIHRpbWVzdGFtcDogc2VyaWFsaXplZC5tZXRhZGF0YS50aW1lc3RhbXAsXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFuYWx5emluZyBnYW1lIHN0YXRlOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGFuYWx5emUgZ2FtZSBzdGF0ZScgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVCgpIHtcbiAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICBtZXNzYWdlOiAnRmFjdG9yeSBCdWlsZGVyIEFJIEFuYWx5c2lzIEFQSScsXG4gICAgdmVyc2lvbjogJzEuMC4wJyxcbiAgICBlbmRwb2ludHM6IHtcbiAgICAgIGFuYWx5emU6IHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQW5hbHl6ZSBmYWN0b3J5IHN0YXRlIGFuZCBnZW5lcmF0ZSBBSSBwcm9tcHQnLFxuICAgICAgICBwYXJhbWV0ZXJzOiB7XG4gICAgICAgICAgZ2FtZVN0YXRlOiAnUmVxdWlyZWQuIFRoZSBjdXJyZW50IGdhbWUgc3RhdGUgb2JqZWN0JyxcbiAgICAgICAgICBhbmFseXRpY3M6ICdPcHRpb25hbC4gRmFjdG9yeSBhbmFseXRpY3MgZGF0YScsXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgICAgaW1wb3J0OiB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0ltcG9ydCBhbmQgdmFsaWRhdGUgc2VyaWFsaXplZCBnYW1lIHN0YXRlJyxcbiAgICAgICAgcGFyYW1ldGVyczoge1xuICAgICAgICAgIHNlcmlhbGl6ZWRTdGF0ZTogJ1JlcXVpcmVkLiBTZXJpYWxpemVkIGdhbWUgc3RhdGUgSlNPTicsXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0sXG4gICAgZ2FtZUluZm86IHtcbiAgICAgIGNvbXBvbmVudFR5cGVzOiBbXG4gICAgICAgICdjb252ZXlvciAtIFRyYW5zcG9ydCBpdGVtcyBiZXR3ZWVuIGNvbXBvbmVudHMnLFxuICAgICAgICAnbWluZXIgLSBFeHRyYWN0IHJhdyByZXNvdXJjZXMnLFxuICAgICAgICAnYXNzZW1ibGVyIC0gQ3JhZnQgaXRlbXMgZnJvbSBtYXRlcmlhbHMnLFxuICAgICAgICAnc3RvcmFnZSAtIFN0b3JlIGl0ZW1zJyxcbiAgICAgICAgJ3NwbGl0dGVyIC0gU3BsaXQgaW5wdXQgaW50byBtdWx0aXBsZSBvdXRwdXRzJyxcbiAgICAgICAgJ21lcmdlciAtIE1lcmdlIG11bHRpcGxlIGlucHV0cyBpbnRvIG9uZSBvdXRwdXQnLFxuICAgICAgXSxcbiAgICAgIHJlc291cmNlVHlwZXM6IFtcbiAgICAgICAgJ2lyb25fb3JlIC0gUmF3IGlyb24gb3JlJyxcbiAgICAgICAgJ2NvcHBlcl9vcmUgLSBSYXcgY29wcGVyIG9yZScsXG4gICAgICAgICdjb2FsIC0gUmF3IGNvYWwnLFxuICAgICAgICAnaXJvbl9wbGF0ZSAtIFByb2Nlc3NlZCBpcm9uJyxcbiAgICAgICAgJ2NvcHBlcl9wbGF0ZSAtIFByb2Nlc3NlZCBjb3BwZXInLFxuICAgICAgICAnZ2VhciAtIElyb24gZ2VhciB3aGVlbCcsXG4gICAgICAgICdjaXJjdWl0IC0gRWxlY3Ryb25pYyBjaXJjdWl0JyxcbiAgICAgIF0sXG4gICAgICByZWNpcGVzOiBbXG4gICAgICAgICdpcm9uX3BsYXRlOiAxIGlyb25fb3JlIOKGkiAxIGlyb25fcGxhdGUgKDMuMnMpJyxcbiAgICAgICAgJ2NvcHBlcl9wbGF0ZTogMSBjb3BwZXJfb3JlIOKGkiAxIGNvcHBlcl9wbGF0ZSAoMy4ycyknLFxuICAgICAgICAnZ2VhcjogMiBpcm9uX3BsYXRlIOKGkiAxIGdlYXIgKDAuNXMpJyxcbiAgICAgICAgJ2NpcmN1aXQ6IDEgaXJvbl9wbGF0ZSArIDMgY29wcGVyX3BsYXRlIOKGkiAxIGNpcmN1aXQgKDAuNXMpJyxcbiAgICAgIF0sXG4gICAgfSxcbiAgfSk7XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiR2FtZVN0YXRlU2VyaWFsaXplciIsIlBPU1QiLCJyZXF1ZXN0IiwiYm9keSIsImpzb24iLCJnYW1lU3RhdGUiLCJhbmFseXRpY3MiLCJlcnJvciIsInN0YXR1cyIsInNlcmlhbGl6ZWQiLCJzZXJpYWxpemUiLCJwcm9tcHQiLCJnZW5lcmF0ZUFJUHJvbXB0Iiwic3VjY2VzcyIsImRhdGEiLCJzZXJpYWxpemVkU3RhdGUiLCJhaVByb21wdCIsIm1ldGFkYXRhIiwiY29tcG9uZW50Q291bnQiLCJjb21wb25lbnRzIiwibGVuZ3RoIiwiZWZmaWNpZW5jeSIsInN0YXRpc3RpY3MiLCJpc1J1bm5pbmciLCJ0aW1lc3RhbXAiLCJjb25zb2xlIiwiR0VUIiwibWVzc2FnZSIsInZlcnNpb24iLCJlbmRwb2ludHMiLCJhbmFseXplIiwibWV0aG9kIiwiZGVzY3JpcHRpb24iLCJwYXJhbWV0ZXJzIiwiaW1wb3J0IiwiZ2FtZUluZm8iLCJjb21wb25lbnRUeXBlcyIsInJlc291cmNlVHlwZXMiLCJyZWNpcGVzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/analyze/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/game.ts":
/*!***************************!*\
  !*** ./src/types/game.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMPONENT_DEFINITIONS: () => (/* binding */ COMPONENT_DEFINITIONS),\n/* harmony export */   ComponentType: () => (/* binding */ ComponentType),\n/* harmony export */   Direction: () => (/* binding */ Direction),\n/* harmony export */   RECIPES: () => (/* binding */ RECIPES),\n/* harmony export */   ResourceType: () => (/* binding */ ResourceType)\n/* harmony export */ });\n// Core game types and interfaces\nvar ComponentType = /*#__PURE__*/ function(ComponentType) {\n    ComponentType[\"CONVEYOR\"] = \"conveyor\";\n    ComponentType[\"MINER\"] = \"miner\";\n    ComponentType[\"ASSEMBLER\"] = \"assembler\";\n    ComponentType[\"STORAGE\"] = \"storage\";\n    ComponentType[\"SPLITTER\"] = \"splitter\";\n    ComponentType[\"MERGER\"] = \"merger\";\n    return ComponentType;\n}({});\nvar ResourceType = /*#__PURE__*/ function(ResourceType) {\n    ResourceType[\"IRON_ORE\"] = \"iron_ore\";\n    ResourceType[\"COPPER_ORE\"] = \"copper_ore\";\n    ResourceType[\"COAL\"] = \"coal\";\n    ResourceType[\"IRON_PLATE\"] = \"iron_plate\";\n    ResourceType[\"COPPER_PLATE\"] = \"copper_plate\";\n    ResourceType[\"GEAR\"] = \"gear\";\n    ResourceType[\"CIRCUIT\"] = \"circuit\";\n    return ResourceType;\n}({});\nvar Direction = /*#__PURE__*/ function(Direction) {\n    Direction[Direction[\"NORTH\"] = 0] = \"NORTH\";\n    Direction[Direction[\"EAST\"] = 1] = \"EAST\";\n    Direction[Direction[\"SOUTH\"] = 2] = \"SOUTH\";\n    Direction[Direction[\"WEST\"] = 3] = \"WEST\";\n    return Direction;\n}({});\n// Component definitions for the game\nconst COMPONENT_DEFINITIONS = {\n    [\"conveyor\"]: {\n        type: \"conveyor\",\n        name: 'Conveyor Belt',\n        description: 'Transports items between components',\n        size: {\n            width: 1,\n            height: 1\n        },\n        maxInputs: 1,\n        maxOutputs: 1,\n        speed: 15,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 1\n            }\n        ]\n    },\n    [\"miner\"]: {\n        type: \"miner\",\n        name: 'Mining Drill',\n        description: 'Extracts raw resources from the ground',\n        size: {\n            width: 2,\n            height: 2\n        },\n        maxInputs: 0,\n        maxOutputs: 1,\n        speed: 0.5,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 10\n            },\n            {\n                resource: \"gear\",\n                amount: 5\n            }\n        ]\n    },\n    [\"assembler\"]: {\n        type: \"assembler\",\n        name: 'Assembling Machine',\n        description: 'Crafts items from raw materials',\n        size: {\n            width: 3,\n            height: 3\n        },\n        maxInputs: 2,\n        maxOutputs: 1,\n        speed: 0.75,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 9\n            },\n            {\n                resource: \"gear\",\n                amount: 5\n            },\n            {\n                resource: \"circuit\",\n                amount: 3\n            }\n        ]\n    },\n    [\"storage\"]: {\n        type: \"storage\",\n        name: 'Storage Chest',\n        description: 'Stores items for later use',\n        size: {\n            width: 1,\n            height: 1\n        },\n        maxInputs: 1,\n        maxOutputs: 1,\n        speed: 30,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 8\n            }\n        ]\n    },\n    [\"splitter\"]: {\n        type: \"splitter\",\n        name: 'Splitter',\n        description: 'Splits input into multiple outputs',\n        size: {\n            width: 2,\n            height: 1\n        },\n        maxInputs: 1,\n        maxOutputs: 2,\n        speed: 15,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 5\n            },\n            {\n                resource: \"circuit\",\n                amount: 5\n            }\n        ]\n    },\n    [\"merger\"]: {\n        type: \"merger\",\n        name: 'Merger',\n        description: 'Merges multiple inputs into one output',\n        size: {\n            width: 2,\n            height: 1\n        },\n        maxInputs: 2,\n        maxOutputs: 1,\n        speed: 15,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 5\n            },\n            {\n                resource: \"circuit\",\n                amount: 5\n            }\n        ]\n    }\n};\n// Recipe definitions\nconst RECIPES = {\n    iron_plate: {\n        id: 'iron_plate',\n        name: 'Iron Plate',\n        inputs: [\n            {\n                resource: \"iron_ore\",\n                amount: 1\n            }\n        ],\n        outputs: [\n            {\n                resource: \"iron_plate\",\n                amount: 1\n            }\n        ],\n        processingTime: 3200\n    },\n    copper_plate: {\n        id: 'copper_plate',\n        name: 'Copper Plate',\n        inputs: [\n            {\n                resource: \"copper_ore\",\n                amount: 1\n            }\n        ],\n        outputs: [\n            {\n                resource: \"copper_plate\",\n                amount: 1\n            }\n        ],\n        processingTime: 3200\n    },\n    gear: {\n        id: 'gear',\n        name: 'Iron Gear Wheel',\n        inputs: [\n            {\n                resource: \"iron_plate\",\n                amount: 2\n            }\n        ],\n        outputs: [\n            {\n                resource: \"gear\",\n                amount: 1\n            }\n        ],\n        processingTime: 500\n    },\n    circuit: {\n        id: 'circuit',\n        name: 'Electronic Circuit',\n        inputs: [\n            {\n                resource: \"iron_plate\",\n                amount: 1\n            },\n            {\n                resource: \"copper_plate\",\n                amount: 3\n            }\n        ],\n        outputs: [\n            {\n                resource: \"circuit\",\n                amount: 1\n            }\n        ],\n        processingTime: 500\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/game.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fanalyze%2Froute&page=%2Fapi%2Fai%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fanalyze%2Froute.ts&appDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();