"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/gameStore.ts":
/*!********************************!*\
  !*** ./src/store/gameStore.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGameStore: () => (/* binding */ useGameStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _types_game__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/game */ \"(app-pages-browser)/./src/types/game.ts\");\n/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/helpers */ \"(app-pages-browser)/./src/utils/helpers.ts\");\n/* harmony import */ var _engine_simulation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/engine/simulation */ \"(app-pages-browser)/./src/engine/simulation.ts\");\n/* harmony import */ var _analytics_performanceAnalyzer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/analytics/performanceAnalyzer */ \"(app-pages-browser)/./src/analytics/performanceAnalyzer.ts\");\n\n\n\n\n\n\nconst GRID_SIZE = {\n    width: 50,\n    height: 50\n};\nconst simulationEngine = new _engine_simulation__WEBPACK_IMPORTED_MODULE_2__.SimulationEngine();\nconst performanceAnalyzer = new _analytics_performanceAnalyzer__WEBPACK_IMPORTED_MODULE_3__.PerformanceAnalyzer();\nconst initialState = {\n    components: new Map(),\n    gridSize: GRID_SIZE,\n    isRunning: false,\n    gameTime: 0,\n    resources: new Map([\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.IRON_ORE,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COPPER_ORE,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COAL,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.IRON_PLATE,\n            100\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COPPER_PLATE,\n            100\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.GEAR,\n            50\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.CIRCUIT,\n            50\n        ]\n    ]),\n    statistics: {\n        totalProduction: new Map(),\n        totalConsumption: new Map(),\n        efficiency: 0,\n        bottlenecks: []\n    }\n};\nconst initialStoreState = {\n    ...initialState,\n    factoryAnalytics: null\n};\nconst useGameStore = (0,zustand__WEBPACK_IMPORTED_MODULE_4__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_5__.subscribeWithSelector)((set, get)=>({\n        ...initialStoreState,\n        addComponent: function(type, position) {\n            let direction = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.NORTH;\n            const state = get();\n            const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[type];\n            // Check if position is valid and not occupied\n            if (!isPositionValid(position, definition.size, state)) {\n                return null;\n            }\n            const id = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_1__.generateId)();\n            const component = {\n                id,\n                type,\n                position,\n                direction,\n                inventory: new Map(),\n                connections: {\n                    inputs: [],\n                    outputs: []\n                },\n                isActive: false,\n                lastProcessTime: 0\n            };\n            set((state)=>{\n                const newComponents = new Map(state.components).set(id, component);\n                // Auto-connect to adjacent components\n                autoConnectComponent(id, component, newComponents);\n                return {\n                    components: newComponents\n                };\n            });\n            return id;\n        },\n        removeComponent: (id)=>{\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const component = newComponents.get(id);\n                if (component) {\n                    // Remove all connections to this component\n                    newComponents.forEach((comp)=>{\n                        comp.connections.inputs = comp.connections.inputs.filter((connId)=>connId !== id);\n                        comp.connections.outputs = comp.connections.outputs.filter((connId)=>connId !== id);\n                    });\n                    newComponents.delete(id);\n                }\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        moveComponent: (id, position)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n                if (!isPositionValid(position, definition.size, state, id)) {\n                    return state;\n                }\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    position\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        rotateComponent: (id)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const newDirection = (component.direction + 1) % 4;\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    direction: newDirection\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        connectComponents: (fromId, toId)=>{\n            const state = get();\n            const fromComponent = state.components.get(fromId);\n            const toComponent = state.components.get(toId);\n            if (!fromComponent || !toComponent || fromId === toId) {\n                return false;\n            }\n            const fromDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[fromComponent.type];\n            const toDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[toComponent.type];\n            // Check connection limits\n            if (fromComponent.connections.outputs.length >= fromDef.maxOutputs || toComponent.connections.inputs.length >= toDef.maxInputs) {\n                return false;\n            }\n            // Check if already connected\n            if (fromComponent.connections.outputs.includes(toId)) {\n                return false;\n            }\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const newFromComponent = {\n                    ...fromComponent\n                };\n                const newToComponent = {\n                    ...toComponent\n                };\n                newFromComponent.connections.outputs.push(toId);\n                newToComponent.connections.inputs.push(fromId);\n                newComponents.set(fromId, newFromComponent);\n                newComponents.set(toId, newToComponent);\n                return {\n                    components: newComponents\n                };\n            });\n            return true;\n        },\n        disconnectComponents: (fromId, toId)=>{\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const fromComponent = newComponents.get(fromId);\n                const toComponent = newComponents.get(toId);\n                if (fromComponent && toComponent) {\n                    fromComponent.connections.outputs = fromComponent.connections.outputs.filter((id)=>id !== toId);\n                    toComponent.connections.inputs = toComponent.connections.inputs.filter((id)=>id !== fromId);\n                }\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        setComponentRecipe: (id, recipeId)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const recipe = _types_game__WEBPACK_IMPORTED_MODULE_0__.RECIPES[recipeId];\n                if (!recipe) return state;\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    recipe\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        toggleSimulation: ()=>{\n            set((state)=>({\n                    isRunning: !state.isRunning\n                }));\n        },\n        updateSimulation: ()=>{\n            const state = get();\n            if (!state.isRunning) return;\n            const updates = simulationEngine.updateSimulation(state);\n            if (Object.keys(updates).length > 0) {\n                const newState = {\n                    ...state,\n                    ...updates\n                };\n                const analytics = performanceAnalyzer.analyzeFactory(newState);\n                set((currentState)=>({\n                        ...currentState,\n                        ...updates,\n                        factoryAnalytics: analytics\n                    }));\n            }\n        },\n        getPerformanceMetrics: ()=>{\n            const state = get();\n            return {\n                throughput: state.statistics.totalProduction,\n                utilization: new Map(),\n                bottlenecks: state.statistics.bottlenecks,\n                efficiency: state.statistics.efficiency\n            };\n        },\n        getFactoryAnalytics: ()=>{\n            const state = get();\n            if (state.factoryAnalytics) {\n                return state.factoryAnalytics;\n            }\n            // Generate analytics on demand if not available\n            return performanceAnalyzer.analyzeFactory(state);\n        },\n        getHistoricalData: (key)=>{\n            return performanceAnalyzer.getHistoricalData(key);\n        },\n        exportGameState: ()=>{\n            const state = get();\n            const exportData = {\n                components: Array.from(state.components.entries()),\n                gridSize: state.gridSize,\n                gameTime: state.gameTime,\n                resources: Array.from(state.resources.entries())\n            };\n            return JSON.stringify(exportData, null, 2);\n        },\n        importGameState: (jsonState)=>{\n            try {\n                const data = JSON.parse(jsonState);\n                set({\n                    components: new Map(data.components),\n                    gridSize: data.gridSize || GRID_SIZE,\n                    gameTime: data.gameTime || 0,\n                    resources: new Map(data.resources),\n                    isRunning: false\n                });\n                return true;\n            } catch (error) {\n                console.error('Failed to import game state:', error);\n                return false;\n            }\n        },\n        resetGame: ()=>{\n            set(initialState);\n        }\n    })));\n// Helper functions\nfunction isPositionValid(position, size, state, excludeId) {\n    // Check bounds\n    if (position.x < 0 || position.y < 0 || position.x + size.width > state.gridSize.width || position.y + size.height > state.gridSize.height) {\n        return false;\n    }\n    // Check for overlaps with existing components\n    for (const [id, component] of state.components){\n        if (excludeId && id === excludeId) continue;\n        const compDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n        const compPos = component.position;\n        // Check if rectangles overlap\n        if (!(position.x >= compPos.x + compDef.size.width || position.x + size.width <= compPos.x || position.y >= compPos.y + compDef.size.height || position.y + size.height <= compPos.y)) {\n            return false;\n        }\n    }\n    return true;\n}\n// Auto-connect components based on adjacency and direction\nfunction autoConnectComponent(newId, newComponent, components) {\n    const newDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[newComponent.type];\n    // Find adjacent components\n    for (const [existingId, existingComponent] of components){\n        if (existingId === newId) continue;\n        const existingDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[existingComponent.type];\n        // Check if components are adjacent and can connect\n        if (areComponentsAdjacent(newComponent, existingComponent)) {\n            const connectionDirection = getConnectionDirection(newComponent, existingComponent);\n            // Determine which component should be input/output based on direction and type\n            const shouldConnect = shouldAutoConnect(newComponent, newDef, existingComponent, existingDef, connectionDirection);\n            if (shouldConnect.connect) {\n                if (shouldConnect.newIsOutput) {\n                    // New component outputs to existing component\n                    if (newComponent.connections.outputs.length < newDef.maxOutputs && existingComponent.connections.inputs.length < existingDef.maxInputs) {\n                        newComponent.connections.outputs.push(existingId);\n                        existingComponent.connections.inputs.push(newId);\n                    }\n                } else {\n                    // Existing component outputs to new component\n                    if (existingComponent.connections.outputs.length < existingDef.maxOutputs && newComponent.connections.inputs.length < newDef.maxInputs) {\n                        existingComponent.connections.outputs.push(newId);\n                        newComponent.connections.inputs.push(existingId);\n                    }\n                }\n            }\n        }\n    }\n}\n// Check if two components are adjacent (within 1 grid unit)\nfunction areComponentsAdjacent(comp1, comp2) {\n    const def1 = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[comp1.type];\n    const def2 = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[comp2.type];\n    // Calculate component bounds\n    const comp1Bounds = {\n        left: comp1.position.x,\n        right: comp1.position.x + def1.size.width,\n        top: comp1.position.y,\n        bottom: comp1.position.y + def1.size.height\n    };\n    const comp2Bounds = {\n        left: comp2.position.x,\n        right: comp2.position.x + def2.size.width,\n        top: comp2.position.y,\n        bottom: comp2.position.y + def2.size.height\n    };\n    // Check if they're adjacent (touching but not overlapping)\n    const horizontallyAdjacent = (comp1Bounds.right === comp2Bounds.left || comp2Bounds.right === comp1Bounds.left) && !(comp1Bounds.bottom <= comp2Bounds.top || comp2Bounds.bottom <= comp1Bounds.top);\n    const verticallyAdjacent = (comp1Bounds.bottom === comp2Bounds.top || comp2Bounds.bottom === comp1Bounds.top) && !(comp1Bounds.right <= comp2Bounds.left || comp2Bounds.right <= comp1Bounds.left);\n    return horizontallyAdjacent || verticallyAdjacent;\n}\n// Get the direction from comp1 to comp2\nfunction getConnectionDirection(comp1, comp2) {\n    const dx = comp2.position.x - comp1.position.x;\n    const dy = comp2.position.y - comp1.position.y;\n    if (Math.abs(dx) > Math.abs(dy)) {\n        return dx > 0 ? _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.EAST : _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.WEST;\n    } else {\n        return dy > 0 ? _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.SOUTH : _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.NORTH;\n    }\n}\n// Determine if components should auto-connect and in which direction\nfunction shouldAutoConnect(comp1, def1, comp2, def2, direction) {\n    // Miners always output\n    if (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MINER && def2.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: true\n        };\n    }\n    if (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MINER && def1.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: false\n        };\n    }\n    // Assemblers prefer to output to storage or conveyors\n    if (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.ASSEMBLER && (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.STORAGE || comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR)) {\n        return {\n            connect: true,\n            newIsOutput: true\n        };\n    }\n    if (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.ASSEMBLER && (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.STORAGE || comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR)) {\n        return {\n            connect: true,\n            newIsOutput: false\n        };\n    }\n    // Conveyors connect in the direction they're facing\n    if (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR && comp1.direction === direction && def2.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: true\n        };\n    }\n    if (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR && comp2.direction === getOppositeDirection(direction) && def1.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: false\n        };\n    }\n    // Default: don't auto-connect\n    return {\n        connect: false,\n        newIsOutput: false\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/gameStore.ts\n"));

/***/ })

});