/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Csrc%5C%5Ccomponents%5C%5CFactoryGame.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Csrc%5C%5Ccomponents%5C%5CFactoryGame.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/FactoryGame.tsx */ \"(rsc)/./src/components/FactoryGame.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEb3dubG9hZHMlNUMlNUNmYWN0b3J5Z2FtZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNGYWN0b3J5R2FtZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBMkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXERvd25sb2Fkc1xcXFxmYWN0b3J5Z2FtZVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxGYWN0b3J5R2FtZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Csrc%5C%5Ccomponents%5C%5CFactoryGame.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"25da5bbb4439\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjI1ZGE1YmJiNDQzOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNyZWF0ZSBOZXh0IEFwcFwiLFxuICBkZXNjcmlwdGlvbjogXCJHZW5lcmF0ZWQgYnkgY3JlYXRlIG5leHQgYXBwXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_FactoryGame__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/FactoryGame */ \"(rsc)/./src/components/FactoryGame.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FactoryGame__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW1EO0FBRXBDLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDSCwrREFBV0E7Ozs7Ozs7Ozs7QUFHbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcc3JjXFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBGYWN0b3J5R2FtZSBmcm9tICdAL2NvbXBvbmVudHMvRmFjdG9yeUdhbWUnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktOTAwXCI+XG4gICAgICA8RmFjdG9yeUdhbWUgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJGYWN0b3J5R2FtZSIsIkhvbWUiLCJkaXYiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/FactoryGame.tsx":
/*!****************************************!*\
  !*** ./src/components/FactoryGame.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\factorygame\\src\\components\\FactoryGame.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Csrc%5C%5Ccomponents%5C%5CFactoryGame.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Csrc%5C%5Ccomponents%5C%5CFactoryGame.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/FactoryGame.tsx */ \"(ssr)/./src/components/FactoryGame.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEb3dubG9hZHMlNUMlNUNmYWN0b3J5Z2FtZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNGYWN0b3J5R2FtZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBMkkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXERvd25sb2Fkc1xcXFxmYWN0b3J5Z2FtZVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxGYWN0b3J5R2FtZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDownloads%5C%5Cfactorygame%5C%5Csrc%5C%5Ccomponents%5C%5CFactoryGame.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/analytics/performanceAnalyzer.ts":
/*!**********************************************!*\
  !*** ./src/analytics/performanceAnalyzer.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceAnalyzer: () => (/* binding */ PerformanceAnalyzer)\n/* harmony export */ });\n/* harmony import */ var _types_game__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/game */ \"(ssr)/./src/types/game.ts\");\n\nclass PerformanceAnalyzer {\n    analyzeFactory(gameState) {\n        const currentTime = Date.now();\n        // Only run full analysis periodically\n        if (currentTime - this.lastAnalysisTime < this.ANALYSIS_INTERVAL) {\n            return this.getLastAnalysis(gameState);\n        }\n        this.lastAnalysisTime = currentTime;\n        const componentAnalytics = this.analyzeComponents(gameState);\n        const resourceFlows = this.analyzeResourceFlows(gameState, componentAnalytics);\n        const bottlenecks = this.identifyBottlenecks(componentAnalytics);\n        const overallEfficiency = this.calculateOverallEfficiency(componentAnalytics);\n        const totalThroughput = this.calculateTotalThroughput(componentAnalytics);\n        const recommendations = this.generateRecommendations(componentAnalytics, resourceFlows, bottlenecks);\n        const performanceScore = this.calculatePerformanceScore(overallEfficiency, totalThroughput, bottlenecks.length);\n        // Store historical data\n        this.updateHistoricalData('efficiency', overallEfficiency);\n        this.updateHistoricalData('throughput', totalThroughput);\n        return {\n            overallEfficiency,\n            totalThroughput,\n            componentAnalytics,\n            resourceFlows,\n            bottlenecks,\n            recommendations,\n            performanceScore\n        };\n    }\n    analyzeComponents(gameState) {\n        const analytics = new Map();\n        for (const [id, component] of gameState.components){\n            const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n            const utilization = this.calculateUtilization(component);\n            const throughput = this.calculateThroughput(component);\n            const efficiency = this.calculateComponentEfficiency(component);\n            const bottleneckScore = this.calculateBottleneckScore(component, gameState);\n            const inputStarvation = this.calculateInputStarvation(component, gameState);\n            const outputBlocked = this.calculateOutputBlocked(component, gameState);\n            const averageInventory = this.calculateAverageInventory(component);\n            const processingTime = this.calculateProcessingTime(component);\n            analytics.set(id, {\n                id,\n                type: component.type,\n                utilization,\n                throughput,\n                efficiency,\n                bottleneckScore,\n                inputStarvation,\n                outputBlocked,\n                averageInventory,\n                processingTime\n            });\n        }\n        return analytics;\n    }\n    calculateUtilization(component) {\n        // Simple utilization based on activity\n        return component.isActive ? 1.0 : 0.0;\n    }\n    calculateThroughput(component) {\n        const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n        const baseSpeed = definition.speed;\n        // Convert to items per minute\n        const itemsPerMinute = baseSpeed * 60;\n        // Adjust based on actual utilization\n        return component.isActive ? itemsPerMinute : 0;\n    }\n    calculateComponentEfficiency(component) {\n        const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n        // For assemblers, check if they have the right inputs\n        if (component.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.ASSEMBLER && component.recipe) {\n            const hasAllInputs = component.recipe.inputs.every((input)=>{\n                const available = component.inventory.get(input.resource) || 0;\n                return available >= input.amount;\n            });\n            return hasAllInputs ? 1.0 : 0.5;\n        }\n        // For other components, efficiency is based on activity\n        return component.isActive ? 1.0 : 0.0;\n    }\n    calculateBottleneckScore(component, gameState) {\n        // A component is a bottleneck if it's blocking others or being blocked\n        let score = 0;\n        // Check if outputs are connected but not flowing\n        const outputComponents = component.connections.outputs.map((id)=>gameState.components.get(id)).filter((comp)=>comp !== undefined);\n        if (outputComponents.length > 0) {\n            const blockedOutputs = outputComponents.filter((comp)=>!comp.isActive).length;\n            score += blockedOutputs / outputComponents.length * 0.5;\n        }\n        // Check if inputs are starved\n        const inputComponents = component.connections.inputs.map((id)=>gameState.components.get(id)).filter((comp)=>comp !== undefined);\n        if (inputComponents.length > 0) {\n            const starvedInputs = inputComponents.filter((comp)=>!comp.isActive).length;\n            score += starvedInputs / inputComponents.length * 0.5;\n        }\n        return Math.min(score, 1.0);\n    }\n    calculateInputStarvation(component, gameState) {\n        if (component.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MINER) return 0; // Miners don't need inputs\n        const totalInventory = Array.from(component.inventory.values()).reduce((sum, amount)=>sum + amount, 0);\n        const maxCapacity = this.getComponentCapacity(component);\n        return 1 - totalInventory / Math.max(maxCapacity * 0.5, 1); // Starved if less than 50% capacity\n    }\n    calculateOutputBlocked(component, gameState) {\n        const totalInventory = Array.from(component.inventory.values()).reduce((sum, amount)=>sum + amount, 0);\n        const maxCapacity = this.getComponentCapacity(component);\n        return totalInventory / Math.max(maxCapacity, 1); // Blocked if inventory is full\n    }\n    calculateAverageInventory(component) {\n        const totalInventory = Array.from(component.inventory.values()).reduce((sum, amount)=>sum + amount, 0);\n        return totalInventory;\n    }\n    calculateProcessingTime(component) {\n        if (component.recipe) {\n            return component.recipe.processingTime;\n        }\n        const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n        return 1000 / definition.speed; // Convert speed to processing time\n    }\n    getComponentCapacity(component) {\n        switch(component.type){\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.STORAGE:\n                return 1000;\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.ASSEMBLER:\n                return 100;\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MINER:\n                return 50;\n            default:\n                return 10;\n        }\n    }\n    analyzeResourceFlows(gameState, componentAnalytics) {\n        const flows = new Map();\n        // Initialize all resource types\n        Object.values(_types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType).forEach((resource)=>{\n            flows.set(resource, {\n                resource,\n                totalProduction: 0,\n                totalConsumption: 0,\n                netFlow: 0,\n                flowEfficiency: 0,\n                bottleneckComponents: []\n            });\n        });\n        // Calculate production and consumption\n        for (const [id, component] of gameState.components){\n            const analytics = componentAnalytics.get(id);\n            if (!analytics) continue;\n            // Production (miners and assemblers)\n            if (component.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MINER) {\n                const flow = flows.get(_types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.IRON_ORE);\n                flow.totalProduction += analytics.throughput;\n            } else if (component.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.ASSEMBLER && component.recipe) {\n                component.recipe.outputs.forEach((output)=>{\n                    const flow = flows.get(output.resource);\n                    flow.totalProduction += analytics.throughput * output.amount;\n                });\n                component.recipe.inputs.forEach((input)=>{\n                    const flow = flows.get(input.resource);\n                    flow.totalConsumption += analytics.throughput * input.amount;\n                });\n            }\n            // Check for bottlenecks\n            if (analytics.bottleneckScore > 0.5) {\n                Object.values(_types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType).forEach((resource)=>{\n                    if (component.inventory.has(resource)) {\n                        flows.get(resource).bottleneckComponents.push(id);\n                    }\n                });\n            }\n        }\n        // Calculate net flow and efficiency\n        flows.forEach((flow)=>{\n            flow.netFlow = flow.totalProduction - flow.totalConsumption;\n            flow.flowEfficiency = flow.totalConsumption > 0 ? Math.min(flow.totalProduction / flow.totalConsumption, 1.0) : flow.totalProduction > 0 ? 1.0 : 0.0;\n        });\n        return flows;\n    }\n    identifyBottlenecks(componentAnalytics) {\n        return Array.from(componentAnalytics.entries()).filter(([_, analytics])=>analytics.bottleneckScore > 0.3).sort((a, b)=>b[1].bottleneckScore - a[1].bottleneckScore).map(([id, _])=>id);\n    }\n    calculateOverallEfficiency(componentAnalytics) {\n        if (componentAnalytics.size === 0) return 0;\n        const totalEfficiency = Array.from(componentAnalytics.values()).reduce((sum, analytics)=>sum + analytics.efficiency, 0);\n        return totalEfficiency / componentAnalytics.size;\n    }\n    calculateTotalThroughput(componentAnalytics) {\n        return Array.from(componentAnalytics.values()).reduce((sum, analytics)=>sum + analytics.throughput, 0);\n    }\n    generateRecommendations(componentAnalytics, resourceFlows, bottlenecks) {\n        const recommendations = [];\n        // Check for bottlenecks\n        if (bottlenecks.length > 0) {\n            recommendations.push(`Address ${bottlenecks.length} bottleneck(s) to improve efficiency`);\n        }\n        // Check for resource imbalances\n        resourceFlows.forEach((flow)=>{\n            if (flow.netFlow < -10) {\n                recommendations.push(`Increase production of ${flow.resource.replace('_', ' ')}`);\n            } else if (flow.netFlow > 50) {\n                recommendations.push(`Consider using excess ${flow.resource.replace('_', ' ')}`);\n            }\n        });\n        // Check for underutilized components\n        const underutilized = Array.from(componentAnalytics.values()).filter((analytics)=>analytics.utilization < 0.5);\n        if (underutilized.length > 0) {\n            recommendations.push(`${underutilized.length} components are underutilized`);\n        }\n        return recommendations;\n    }\n    calculatePerformanceScore(efficiency, throughput, bottleneckCount) {\n        const efficiencyScore = efficiency * 40; // 40 points max\n        const throughputScore = Math.min(throughput / 100, 1) * 40; // 40 points max\n        const bottleneckPenalty = bottleneckCount * 5; // -5 points per bottleneck\n        return Math.max(0, Math.min(100, efficiencyScore + throughputScore - bottleneckPenalty));\n    }\n    updateHistoricalData(key, value) {\n        if (!this.historicalData.has(key)) {\n            this.historicalData.set(key, []);\n        }\n        const data = this.historicalData.get(key);\n        data.push(value);\n        // Keep only recent data\n        if (data.length > this.HISTORY_LENGTH) {\n            data.shift();\n        }\n    }\n    getLastAnalysis(gameState) {\n        // Return a simplified analysis for frequent updates\n        const componentAnalytics = new Map();\n        for (const [id, component] of gameState.components){\n            componentAnalytics.set(id, {\n                id,\n                type: component.type,\n                utilization: component.isActive ? 1.0 : 0.0,\n                throughput: 0,\n                efficiency: 0,\n                bottleneckScore: 0,\n                inputStarvation: 0,\n                outputBlocked: 0,\n                averageInventory: 0,\n                processingTime: 0\n            });\n        }\n        return {\n            overallEfficiency: 0,\n            totalThroughput: 0,\n            componentAnalytics,\n            resourceFlows: new Map(),\n            bottlenecks: [],\n            recommendations: [],\n            performanceScore: 0\n        };\n    }\n    getHistoricalData(key) {\n        return this.historicalData.get(key) || [];\n    }\n    constructor(){\n        this.historicalData = new Map();\n        this.lastAnalysisTime = 0;\n        this.ANALYSIS_INTERVAL = 5000 // 5 seconds\n        ;\n        this.HISTORY_LENGTH = 20 // Keep 20 data points\n        ;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/analytics/performanceAnalyzer.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/AIAssistant.tsx":
/*!****************************************!*\
  !*** ./src/components/AIAssistant.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_gameStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/gameStore */ \"(ssr)/./src/store/gameStore.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Copy,Download,Loader2,MessageSquare,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Copy,Download,Loader2,MessageSquare,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Copy,Download,Loader2,MessageSquare,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Copy,Download,Loader2,MessageSquare,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Copy,Download,Loader2,MessageSquare,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Copy,Download,Loader2,MessageSquare,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Copy,Download,Loader2,MessageSquare,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Copy,Download,Loader2,MessageSquare,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst AIAssistant = ()=>{\n    const { components, resources, statistics, gridSize, gameTime, isRunning, getFactoryAnalytics } = (0,_store_gameStore__WEBPACK_IMPORTED_MODULE_2__.useGameStore)();\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [analysisResult, setAnalysisResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copySuccess, setCopySuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAnalyze = async ()=>{\n        setIsAnalyzing(true);\n        setError(null);\n        try {\n            const gameState = {\n                components,\n                resources,\n                statistics,\n                gridSize,\n                gameTime,\n                isRunning\n            };\n            const analytics = getFactoryAnalytics();\n            const response = await fetch('/api/ai/analyze', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    gameState,\n                    analytics\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to analyze factory');\n            }\n            const result = await response.json();\n            setAnalysisResult(result.data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error occurred');\n        } finally{\n            setIsAnalyzing(false);\n        }\n    };\n    const handleCopyPrompt = async ()=>{\n        if (!analysisResult) return;\n        try {\n            await navigator.clipboard.writeText(analysisResult.aiPrompt);\n            setCopySuccess(true);\n            setTimeout(()=>setCopySuccess(false), 2000);\n        } catch (err) {\n            setError('Failed to copy to clipboard');\n        }\n    };\n    const handleDownloadState = ()=>{\n        if (!analysisResult) return;\n        const blob = new Blob([\n            JSON.stringify(analysisResult.serializedState, null, 2)\n        ], {\n            type: 'application/json'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `factory-state-${Date.now()}.json`;\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    const handleDownloadPrompt = ()=>{\n        if (!analysisResult) return;\n        const blob = new Blob([\n            analysisResult.aiPrompt\n        ], {\n            type: 'text/plain'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `factory-analysis-${Date.now()}.txt`;\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-5 h-5 text-purple-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-white\",\n                        children: \"AI Assistant\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"Generate an AI-friendly analysis of your factory for optimization suggestions.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleAnalyze,\n                        disabled: isAnalyzing || components.size === 0,\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__[\"default\"])('w-full flex items-center justify-center gap-2 p-3 rounded-lg font-medium transition-colors', {\n                            'bg-purple-600 hover:bg-purple-700 text-white': !isAnalyzing && components.size > 0,\n                            'bg-gray-600 text-gray-400 cursor-not-allowed': isAnalyzing || components.size === 0\n                        }),\n                        children: isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Analyzing Factory...\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Generate AI Analysis\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    components.size === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-yellow-400 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Add some components to your factory first\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-red-400 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, undefined),\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined),\n                    analysisResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 border-t border-gray-700 pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-green-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Analysis Complete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-700 rounded p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Components\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white font-semibold\",\n                                                children: analysisResult.metadata.componentCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-700 rounded p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Efficiency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white font-semibold\",\n                                                children: [\n                                                    (analysisResult.metadata.efficiency * 100).toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCopyPrompt,\n                                                className: \"flex items-center justify-center gap-2 p-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors\",\n                                                children: copySuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Copied!\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Copy Prompt\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleDownloadPrompt,\n                                                className: \"flex items-center justify-center gap-2 p-2 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Download\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDownloadState,\n                                        className: \"w-full flex items-center justify-center gap-2 p-2 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Copy_Download_Loader2_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Download State JSON\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400\",\n                                children: \"Use the copied prompt with any AI assistant (ChatGPT, Claude, etc.) to get optimization suggestions for your factory.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-700 pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-white mb-2\",\n                                children: \"How to use:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"text-xs text-gray-300 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"1. Build your factory with components\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: '2. Click \"Generate AI Analysis\"'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"3. Copy the generated prompt\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"4. Paste it into any AI assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"5. Get optimization suggestions!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\AIAssistant.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AIAssistant);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AIAssistant.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ComponentPalette.tsx":
/*!*********************************************!*\
  !*** ./src/components/ComponentPalette.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDrag.js\");\n/* harmony import */ var _types_game__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/game */ \"(ssr)/./src/types/game.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronDown,ChevronRight,Cog,Merge,Package,Pickaxe,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronDown,ChevronRight,Cog,Merge,Package,Pickaxe,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pickaxe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronDown,ChevronRight,Cog,Merge,Package,Pickaxe,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronDown,ChevronRight,Cog,Merge,Package,Pickaxe,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronDown,ChevronRight,Cog,Merge,Package,Pickaxe,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/split.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronDown,ChevronRight,Cog,Merge,Package,Pickaxe,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/merge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronDown,ChevronRight,Cog,Merge,Package,Pickaxe,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChevronDown,ChevronRight,Cog,Merge,Package,Pickaxe,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst DraggableComponent = ({ type, definition })=>{\n    const [{ isDragging }, drag] = (0,react_dnd__WEBPACK_IMPORTED_MODULE_4__.useDrag)({\n        type: 'component',\n        item: {\n            type\n        },\n        collect: {\n            \"DraggableComponent.useDrag\": (monitor)=>({\n                    isDragging: monitor.isDragging()\n                })\n        }[\"DraggableComponent.useDrag\"]\n    });\n    const getIcon = ()=>{\n        switch(type){\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.CONVEYOR:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, undefined);\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.MINER:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, undefined);\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.ASSEMBLER:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, undefined);\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.STORAGE:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, undefined);\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.SPLITTER:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, undefined);\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.MERGER:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getColor = ()=>{\n        switch(type){\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.CONVEYOR:\n                return 'bg-yellow-600 hover:bg-yellow-500 border-yellow-500';\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.MINER:\n                return 'bg-blue-600 hover:bg-blue-500 border-blue-500';\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.ASSEMBLER:\n                return 'bg-green-600 hover:bg-green-500 border-green-500';\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.STORAGE:\n                return 'bg-purple-600 hover:bg-purple-500 border-purple-500';\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.SPLITTER:\n                return 'bg-orange-600 hover:bg-orange-500 border-orange-500';\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.MERGER:\n                return 'bg-red-600 hover:bg-red-500 border-red-500';\n            default:\n                return 'bg-gray-600 hover:bg-gray-500 border-gray-500';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: drag,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__[\"default\"])('p-4 border-2 rounded-lg cursor-grab transition-all duration-200 text-white', getColor(), {\n            'opacity-50 cursor-grabbing': isDragging,\n            'transform scale-105': !isDragging\n        }),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: getIcon()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-sm truncate\",\n                            children: definition.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs opacity-90 line-clamp-2\",\n                            children: definition.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1 text-xs opacity-75\",\n                            children: [\n                                \"Speed: \",\n                                definition.speed\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\nconst CategorySection = ({ title, components, isExpanded, onToggle })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"w-full flex items-center justify-between p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-semibold text-white\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChevronDown_ChevronRight_Cog_Merge_Package_Pickaxe_Split_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 space-y-2\",\n                children: components.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DraggableComponent, {\n                        type: type,\n                        definition: _types_game__WEBPACK_IMPORTED_MODULE_2__.COMPONENT_DEFINITIONS[type]\n                    }, type, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, undefined);\n};\nconst ComponentPalette = ()=>{\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'Transportation',\n        'Production'\n    ]));\n    const toggleCategory = (category)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(category)) {\n            newExpanded.delete(category);\n        } else {\n            newExpanded.add(category);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const categories = [\n        {\n            title: 'Transportation',\n            components: [\n                _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.CONVEYOR,\n                _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.SPLITTER,\n                _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.MERGER\n            ]\n        },\n        {\n            title: 'Production',\n            components: [\n                _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.MINER,\n                _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.ASSEMBLER\n            ]\n        },\n        {\n            title: 'Storage',\n            components: [\n                _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.STORAGE\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full p-4 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white mb-2\",\n                        children: \"Components\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"Drag components onto the board to build your factory\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, undefined),\n            categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategorySection, {\n                    title: category.title,\n                    components: category.components,\n                    isExpanded: expandedCategories.has(category.title),\n                    onToggle: ()=>toggleCategory(category.title)\n                }, category.title, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, undefined)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 p-4 bg-gray-700 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-white mb-2\",\n                        children: \"Tips\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-xs text-gray-300 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Drag components to place them\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Right-click to rotate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Double-click assemblers to set recipes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Connect components by placing them adjacent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Use splitters to divide resource flows\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Use mergers to combine resource flows\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\ComponentPalette.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponentPalette);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ComponentPalette.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FactoryComponent.tsx":
/*!*********************************************!*\
  !*** ./src/components/FactoryComponent.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDrag.js\");\n/* harmony import */ var _types_game__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/game */ \"(ssr)/./src/types/game.ts\");\n/* harmony import */ var _store_gameStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/gameStore */ \"(ssr)/./src/store/gameStore.ts\");\n/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/helpers */ \"(ssr)/./src/utils/helpers.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Cog,Merge,Package,Pickaxe,RotateCw,Settings,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Cog,Merge,Package,Pickaxe,RotateCw,Settings,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pickaxe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Cog,Merge,Package,Pickaxe,RotateCw,Settings,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Cog,Merge,Package,Pickaxe,RotateCw,Settings,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Cog,Merge,Package,Pickaxe,RotateCw,Settings,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/split.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Cog,Merge,Package,Pickaxe,RotateCw,Settings,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/merge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Cog,Merge,Package,Pickaxe,RotateCw,Settings,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Cog,Merge,Package,Pickaxe,RotateCw,Settings,Split!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst FactoryComponent = ({ component, position, cellSize, isSelected, onSelect, onMove })=>{\n    const { rotateComponent, setComponentRecipe } = (0,_store_gameStore__WEBPACK_IMPORTED_MODULE_3__.useGameStore)();\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const definition = _types_game__WEBPACK_IMPORTED_MODULE_2__.COMPONENT_DEFINITIONS[component.type];\n    const [{ isDragging }, drag] = (0,react_dnd__WEBPACK_IMPORTED_MODULE_6__.useDrag)({\n        type: 'placed-component',\n        item: {\n            id: component.id,\n            originalPosition: component.position\n        },\n        end: {\n            \"FactoryComponent.useDrag\": (item, monitor)=>{\n                const dropResult = monitor.getDropResult();\n                if (!dropResult) return;\n                const clientOffset = monitor.getClientOffset();\n                if (!clientOffset) return;\n                // Calculate new grid position\n                const newGridPos = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_4__.screenToGrid)({\n                    x: clientOffset.x,\n                    y: clientOffset.y\n                }, cellSize);\n                onMove(newGridPos);\n            }\n        }[\"FactoryComponent.useDrag\"],\n        collect: {\n            \"FactoryComponent.useDrag\": (monitor)=>({\n                    isDragging: monitor.isDragging()\n                })\n        }[\"FactoryComponent.useDrag\"]\n    });\n    const getComponentIcon = ()=>{\n        switch(component.type){\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.CONVEYOR:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 16\n                }, undefined);\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.MINER:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 16\n                }, undefined);\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.ASSEMBLER:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 16\n                }, undefined);\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.STORAGE:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 16\n                }, undefined);\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.SPLITTER:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 16\n                }, undefined);\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.MERGER:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getComponentColor = ()=>{\n        switch(component.type){\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.CONVEYOR:\n                return 'bg-yellow-600 border-yellow-500';\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.MINER:\n                return 'bg-blue-600 border-blue-500';\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.ASSEMBLER:\n                return 'bg-green-600 border-green-500';\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.STORAGE:\n                return 'bg-purple-600 border-purple-500';\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.SPLITTER:\n                return 'bg-orange-600 border-orange-500';\n            case _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.MERGER:\n                return 'bg-red-600 border-red-500';\n            default:\n                return 'bg-gray-600 border-gray-500';\n        }\n    };\n    const handleRightClick = (e)=>{\n        e.preventDefault();\n        rotateComponent(component.id);\n    };\n    const handleDoubleClick = ()=>{\n        if (component.type === _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.ASSEMBLER) {\n            // Open recipe selection (simplified for now)\n            const recipeId = prompt('Enter recipe ID (iron_plate, copper_plate, gear, circuit):');\n            if (recipeId) {\n                setComponentRecipe(component.id, recipeId);\n            }\n        }\n    };\n    const width = definition.size.width * cellSize;\n    const height = definition.size.height * cellSize;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: drag,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_5__[\"default\"])('absolute border-2 rounded cursor-pointer transition-all duration-200 flex items-center justify-center', getComponentColor(), {\n            'ring-2 ring-blue-400 ring-opacity-75': isSelected,\n            'opacity-50': isDragging,\n            'shadow-lg': component.isActive,\n            'animate-pulse': component.isActive && component.type === _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.MINER\n        }),\n        style: {\n            left: position.x,\n            top: position.y,\n            width,\n            height,\n            transform: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_4__.getRotationTransform)(component.direction)\n        },\n        onClick: onSelect,\n        onContextMenu: handleRightClick,\n        onDoubleClick: handleDoubleClick,\n        onMouseEnter: ()=>setShowTooltip(true),\n        onMouseLeave: ()=>setShowTooltip(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: getComponentIcon()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, undefined),\n            component.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1 right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, undefined),\n            component.inventory.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1 left-1 text-xs text-white bg-black bg-opacity-50 rounded px-1\",\n                children: Array.from(component.inventory.values()).reduce((sum, amount)=>sum + amount, 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, undefined),\n            component.type === _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.ASSEMBLER && component.recipe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1 left-1 text-xs text-white bg-black bg-opacity-50 rounded px-1\",\n                children: component.recipe.name.slice(0, 3)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, undefined),\n            definition.maxInputs > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-green-400 rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, undefined),\n            definition.maxOutputs > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -right-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-red-400 rounded-full\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, undefined),\n            showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black bg-opacity-90 text-white text-xs rounded whitespace-nowrap z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold\",\n                        children: definition.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: definition.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, undefined),\n                    component.recipe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-yellow-300\",\n                        children: [\n                            \"Recipe: \",\n                            component.recipe.name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-300\",\n                        children: \"Right-click to rotate, Double-click for settings\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                lineNumber: 179,\n                columnNumber: 9\n            }, undefined),\n            isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-8 left-0 flex gap-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            rotateComponent(component.id);\n                        },\n                        className: \"p-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                        title: \"Rotate\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-3 h-3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, undefined),\n                    component.type === _types_game__WEBPACK_IMPORTED_MODULE_2__.ComponentType.ASSEMBLER && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            handleDoubleClick();\n                        },\n                        className: \"p-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors\",\n                        title: \"Configure Recipe\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Cog_Merge_Package_Pickaxe_RotateCw_Settings_Split_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"w-3 h-3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n                lineNumber: 193,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryComponent.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FactoryComponent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FactoryComponent.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FactoryGame.tsx":
/*!****************************************!*\
  !*** ./src/components/FactoryGame.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/core/DndProvider.js\");\n/* harmony import */ var react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-dnd-html5-backend */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/index.js\");\n/* harmony import */ var _GameBoard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GameBoard */ \"(ssr)/./src/components/GameBoard.tsx\");\n/* harmony import */ var _ComponentPalette__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ComponentPalette */ \"(ssr)/./src/components/ComponentPalette.tsx\");\n/* harmony import */ var _StatsPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StatsPanel */ \"(ssr)/./src/components/StatsPanel.tsx\");\n/* harmony import */ var _store_gameStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/gameStore */ \"(ssr)/./src/store/gameStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst FactoryGame = ()=>{\n    const { isRunning, toggleSimulation } = (0,_store_gameStore__WEBPACK_IMPORTED_MODULE_5__.useGameStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_dnd__WEBPACK_IMPORTED_MODULE_6__.DndProvider, {\n        backend: react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_7__.HTML5Backend,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex flex-col bg-gray-900 text-white\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-gray-800 p-4 border-b border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-blue-400\",\n                                children: \"Factory Builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleSimulation,\n                                    className: `px-4 py-2 rounded font-medium transition-colors ${isRunning ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-green-600 hover:bg-green-700 text-white'}`,\n                                    children: [\n                                        isRunning ? 'Pause' : 'Start',\n                                        \" Simulation\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-80 bg-gray-800 border-r border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentPalette__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GameBoard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-80 bg-gray-800 border-l border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsPanel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\FactoryGame.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FactoryGame);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9GYWN0b3J5R2FtZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ2M7QUFDZTtBQUNuQjtBQUNjO0FBQ1o7QUFDVztBQUVqRCxNQUFNTyxjQUF3QjtJQUM1QixNQUFNLEVBQUVDLFNBQVMsRUFBRUMsZ0JBQWdCLEVBQUUsR0FBR0gsOERBQVlBO0lBRXBELHFCQUNFLDhEQUFDTCxrREFBV0E7UUFBQ1MsU0FBU1IsaUVBQVlBO2tCQUNoQyw0RUFBQ1M7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNDO29CQUFPRCxXQUFVOzhCQUNoQiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBR0YsV0FBVTswQ0FBbUM7Ozs7OzswQ0FDakQsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRztvQ0FDQ0MsU0FBU1A7b0NBQ1RHLFdBQVcsQ0FBQyxnREFBZ0QsRUFDMURKLFlBQ0ksMkNBQ0EsOENBQ0o7O3dDQUVEQSxZQUFZLFVBQVU7d0NBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU92Qyw4REFBQ0c7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ1IseURBQWdCQTs7Ozs7Ozs7OztzQ0FJbkIsOERBQUNPOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDVCxrREFBU0E7Ozs7Ozs7Ozs7c0NBSVosOERBQUNROzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDUCxtREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU12QjtBQUVBLGlFQUFlRSxXQUFXQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXHNyY1xcY29tcG9uZW50c1xcRmFjdG9yeUdhbWUudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IERuZFByb3ZpZGVyIH0gZnJvbSAncmVhY3QtZG5kJztcbmltcG9ydCB7IEhUTUw1QmFja2VuZCB9IGZyb20gJ3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kJztcbmltcG9ydCBHYW1lQm9hcmQgZnJvbSAnLi9HYW1lQm9hcmQnO1xuaW1wb3J0IENvbXBvbmVudFBhbGV0dGUgZnJvbSAnLi9Db21wb25lbnRQYWxldHRlJztcbmltcG9ydCBTdGF0c1BhbmVsIGZyb20gJy4vU3RhdHNQYW5lbCc7XG5pbXBvcnQgeyB1c2VHYW1lU3RvcmUgfSBmcm9tICdAL3N0b3JlL2dhbWVTdG9yZSc7XG5cbmNvbnN0IEZhY3RvcnlHYW1lOiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgY29uc3QgeyBpc1J1bm5pbmcsIHRvZ2dsZVNpbXVsYXRpb24gfSA9IHVzZUdhbWVTdG9yZSgpO1xuXG4gIHJldHVybiAoXG4gICAgPERuZFByb3ZpZGVyIGJhY2tlbmQ9e0hUTUw1QmFja2VuZH0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtc2NyZWVuIGZsZXggZmxleC1jb2wgYmctZ3JheS05MDAgdGV4dC13aGl0ZVwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLWdyYXktODAwIHAtNCBib3JkZXItYiBib3JkZXItZ3JheS03MDBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNDAwXCI+RmFjdG9yeSBCdWlsZGVyPC9oMT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtNCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZVNpbXVsYXRpb259XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNCBweS0yIHJvdW5kZWQgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgIGlzUnVubmluZ1xuICAgICAgICAgICAgICAgICAgICA/ICdiZy1yZWQtNjAwIGhvdmVyOmJnLXJlZC03MDAgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzUnVubmluZyA/ICdQYXVzZScgOiAnU3RhcnQnfSBTaW11bGF0aW9uXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvaGVhZGVyPlxuXG4gICAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXhcIj5cbiAgICAgICAgICB7LyogQ29tcG9uZW50IFBhbGV0dGUgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTgwIGJnLWdyYXktODAwIGJvcmRlci1yIGJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgPENvbXBvbmVudFBhbGV0dGUgLz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBHYW1lIEJvYXJkICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICA8R2FtZUJvYXJkIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU3RhdHMgUGFuZWwgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTgwIGJnLWdyYXktODAwIGJvcmRlci1sIGJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgPFN0YXRzUGFuZWwgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L0RuZFByb3ZpZGVyPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgRmFjdG9yeUdhbWU7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJEbmRQcm92aWRlciIsIkhUTUw1QmFja2VuZCIsIkdhbWVCb2FyZCIsIkNvbXBvbmVudFBhbGV0dGUiLCJTdGF0c1BhbmVsIiwidXNlR2FtZVN0b3JlIiwiRmFjdG9yeUdhbWUiLCJpc1J1bm5pbmciLCJ0b2dnbGVTaW11bGF0aW9uIiwiYmFja2VuZCIsImRpdiIsImNsYXNzTmFtZSIsImhlYWRlciIsImgxIiwiYnV0dG9uIiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FactoryGame.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/GameBoard.tsx":
/*!**************************************!*\
  !*** ./src/components/GameBoard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDrop.js\");\n/* harmony import */ var _store_gameStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/gameStore */ \"(ssr)/./src/store/gameStore.ts\");\n/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/helpers */ \"(ssr)/./src/utils/helpers.ts\");\n/* harmony import */ var _FactoryComponent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FactoryComponent */ \"(ssr)/./src/components/FactoryComponent.tsx\");\n/* harmony import */ var _GridOverlay__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./GridOverlay */ \"(ssr)/./src/components/GridOverlay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst CELL_SIZE = 40;\nconst BOARD_WIDTH = 2000;\nconst BOARD_HEIGHT = 2000;\nconst GameBoard = ()=>{\n    const boardRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [viewOffset, setViewOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [selectedComponent, setSelectedComponent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { components, addComponent, moveComponent, removeComponent, isRunning, updateSimulation } = (0,_store_gameStore__WEBPACK_IMPORTED_MODULE_2__.useGameStore)();\n    // Animation loop for simulation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GameBoard.useEffect\": ()=>{\n            if (!isRunning) return;\n            const interval = setInterval({\n                \"GameBoard.useEffect.interval\": ()=>{\n                    updateSimulation();\n                }\n            }[\"GameBoard.useEffect.interval\"], 100);\n            return ({\n                \"GameBoard.useEffect\": ()=>clearInterval(interval)\n            })[\"GameBoard.useEffect\"];\n        }\n    }[\"GameBoard.useEffect\"], [\n        isRunning,\n        updateSimulation\n    ]);\n    // Drop handler for adding components\n    const [{ isOver }, drop] = (0,react_dnd__WEBPACK_IMPORTED_MODULE_6__.useDrop)({\n        accept: 'component',\n        drop: {\n            \"GameBoard.useDrop\": (item, monitor)=>{\n                const clientOffset = monitor.getClientOffset();\n                if (!clientOffset || !boardRef.current) return;\n                const boardRect = boardRef.current.getBoundingClientRect();\n                const screenPos = {\n                    x: clientOffset.x - boardRect.left,\n                    y: clientOffset.y - boardRect.top\n                };\n                const gridPos = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.screenToGrid)(screenPos, CELL_SIZE, viewOffset);\n                addComponent(item.type, gridPos);\n            }\n        }[\"GameBoard.useDrop\"],\n        collect: {\n            \"GameBoard.useDrop\": (monitor)=>({\n                    isOver: monitor.isOver()\n                })\n        }[\"GameBoard.useDrop\"]\n    });\n    // Pan handling\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GameBoard.useCallback[handleMouseDown]\": (e)=>{\n            if (e.button === 1 || e.button === 0 && e.ctrlKey) {\n                setIsDragging(true);\n                setDragStart({\n                    x: e.clientX - viewOffset.x,\n                    y: e.clientY - viewOffset.y\n                });\n                e.preventDefault();\n            }\n        }\n    }[\"GameBoard.useCallback[handleMouseDown]\"], [\n        viewOffset\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GameBoard.useCallback[handleMouseMove]\": (e)=>{\n            if (isDragging) {\n                setViewOffset({\n                    x: e.clientX - dragStart.x,\n                    y: e.clientY - dragStart.y\n                });\n            }\n        }\n    }[\"GameBoard.useCallback[handleMouseMove]\"], [\n        isDragging,\n        dragStart\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GameBoard.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n        }\n    }[\"GameBoard.useCallback[handleMouseUp]\"], []);\n    // Keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GameBoard.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"GameBoard.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Delete' && selectedComponent) {\n                        removeComponent(selectedComponent);\n                        setSelectedComponent(null);\n                    }\n                }\n            }[\"GameBoard.useEffect.handleKeyDown\"];\n            window.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"GameBoard.useEffect\": ()=>window.removeEventListener('keydown', handleKeyDown)\n            })[\"GameBoard.useEffect\"];\n        }\n    }[\"GameBoard.useEffect\"], [\n        selectedComponent,\n        removeComponent\n    ]);\n    // Combine refs\n    const combinedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GameBoard.useCallback[combinedRef]\": (node)=>{\n            boardRef.current = node;\n            drop(node);\n        }\n    }[\"GameBoard.useCallback[combinedRef]\"], [\n        drop\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: combinedRef,\n        className: `relative w-full h-full overflow-hidden bg-gray-900 cursor-${isDragging ? 'grabbing' : 'grab'} ${isOver ? 'bg-gray-800' : ''}`,\n        onMouseDown: handleMouseDown,\n        onMouseMove: handleMouseMove,\n        onMouseUp: handleMouseUp,\n        onMouseLeave: handleMouseUp,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GridOverlay__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                cellSize: CELL_SIZE,\n                offset: viewOffset,\n                width: BOARD_WIDTH,\n                height: BOARD_HEIGHT\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute\",\n                style: {\n                    transform: `translate(${viewOffset.x}px, ${viewOffset.y}px)`,\n                    width: BOARD_WIDTH,\n                    height: BOARD_HEIGHT\n                },\n                children: Array.from(components.entries()).map(([id, component])=>{\n                    const screenPos = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.gridToScreen)(component.position, CELL_SIZE);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FactoryComponent__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        component: component,\n                        position: screenPos,\n                        cellSize: CELL_SIZE,\n                        isSelected: selectedComponent === id,\n                        onSelect: ()=>setSelectedComponent(id),\n                        onMove: (newGridPos)=>moveComponent(id, newGridPos)\n                    }, id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, undefined),\n            components.size === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold mb-2\",\n                            children: \"Welcome to Factory Builder!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-1\",\n                            children: \"Drag components from the left panel to start building\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-1\",\n                            children: \"Middle-click or Ctrl+click to pan the view\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Press Delete to remove selected components\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, undefined),\n            isOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-blue-500 bg-opacity-20 border-2 border-blue-500 border-dashed pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GameBoard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/GameBoard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/GridOverlay.tsx":
/*!****************************************!*\
  !*** ./src/components/GridOverlay.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst GridOverlay = ({ cellSize, offset, width, height })=>{\n    const gridLines = [];\n    // Calculate visible grid range\n    const startX = Math.floor(-offset.x / cellSize) * cellSize;\n    const startY = Math.floor(-offset.y / cellSize) * cellSize;\n    const endX = startX + width + cellSize;\n    const endY = startY + height + cellSize;\n    // Vertical lines\n    for(let x = startX; x <= endX; x += cellSize){\n        gridLines.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n            x1: x,\n            y1: startY,\n            x2: x,\n            y2: endY,\n            stroke: \"rgba(255, 255, 255, 0.1)\",\n            strokeWidth: \"1\"\n        }, `v-${x}`, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GridOverlay.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined));\n    }\n    // Horizontal lines\n    for(let y = startY; y <= endY; y += cellSize){\n        gridLines.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n            x1: startX,\n            y1: y,\n            x2: endX,\n            y2: y,\n            stroke: \"rgba(255, 255, 255, 0.1)\",\n            strokeWidth: \"1\"\n        }, `h-${y}`, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GridOverlay.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined));\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"absolute inset-0 pointer-events-none\",\n        style: {\n            transform: `translate(${offset.x}px, ${offset.y}px)`\n        },\n        children: gridLines\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GridOverlay.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GridOverlay);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/GridOverlay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StatsPanel.tsx":
/*!***************************************!*\
  !*** ./src/components/StatsPanel.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_gameStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/gameStore */ \"(ssr)/./src/store/gameStore.ts\");\n/* harmony import */ var _types_game__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/types/game */ \"(ssr)/./src/types/game.ts\");\n/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/helpers */ \"(ssr)/./src/utils/helpers.ts\");\n/* harmony import */ var _AIAssistant__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AIAssistant */ \"(ssr)/./src/components/AIAssistant.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,ChevronDown,ChevronRight,Clock,Cog,Download,Package,RotateCcw,Upload,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst StatsSection = ({ title, icon, children, isExpanded, onToggle })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"w-full flex items-center justify-between p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            icon,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-white\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 p-3 bg-gray-800 rounded-lg\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\nconst ResourceDisplay = ({ type, amount })=>{\n    const getResourceColor = (type)=>{\n        switch(type){\n            case _types_game__WEBPACK_IMPORTED_MODULE_3__.ResourceType.IRON_ORE:\n                return 'text-gray-400';\n            case _types_game__WEBPACK_IMPORTED_MODULE_3__.ResourceType.COPPER_ORE:\n                return 'text-orange-400';\n            case _types_game__WEBPACK_IMPORTED_MODULE_3__.ResourceType.COAL:\n                return 'text-gray-600';\n            case _types_game__WEBPACK_IMPORTED_MODULE_3__.ResourceType.IRON_PLATE:\n                return 'text-gray-300';\n            case _types_game__WEBPACK_IMPORTED_MODULE_3__.ResourceType.COPPER_PLATE:\n                return 'text-orange-300';\n            case _types_game__WEBPACK_IMPORTED_MODULE_3__.ResourceType.GEAR:\n                return 'text-yellow-400';\n            case _types_game__WEBPACK_IMPORTED_MODULE_3__.ResourceType.CIRCUIT:\n                return 'text-green-400';\n            default:\n                return 'text-white';\n        }\n    };\n    const getResourceName = (type)=>{\n        return type.replace('_', ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase());\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-between items-center py-1\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,clsx__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('text-sm', getResourceColor(type)),\n                children: getResourceName(type)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-white font-mono text-sm\",\n                children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(amount)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\nconst StatsPanel = ()=>{\n    const { resources, statistics, components, gameTime, isRunning, getPerformanceMetrics, exportGameState, importGameState, resetGame } = (0,_store_gameStore__WEBPACK_IMPORTED_MODULE_2__.useGameStore)();\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'Production',\n        'Resources',\n        'AI Assistant'\n    ]));\n    const toggleSection = (section)=>{\n        const newExpanded = new Set(expandedSections);\n        if (newExpanded.has(section)) {\n            newExpanded.delete(section);\n        } else {\n            newExpanded.add(section);\n        }\n        setExpandedSections(newExpanded);\n    };\n    const metrics = getPerformanceMetrics();\n    const handleExport = ()=>{\n        const data = exportGameState();\n        const blob = new Blob([\n            data\n        ], {\n            type: 'application/json'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `factory-${Date.now()}.json`;\n        a.click();\n        URL.revokeObjectURL(url);\n    };\n    const handleImport = ()=>{\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = '.json';\n        input.onchange = (e)=>{\n            const file = e.target.files?.[0];\n            if (file) {\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    const content = e.target?.result;\n                    if (importGameState(content)) {\n                        alert('Factory imported successfully!');\n                    } else {\n                        alert('Failed to import factory. Please check the file format.');\n                    }\n                };\n                reader.readAsText(file);\n            }\n        };\n        input.click();\n    };\n    const componentCounts = Array.from(components.values()).reduce((acc, component)=>{\n        acc[component.type] = (acc[component.type] || 0) + 1;\n        return acc;\n    }, {});\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full p-4 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white mb-2\",\n                        children: \"Factory Stats\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-gray-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Runtime: \",\n                                    (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_4__.formatTime)(gameTime)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-gray-300 mt-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: isRunning ? 'text-green-400' : 'text-red-400',\n                                children: isRunning ? 'Running' : 'Paused'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsSection, {\n                title: \"Production\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 15\n                }, void 0),\n                isExpanded: expandedSections.has('Production'),\n                onToggle: ()=>toggleSection('Production'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: \"Efficiency\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-mono text-sm\",\n                                    children: [\n                                        (statistics.efficiency * 100).toFixed(1),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined),\n                        Array.from(statistics.totalProduction.entries()).map(([resource, amount])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResourceDisplay, {\n                                type: resource,\n                                amount: amount\n                            }, resource, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, undefined)),\n                        statistics.totalProduction.size === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"No production data yet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsSection, {\n                title: \"Resources\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-green-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 15\n                }, void 0),\n                isExpanded: expandedSections.has('Resources'),\n                onToggle: ()=>toggleSection('Resources'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: Array.from(resources.entries()).map(([resource, amount])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResourceDisplay, {\n                            type: resource,\n                            amount: amount\n                        }, resource, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsSection, {\n                title: \"Components\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4 text-purple-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 15\n                }, void 0),\n                isExpanded: expandedSections.has('Components'),\n                onToggle: ()=>toggleSection('Components'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        Object.entries(componentCounts).map(([type, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center py-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-300 capitalize\",\n                                        children: type.replace('_', ' ')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-mono text-sm\",\n                                        children: count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, type, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, undefined)),\n                        Object.keys(componentCounts).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm\",\n                            children: \"No components placed\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined),\n            statistics.bottlenecks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsSection, {\n                title: \"Bottlenecks\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"w-4 h-4 text-red-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 17\n                }, void 0),\n                isExpanded: expandedSections.has('Bottlenecks'),\n                onToggle: ()=>toggleSection('Bottlenecks'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: statistics.bottlenecks.map((componentId, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-red-300\",\n                            children: [\n                                \"Component \",\n                                index + 1,\n                                \": \",\n                                componentId.slice(0, 8),\n                                \"...\"\n                            ]\n                        }, componentId, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatsSection, {\n                title: \"AI Assistant\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"w-4 h-4 text-purple-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 15\n                }, void 0),\n                isExpanded: expandedSections.has('AI Assistant'),\n                onToggle: ()=>toggleSection('AI Assistant'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIAssistant__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-white mb-3\",\n                        children: \"Actions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleExport,\n                        className: \"w-full flex items-center gap-2 p-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Export Factory\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleImport,\n                        className: \"w-full flex items-center gap-2 p-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Import Factory\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            if (confirm('Are you sure you want to reset the factory? This cannot be undone.')) {\n                                resetGame();\n                            }\n                        },\n                        className: \"w-full flex items-center gap-2 p-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_ChevronDown_ChevronRight_Clock_Cog_Download_Package_RotateCcw_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Reset Factory\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 bg-gray-800 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-white mb-2\",\n                        children: \"Performance\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Components: \",\n                                    components.size\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Active: \",\n                                    Array.from(components.values()).filter((c)=>c.isActive).length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Efficiency: \",\n                                    (metrics.efficiency * 100).toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\StatsPanel.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StatsPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TdGF0c1BhbmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV3QztBQUNTO0FBQ1U7QUFDQTtBQUNuQjtBQWNsQjtBQUNFO0FBVXhCLE1BQU1vQixlQUE0QyxDQUFDLEVBQ2pEQyxLQUFLLEVBQ0xDLElBQUksRUFDSkMsUUFBUSxFQUNSQyxVQUFVLEVBQ1ZDLFFBQVEsRUFDVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQ0NDLFNBQVNKO2dCQUNURSxXQUFVOztrQ0FFViw4REFBQ0Q7d0JBQUlDLFdBQVU7OzRCQUNaTDswQ0FDRCw4REFBQ1E7Z0NBQUtILFdBQVU7MENBQTRCTjs7Ozs7Ozs7Ozs7O29CQUU3Q0csMkJBQ0MsOERBQUNiLGtMQUFXQTt3QkFBQ2dCLFdBQVU7Ozs7O2tEQUV2Qiw4REFBQ2Ysa0xBQVlBO3dCQUFDZSxXQUFVOzs7Ozs7Ozs7Ozs7WUFJM0JILDRCQUNDLDhEQUFDRTtnQkFBSUMsV0FBVTswQkFDWko7Ozs7Ozs7Ozs7OztBQUtYO0FBRUEsTUFBTVEsa0JBQW9FLENBQUMsRUFBRUMsSUFBSSxFQUFFQyxNQUFNLEVBQUU7SUFDekYsTUFBTUMsbUJBQW1CLENBQUNGO1FBQ3hCLE9BQVFBO1lBQ04sS0FBSzdCLHFEQUFZQSxDQUFDZ0MsUUFBUTtnQkFDeEIsT0FBTztZQUNULEtBQUtoQyxxREFBWUEsQ0FBQ2lDLFVBQVU7Z0JBQzFCLE9BQU87WUFDVCxLQUFLakMscURBQVlBLENBQUNrQyxJQUFJO2dCQUNwQixPQUFPO1lBQ1QsS0FBS2xDLHFEQUFZQSxDQUFDbUMsVUFBVTtnQkFDMUIsT0FBTztZQUNULEtBQUtuQyxxREFBWUEsQ0FBQ29DLFlBQVk7Z0JBQzVCLE9BQU87WUFDVCxLQUFLcEMscURBQVlBLENBQUNxQyxJQUFJO2dCQUNwQixPQUFPO1lBQ1QsS0FBS3JDLHFEQUFZQSxDQUFDc0MsT0FBTztnQkFDdkIsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTUMsa0JBQWtCLENBQUNWO1FBQ3ZCLE9BQU9BLEtBQUtXLE9BQU8sQ0FBQyxLQUFLLEtBQUtBLE9BQU8sQ0FBQyxTQUFTQyxDQUFBQSxJQUFLQSxFQUFFQyxXQUFXO0lBQ25FO0lBRUEscUJBQ0UsOERBQUNuQjtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0c7Z0JBQUtILFdBQVdSLGdEQUFJQSxDQUFDLFdBQVdlLGlCQUFpQkY7MEJBQy9DVSxnQkFBZ0JWOzs7Ozs7MEJBRW5CLDhEQUFDRjtnQkFBS0gsV0FBVTswQkFDYnZCLDREQUFZQSxDQUFDNkI7Ozs7Ozs7Ozs7OztBQUl0QjtBQUVBLE1BQU1hLGFBQXVCO0lBQzNCLE1BQU0sRUFDSkMsU0FBUyxFQUNUQyxVQUFVLEVBQ1ZDLFVBQVUsRUFDVkMsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLHFCQUFxQixFQUNyQkMsZUFBZSxFQUNmQyxlQUFlLEVBQ2ZDLFNBQVMsRUFDVixHQUFHckQsOERBQVlBO0lBRWhCLE1BQU0sQ0FBQ3NELGtCQUFrQkMsb0JBQW9CLEdBQUd4RCwrQ0FBUUEsQ0FDdEQsSUFBSXlELElBQUk7UUFBQztRQUFjO1FBQWE7S0FBZTtJQUdyRCxNQUFNQyxnQkFBZ0IsQ0FBQ0M7UUFDckIsTUFBTUMsY0FBYyxJQUFJSCxJQUFJRjtRQUM1QixJQUFJSyxZQUFZQyxHQUFHLENBQUNGLFVBQVU7WUFDNUJDLFlBQVlFLE1BQU0sQ0FBQ0g7UUFDckIsT0FBTztZQUNMQyxZQUFZRyxHQUFHLENBQUNKO1FBQ2xCO1FBQ0FILG9CQUFvQkk7SUFDdEI7SUFFQSxNQUFNSSxVQUFVYjtJQUVoQixNQUFNYyxlQUFlO1FBQ25CLE1BQU1DLE9BQU9kO1FBQ2IsTUFBTWUsT0FBTyxJQUFJQyxLQUFLO1lBQUNGO1NBQUssRUFBRTtZQUFFbkMsTUFBTTtRQUFtQjtRQUN6RCxNQUFNc0MsTUFBTUMsSUFBSUMsZUFBZSxDQUFDSjtRQUNoQyxNQUFNSyxJQUFJQyxTQUFTQyxhQUFhLENBQUM7UUFDakNGLEVBQUVHLElBQUksR0FBR047UUFDVEcsRUFBRUksUUFBUSxHQUFHLENBQUMsUUFBUSxFQUFFQyxLQUFLQyxHQUFHLEdBQUcsS0FBSyxDQUFDO1FBQ3pDTixFQUFFTyxLQUFLO1FBQ1BULElBQUlVLGVBQWUsQ0FBQ1g7SUFDdEI7SUFFQSxNQUFNWSxlQUFlO1FBQ25CLE1BQU1DLFFBQVFULFNBQVNDLGFBQWEsQ0FBQztRQUNyQ1EsTUFBTW5ELElBQUksR0FBRztRQUNibUQsTUFBTUMsTUFBTSxHQUFHO1FBQ2ZELE1BQU1FLFFBQVEsR0FBRyxDQUFDQztZQUNoQixNQUFNQyxPQUFPLEVBQUdDLE1BQU0sQ0FBc0JDLEtBQUssRUFBRSxDQUFDLEVBQUU7WUFDdEQsSUFBSUYsTUFBTTtnQkFDUixNQUFNRyxTQUFTLElBQUlDO2dCQUNuQkQsT0FBT0UsTUFBTSxHQUFHLENBQUNOO29CQUNmLE1BQU1PLFVBQVVQLEVBQUVFLE1BQU0sRUFBRU07b0JBQzFCLElBQUl4QyxnQkFBZ0J1QyxVQUFVO3dCQUM1QkUsTUFBTTtvQkFDUixPQUFPO3dCQUNMQSxNQUFNO29CQUNSO2dCQUNGO2dCQUNBTCxPQUFPTSxVQUFVLENBQUNUO1lBQ3BCO1FBQ0Y7UUFDQUosTUFBTUgsS0FBSztJQUNiO0lBRUEsTUFBTWlCLGtCQUFrQkMsTUFBTUMsSUFBSSxDQUFDbEQsV0FBV21ELE1BQU0sSUFBSUMsTUFBTSxDQUFDLENBQUNDLEtBQUtDO1FBQ25FRCxHQUFHLENBQUNDLFVBQVV2RSxJQUFJLENBQUMsR0FBRyxDQUFDc0UsR0FBRyxDQUFDQyxVQUFVdkUsSUFBSSxDQUFDLElBQUksS0FBSztRQUNuRCxPQUFPc0U7SUFDVCxHQUFHLENBQUM7SUFFSixxQkFDRSw4REFBQzVFO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUM2RTt3QkFBRzdFLFdBQVU7a0NBQW9DOzs7Ozs7a0NBQ2xELDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNuQixrTEFBS0E7Z0NBQUNtQixXQUFVOzs7Ozs7MENBQ2pCLDhEQUFDRzs7b0NBQUs7b0NBQVV6QiwwREFBVUEsQ0FBQzZDOzs7Ozs7Ozs7Ozs7O2tDQUU3Qiw4REFBQ3hCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ2xCLG1MQUFHQTtnQ0FBQ2tCLFdBQVU7Ozs7OzswQ0FDZiw4REFBQ0c7Z0NBQUtILFdBQVd3QixZQUFZLG1CQUFtQjswQ0FDN0NBLFlBQVksWUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU0vQiw4REFBQy9CO2dCQUNDQyxPQUFNO2dCQUNOQyxvQkFBTSw4REFBQ2YsbUxBQVNBO29CQUFDb0IsV0FBVTs7Ozs7O2dCQUMzQkgsWUFBWWdDLGlCQUFpQk0sR0FBRyxDQUFDO2dCQUNqQ3JDLFVBQVUsSUFBTWtDLGNBQWM7MEJBRTlCLDRFQUFDakM7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNHO29DQUFLSCxXQUFVOzhDQUF3Qjs7Ozs7OzhDQUN4Qyw4REFBQ0c7b0NBQUtILFdBQVU7O3dDQUNacUIsQ0FBQUEsV0FBV3lELFVBQVUsR0FBRyxHQUFFLEVBQUdDLE9BQU8sQ0FBQzt3Q0FBRzs7Ozs7Ozs7Ozs7Ozt3QkFJN0NSLE1BQU1DLElBQUksQ0FBQ25ELFdBQVcyRCxlQUFlLENBQUNDLE9BQU8sSUFBSUMsR0FBRyxDQUFDLENBQUMsQ0FBQ0MsVUFBVTdFLE9BQU8saUJBQ3ZFLDhEQUFDRjtnQ0FBK0JDLE1BQU04RTtnQ0FBVTdFLFFBQVFBOytCQUFsQzZFOzs7Ozt3QkFHdkI5RCxXQUFXMkQsZUFBZSxDQUFDSSxJQUFJLEtBQUssbUJBQ25DLDhEQUFDQzs0QkFBRXJGLFdBQVU7c0NBQXdCOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFNM0MsOERBQUNQO2dCQUNDQyxPQUFNO2dCQUNOQyxvQkFBTSw4REFBQ0wsbUxBQU9BO29CQUFDVSxXQUFVOzs7Ozs7Z0JBQ3pCSCxZQUFZZ0MsaUJBQWlCTSxHQUFHLENBQUM7Z0JBQ2pDckMsVUFBVSxJQUFNa0MsY0FBYzswQkFFOUIsNEVBQUNqQztvQkFBSUMsV0FBVTs4QkFDWnVFLE1BQU1DLElBQUksQ0FBQ3BELFVBQVU2RCxPQUFPLElBQUlDLEdBQUcsQ0FBQyxDQUFDLENBQUNDLFVBQVU3RSxPQUFPLGlCQUN0RCw4REFBQ0Y7NEJBQStCQyxNQUFNOEU7NEJBQVU3RSxRQUFRQTsyQkFBbEM2RTs7Ozs7Ozs7Ozs7Ozs7OzBCQU01Qiw4REFBQzFGO2dCQUNDQyxPQUFNO2dCQUNOQyxvQkFBTSw4REFBQ04sbUxBQUdBO29CQUFDVyxXQUFVOzs7Ozs7Z0JBQ3JCSCxZQUFZZ0MsaUJBQWlCTSxHQUFHLENBQUM7Z0JBQ2pDckMsVUFBVSxJQUFNa0MsY0FBYzswQkFFOUIsNEVBQUNqQztvQkFBSUMsV0FBVTs7d0JBQ1pzRixPQUFPTCxPQUFPLENBQUNYLGlCQUFpQlksR0FBRyxDQUFDLENBQUMsQ0FBQzdFLE1BQU1rRixNQUFNLGlCQUNqRCw4REFBQ3hGO2dDQUFlQyxXQUFVOztrREFDeEIsOERBQUNHO3dDQUFLSCxXQUFVO2tEQUNiSyxLQUFLVyxPQUFPLENBQUMsS0FBSzs7Ozs7O2tEQUVyQiw4REFBQ2I7d0NBQUtILFdBQVU7a0RBQWdDdUY7Ozs7Ozs7K0JBSnhDbEY7Ozs7O3dCQU9YaUYsT0FBT0UsSUFBSSxDQUFDbEIsaUJBQWlCbUIsTUFBTSxLQUFLLG1CQUN2Qyw4REFBQ0o7NEJBQUVyRixXQUFVO3NDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFNMUNxQixXQUFXcUUsV0FBVyxDQUFDRCxNQUFNLEdBQUcsbUJBQy9CLDhEQUFDaEc7Z0JBQ0NDLE9BQU07Z0JBQ05DLG9CQUFNLDhEQUFDWixtTEFBYUE7b0JBQUNpQixXQUFVOzs7Ozs7Z0JBQy9CSCxZQUFZZ0MsaUJBQWlCTSxHQUFHLENBQUM7Z0JBQ2pDckMsVUFBVSxJQUFNa0MsY0FBYzswQkFFOUIsNEVBQUNqQztvQkFBSUMsV0FBVTs4QkFDWnFCLFdBQVdxRSxXQUFXLENBQUNSLEdBQUcsQ0FBQyxDQUFDUyxhQUFhQyxzQkFDeEMsOERBQUM3Rjs0QkFBc0JDLFdBQVU7O2dDQUF1QjtnQ0FDM0M0RixRQUFRO2dDQUFFO2dDQUFHRCxZQUFZRSxLQUFLLENBQUMsR0FBRztnQ0FBRzs7MkJBRHhDRjs7Ozs7Ozs7Ozs7Ozs7OzBCQVNsQiw4REFBQ2xHO2dCQUNDQyxPQUFNO2dCQUNOQyxvQkFBTSw4REFBQ0osbUxBQUtBO29CQUFDUyxXQUFVOzs7Ozs7Z0JBQ3ZCSCxZQUFZZ0MsaUJBQWlCTSxHQUFHLENBQUM7Z0JBQ2pDckMsVUFBVSxJQUFNa0MsY0FBYzswQkFFOUIsNEVBQUNyRCxvREFBV0E7Ozs7Ozs7Ozs7MEJBSWQsOERBQUNvQjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUM4Rjt3QkFBRzlGLFdBQVU7a0NBQWdDOzs7Ozs7a0NBRTlDLDhEQUFDQzt3QkFDQ0MsU0FBU3FDO3dCQUNUdkMsV0FBVTs7MENBRVYsOERBQUNkLG1MQUFRQTtnQ0FBQ2MsV0FBVTs7Ozs7OzRCQUFZOzs7Ozs7O2tDQUlsQyw4REFBQ0M7d0JBQ0NDLFNBQVNxRDt3QkFDVHZELFdBQVU7OzBDQUVWLDhEQUFDYixtTEFBTUE7Z0NBQUNhLFdBQVU7Ozs7Ozs0QkFBWTs7Ozs7OztrQ0FJaEMsOERBQUNDO3dCQUNDQyxTQUFTOzRCQUNQLElBQUk2RixRQUFRLHVFQUF1RTtnQ0FDakZuRTs0QkFDRjt3QkFDRjt3QkFDQTVCLFdBQVU7OzBDQUVWLDhEQUFDWixtTEFBU0E7Z0NBQUNZLFdBQVU7Ozs7Ozs0QkFBWTs7Ozs7Ozs7Ozs7OzswQkFNckMsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQzhGO3dCQUFHOUYsV0FBVTtrQ0FBZ0M7Ozs7OztrQ0FDOUMsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O29DQUFJO29DQUFhdUIsV0FBVzhELElBQUk7Ozs7Ozs7MENBQ2pDLDhEQUFDckY7O29DQUFJO29DQUFTd0UsTUFBTUMsSUFBSSxDQUFDbEQsV0FBV21ELE1BQU0sSUFBSXVCLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsUUFBUSxFQUFFVCxNQUFNOzs7Ozs7OzBDQUM1RSw4REFBQzFGOztvQ0FBSTtvQ0FBY3VDLENBQUFBLFFBQVF3QyxVQUFVLEdBQUcsR0FBRSxFQUFHQyxPQUFPLENBQUM7b0NBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLbEU7QUFFQSxpRUFBZTVELFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcc3JjXFxjb21wb25lbnRzXFxTdGF0c1BhbmVsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUdhbWVTdG9yZSB9IGZyb20gJ0Avc3RvcmUvZ2FtZVN0b3JlJztcbmltcG9ydCB7IFJlc291cmNlVHlwZSwgQ29tcG9uZW50VHlwZSB9IGZyb20gJ0AvdHlwZXMvZ2FtZSc7XG5pbXBvcnQgeyBmb3JtYXROdW1iZXIsIGZvcm1hdFRpbWUgfSBmcm9tICdAL3V0aWxzL2hlbHBlcnMnO1xuaW1wb3J0IEFJQXNzaXN0YW50IGZyb20gJy4vQUlBc3Npc3RhbnQnO1xuaW1wb3J0IHtcbiAgQmFyQ2hhcnQzLFxuICBDbG9jayxcbiAgWmFwLFxuICBBbGVydFRyaWFuZ2xlLFxuICBDaGV2cm9uRG93bixcbiAgQ2hldnJvblJpZ2h0LFxuICBEb3dubG9hZCxcbiAgVXBsb2FkLFxuICBSb3RhdGVDY3csXG4gIENvZyxcbiAgUGFja2FnZSxcbiAgQnJhaW5cbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCBjbHN4IGZyb20gJ2Nsc3gnO1xuXG5pbnRlcmZhY2UgU3RhdHNTZWN0aW9uUHJvcHMge1xuICB0aXRsZTogc3RyaW5nO1xuICBpY29uOiBSZWFjdC5SZWFjdE5vZGU7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIGlzRXhwYW5kZWQ6IGJvb2xlYW47XG4gIG9uVG9nZ2xlOiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBTdGF0c1NlY3Rpb246IFJlYWN0LkZDPFN0YXRzU2VjdGlvblByb3BzPiA9ICh7IFxuICB0aXRsZSwgXG4gIGljb24sIFxuICBjaGlsZHJlbiwgXG4gIGlzRXhwYW5kZWQsIFxuICBvblRvZ2dsZSBcbn0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgIDxidXR0b25cbiAgICAgICAgb25DbGljaz17b25Ub2dnbGV9XG4gICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIGJnLWdyYXktNzAwIGhvdmVyOmJnLWdyYXktNjAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAge2ljb259XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+e3RpdGxlfTwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIHtpc0V4cGFuZGVkID8gKFxuICAgICAgICAgIDxDaGV2cm9uRG93biBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS0zMDBcIiAvPlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktMzAwXCIgLz5cbiAgICAgICAgKX1cbiAgICAgIDwvYnV0dG9uPlxuICAgICAgXG4gICAgICB7aXNFeHBhbmRlZCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMiBwLTMgYmctZ3JheS04MDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuY29uc3QgUmVzb3VyY2VEaXNwbGF5OiBSZWFjdC5GQzx7IHR5cGU6IFJlc291cmNlVHlwZTsgYW1vdW50OiBudW1iZXIgfT4gPSAoeyB0eXBlLCBhbW91bnQgfSkgPT4ge1xuICBjb25zdCBnZXRSZXNvdXJjZUNvbG9yID0gKHR5cGU6IFJlc291cmNlVHlwZSkgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSBSZXNvdXJjZVR5cGUuSVJPTl9PUkU6XG4gICAgICAgIHJldHVybiAndGV4dC1ncmF5LTQwMCc7XG4gICAgICBjYXNlIFJlc291cmNlVHlwZS5DT1BQRVJfT1JFOlxuICAgICAgICByZXR1cm4gJ3RleHQtb3JhbmdlLTQwMCc7XG4gICAgICBjYXNlIFJlc291cmNlVHlwZS5DT0FMOlxuICAgICAgICByZXR1cm4gJ3RleHQtZ3JheS02MDAnO1xuICAgICAgY2FzZSBSZXNvdXJjZVR5cGUuSVJPTl9QTEFURTpcbiAgICAgICAgcmV0dXJuICd0ZXh0LWdyYXktMzAwJztcbiAgICAgIGNhc2UgUmVzb3VyY2VUeXBlLkNPUFBFUl9QTEFURTpcbiAgICAgICAgcmV0dXJuICd0ZXh0LW9yYW5nZS0zMDAnO1xuICAgICAgY2FzZSBSZXNvdXJjZVR5cGUuR0VBUjpcbiAgICAgICAgcmV0dXJuICd0ZXh0LXllbGxvdy00MDAnO1xuICAgICAgY2FzZSBSZXNvdXJjZVR5cGUuQ0lSQ1VJVDpcbiAgICAgICAgcmV0dXJuICd0ZXh0LWdyZWVuLTQwMCc7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ3RleHQtd2hpdGUnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRSZXNvdXJjZU5hbWUgPSAodHlwZTogUmVzb3VyY2VUeXBlKSA9PiB7XG4gICAgcmV0dXJuIHR5cGUucmVwbGFjZSgnXycsICcgJykucmVwbGFjZSgvXFxiXFx3L2csIGwgPT4gbC50b1VwcGVyQ2FzZSgpKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHB5LTFcIj5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT17Y2xzeCgndGV4dC1zbScsIGdldFJlc291cmNlQ29sb3IodHlwZSkpfT5cbiAgICAgICAge2dldFJlc291cmNlTmFtZSh0eXBlKX1cbiAgICAgIDwvc3Bhbj5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tb25vIHRleHQtc21cIj5cbiAgICAgICAge2Zvcm1hdE51bWJlcihhbW91bnQpfVxuICAgICAgPC9zcGFuPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuY29uc3QgU3RhdHNQYW5lbDogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIGNvbnN0IHtcbiAgICByZXNvdXJjZXMsXG4gICAgc3RhdGlzdGljcyxcbiAgICBjb21wb25lbnRzLFxuICAgIGdhbWVUaW1lLFxuICAgIGlzUnVubmluZyxcbiAgICBnZXRQZXJmb3JtYW5jZU1ldHJpY3MsXG4gICAgZXhwb3J0R2FtZVN0YXRlLFxuICAgIGltcG9ydEdhbWVTdGF0ZSxcbiAgICByZXNldEdhbWUsXG4gIH0gPSB1c2VHYW1lU3RvcmUoKTtcblxuICBjb25zdCBbZXhwYW5kZWRTZWN0aW9ucywgc2V0RXhwYW5kZWRTZWN0aW9uc10gPSB1c2VTdGF0ZTxTZXQ8c3RyaW5nPj4oXG4gICAgbmV3IFNldChbJ1Byb2R1Y3Rpb24nLCAnUmVzb3VyY2VzJywgJ0FJIEFzc2lzdGFudCddKVxuICApO1xuXG4gIGNvbnN0IHRvZ2dsZVNlY3Rpb24gPSAoc2VjdGlvbjogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgbmV3RXhwYW5kZWQgPSBuZXcgU2V0KGV4cGFuZGVkU2VjdGlvbnMpO1xuICAgIGlmIChuZXdFeHBhbmRlZC5oYXMoc2VjdGlvbikpIHtcbiAgICAgIG5ld0V4cGFuZGVkLmRlbGV0ZShzZWN0aW9uKTtcbiAgICB9IGVsc2Uge1xuICAgICAgbmV3RXhwYW5kZWQuYWRkKHNlY3Rpb24pO1xuICAgIH1cbiAgICBzZXRFeHBhbmRlZFNlY3Rpb25zKG5ld0V4cGFuZGVkKTtcbiAgfTtcblxuICBjb25zdCBtZXRyaWNzID0gZ2V0UGVyZm9ybWFuY2VNZXRyaWNzKCk7XG5cbiAgY29uc3QgaGFuZGxlRXhwb3J0ID0gKCkgPT4ge1xuICAgIGNvbnN0IGRhdGEgPSBleHBvcnRHYW1lU3RhdGUoKTtcbiAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2RhdGFdLCB7IHR5cGU6ICdhcHBsaWNhdGlvbi9qc29uJyB9KTtcbiAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xuICAgIGNvbnN0IGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7XG4gICAgYS5ocmVmID0gdXJsO1xuICAgIGEuZG93bmxvYWQgPSBgZmFjdG9yeS0ke0RhdGUubm93KCl9Lmpzb25gO1xuICAgIGEuY2xpY2soKTtcbiAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVybCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW1wb3J0ID0gKCkgPT4ge1xuICAgIGNvbnN0IGlucHV0ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnaW5wdXQnKTtcbiAgICBpbnB1dC50eXBlID0gJ2ZpbGUnO1xuICAgIGlucHV0LmFjY2VwdCA9ICcuanNvbic7XG4gICAgaW5wdXQub25jaGFuZ2UgPSAoZSkgPT4ge1xuICAgICAgY29uc3QgZmlsZSA9IChlLnRhcmdldCBhcyBIVE1MSW5wdXRFbGVtZW50KS5maWxlcz8uWzBdO1xuICAgICAgaWYgKGZpbGUpIHtcbiAgICAgICAgY29uc3QgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTtcbiAgICAgICAgcmVhZGVyLm9ubG9hZCA9IChlKSA9PiB7XG4gICAgICAgICAgY29uc3QgY29udGVudCA9IGUudGFyZ2V0Py5yZXN1bHQgYXMgc3RyaW5nO1xuICAgICAgICAgIGlmIChpbXBvcnRHYW1lU3RhdGUoY29udGVudCkpIHtcbiAgICAgICAgICAgIGFsZXJ0KCdGYWN0b3J5IGltcG9ydGVkIHN1Y2Nlc3NmdWxseSEnKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgYWxlcnQoJ0ZhaWxlZCB0byBpbXBvcnQgZmFjdG9yeS4gUGxlYXNlIGNoZWNrIHRoZSBmaWxlIGZvcm1hdC4nKTtcbiAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIHJlYWRlci5yZWFkQXNUZXh0KGZpbGUpO1xuICAgICAgfVxuICAgIH07XG4gICAgaW5wdXQuY2xpY2soKTtcbiAgfTtcblxuICBjb25zdCBjb21wb25lbnRDb3VudHMgPSBBcnJheS5mcm9tKGNvbXBvbmVudHMudmFsdWVzKCkpLnJlZHVjZSgoYWNjLCBjb21wb25lbnQpID0+IHtcbiAgICBhY2NbY29tcG9uZW50LnR5cGVdID0gKGFjY1tjb21wb25lbnQudHlwZV0gfHwgMCkgKyAxO1xuICAgIHJldHVybiBhY2M7XG4gIH0sIHt9IGFzIFJlY29yZDxDb21wb25lbnRUeXBlLCBudW1iZXI+KTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIHAtNCBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+RmFjdG9yeSBTdGF0czwvaDI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1zbSB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgIDxzcGFuPlJ1bnRpbWU6IHtmb3JtYXRUaW1lKGdhbWVUaW1lKX08L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gdGV4dC1ncmF5LTMwMCBtdC0xXCI+XG4gICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2lzUnVubmluZyA/ICd0ZXh0LWdyZWVuLTQwMCcgOiAndGV4dC1yZWQtNDAwJ30+XG4gICAgICAgICAgICB7aXNSdW5uaW5nID8gJ1J1bm5pbmcnIDogJ1BhdXNlZCd9XG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUHJvZHVjdGlvbiBTdGF0cyAqL31cbiAgICAgIDxTdGF0c1NlY3Rpb25cbiAgICAgICAgdGl0bGU9XCJQcm9kdWN0aW9uXCJcbiAgICAgICAgaWNvbj17PEJhckNoYXJ0MyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtYmx1ZS00MDBcIiAvPn1cbiAgICAgICAgaXNFeHBhbmRlZD17ZXhwYW5kZWRTZWN0aW9ucy5oYXMoJ1Byb2R1Y3Rpb24nKX1cbiAgICAgICAgb25Ub2dnbGU9eygpID0+IHRvZ2dsZVNlY3Rpb24oJ1Byb2R1Y3Rpb24nKX1cbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktMzAwXCI+RWZmaWNpZW5jeTwvc3Bhbj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tb25vIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgeyhzdGF0aXN0aWNzLmVmZmljaWVuY3kgKiAxMDApLnRvRml4ZWQoMSl9JVxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIHtBcnJheS5mcm9tKHN0YXRpc3RpY3MudG90YWxQcm9kdWN0aW9uLmVudHJpZXMoKSkubWFwKChbcmVzb3VyY2UsIGFtb3VudF0pID0+IChcbiAgICAgICAgICAgIDxSZXNvdXJjZURpc3BsYXkga2V5PXtyZXNvdXJjZX0gdHlwZT17cmVzb3VyY2V9IGFtb3VudD17YW1vdW50fSAvPlxuICAgICAgICAgICkpfVxuICAgICAgICAgIFxuICAgICAgICAgIHtzdGF0aXN0aWNzLnRvdGFsUHJvZHVjdGlvbi5zaXplID09PSAwICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPk5vIHByb2R1Y3Rpb24gZGF0YSB5ZXQ8L3A+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L1N0YXRzU2VjdGlvbj5cblxuICAgICAgey8qIFJlc291cmNlcyAqL31cbiAgICAgIDxTdGF0c1NlY3Rpb25cbiAgICAgICAgdGl0bGU9XCJSZXNvdXJjZXNcIlxuICAgICAgICBpY29uPXs8UGFja2FnZSBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JlZW4tNDAwXCIgLz59XG4gICAgICAgIGlzRXhwYW5kZWQ9e2V4cGFuZGVkU2VjdGlvbnMuaGFzKCdSZXNvdXJjZXMnKX1cbiAgICAgICAgb25Ub2dnbGU9eygpID0+IHRvZ2dsZVNlY3Rpb24oJ1Jlc291cmNlcycpfVxuICAgICAgPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICAgIHtBcnJheS5mcm9tKHJlc291cmNlcy5lbnRyaWVzKCkpLm1hcCgoW3Jlc291cmNlLCBhbW91bnRdKSA9PiAoXG4gICAgICAgICAgICA8UmVzb3VyY2VEaXNwbGF5IGtleT17cmVzb3VyY2V9IHR5cGU9e3Jlc291cmNlfSBhbW91bnQ9e2Ftb3VudH0gLz5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L1N0YXRzU2VjdGlvbj5cblxuICAgICAgey8qIENvbXBvbmVudHMgKi99XG4gICAgICA8U3RhdHNTZWN0aW9uXG4gICAgICAgIHRpdGxlPVwiQ29tcG9uZW50c1wiXG4gICAgICAgIGljb249ezxDb2cgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXB1cnBsZS00MDBcIiAvPn1cbiAgICAgICAgaXNFeHBhbmRlZD17ZXhwYW5kZWRTZWN0aW9ucy5oYXMoJ0NvbXBvbmVudHMnKX1cbiAgICAgICAgb25Ub2dnbGU9eygpID0+IHRvZ2dsZVNlY3Rpb24oJ0NvbXBvbmVudHMnKX1cbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICB7T2JqZWN0LmVudHJpZXMoY29tcG9uZW50Q291bnRzKS5tYXAoKFt0eXBlLCBjb3VudF0pID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXt0eXBlfSBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgcHktMVwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS0zMDAgY2FwaXRhbGl6ZVwiPlxuICAgICAgICAgICAgICAgIHt0eXBlLnJlcGxhY2UoJ18nLCAnICcpfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tb25vIHRleHQtc21cIj57Y291bnR9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgICAge09iamVjdC5rZXlzKGNvbXBvbmVudENvdW50cykubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPk5vIGNvbXBvbmVudHMgcGxhY2VkPC9wPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9TdGF0c1NlY3Rpb24+XG5cbiAgICAgIHsvKiBCb3R0bGVuZWNrcyAqL31cbiAgICAgIHtzdGF0aXN0aWNzLmJvdHRsZW5lY2tzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICA8U3RhdHNTZWN0aW9uXG4gICAgICAgICAgdGl0bGU9XCJCb3R0bGVuZWNrc1wiXG4gICAgICAgICAgaWNvbj17PEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXJlZC00MDBcIiAvPn1cbiAgICAgICAgICBpc0V4cGFuZGVkPXtleHBhbmRlZFNlY3Rpb25zLmhhcygnQm90dGxlbmVja3MnKX1cbiAgICAgICAgICBvblRvZ2dsZT17KCkgPT4gdG9nZ2xlU2VjdGlvbignQm90dGxlbmVja3MnKX1cbiAgICAgICAgPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XG4gICAgICAgICAgICB7c3RhdGlzdGljcy5ib3R0bGVuZWNrcy5tYXAoKGNvbXBvbmVudElkLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2IGtleT17Y29tcG9uZW50SWR9IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtMzAwXCI+XG4gICAgICAgICAgICAgICAgQ29tcG9uZW50IHtpbmRleCArIDF9OiB7Y29tcG9uZW50SWQuc2xpY2UoMCwgOCl9Li4uXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvU3RhdHNTZWN0aW9uPlxuICAgICAgKX1cblxuICAgICAgey8qIEFJIEFzc2lzdGFudCAqL31cbiAgICAgIDxTdGF0c1NlY3Rpb25cbiAgICAgICAgdGl0bGU9XCJBSSBBc3Npc3RhbnRcIlxuICAgICAgICBpY29uPXs8QnJhaW4gY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXB1cnBsZS00MDBcIiAvPn1cbiAgICAgICAgaXNFeHBhbmRlZD17ZXhwYW5kZWRTZWN0aW9ucy5oYXMoJ0FJIEFzc2lzdGFudCcpfVxuICAgICAgICBvblRvZ2dsZT17KCkgPT4gdG9nZ2xlU2VjdGlvbignQUkgQXNzaXN0YW50Jyl9XG4gICAgICA+XG4gICAgICAgIDxBSUFzc2lzdGFudCAvPlxuICAgICAgPC9TdGF0c1NlY3Rpb24+XG5cbiAgICAgIHsvKiBBY3Rpb25zICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04IHNwYWNlLXktMlwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTNcIj5BY3Rpb25zPC9oMz5cbiAgICAgICAgXG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVFeHBvcnR9XG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHAtMiBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICA+XG4gICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgIEV4cG9ydCBGYWN0b3J5XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgICBcbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUltcG9ydH1cbiAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcC0yIGJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDAgdGV4dC13aGl0ZSByb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxVcGxvYWQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgSW1wb3J0IEZhY3RvcnlcbiAgICAgICAgPC9idXR0b24+XG4gICAgICAgIFxuICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgaWYgKGNvbmZpcm0oJ0FyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byByZXNldCB0aGUgZmFjdG9yeT8gVGhpcyBjYW5ub3QgYmUgdW5kb25lLicpKSB7XG4gICAgICAgICAgICAgIHJlc2V0R2FtZSgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHAtMiBiZy1yZWQtNjAwIGhvdmVyOmJnLXJlZC03MDAgdGV4dC13aGl0ZSByb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxSb3RhdGVDY3cgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgUmVzZXQgRmFjdG9yeVxuICAgICAgICA8L2J1dHRvbj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUGVyZm9ybWFuY2UgTWV0cmljcyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBwLTMgYmctZ3JheS04MDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5QZXJmb3JtYW5jZTwvaDM+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktMzAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgIDxkaXY+Q29tcG9uZW50czoge2NvbXBvbmVudHMuc2l6ZX08L2Rpdj5cbiAgICAgICAgICA8ZGl2PkFjdGl2ZToge0FycmF5LmZyb20oY29tcG9uZW50cy52YWx1ZXMoKSkuZmlsdGVyKGMgPT4gYy5pc0FjdGl2ZSkubGVuZ3RofTwvZGl2PlxuICAgICAgICAgIDxkaXY+RWZmaWNpZW5jeTogeyhtZXRyaWNzLmVmZmljaWVuY3kgKiAxMDApLnRvRml4ZWQoMSl9JTwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgU3RhdHNQYW5lbDtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlR2FtZVN0b3JlIiwiUmVzb3VyY2VUeXBlIiwiZm9ybWF0TnVtYmVyIiwiZm9ybWF0VGltZSIsIkFJQXNzaXN0YW50IiwiQmFyQ2hhcnQzIiwiQ2xvY2siLCJaYXAiLCJBbGVydFRyaWFuZ2xlIiwiQ2hldnJvbkRvd24iLCJDaGV2cm9uUmlnaHQiLCJEb3dubG9hZCIsIlVwbG9hZCIsIlJvdGF0ZUNjdyIsIkNvZyIsIlBhY2thZ2UiLCJCcmFpbiIsImNsc3giLCJTdGF0c1NlY3Rpb24iLCJ0aXRsZSIsImljb24iLCJjaGlsZHJlbiIsImlzRXhwYW5kZWQiLCJvblRvZ2dsZSIsImRpdiIsImNsYXNzTmFtZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzcGFuIiwiUmVzb3VyY2VEaXNwbGF5IiwidHlwZSIsImFtb3VudCIsImdldFJlc291cmNlQ29sb3IiLCJJUk9OX09SRSIsIkNPUFBFUl9PUkUiLCJDT0FMIiwiSVJPTl9QTEFURSIsIkNPUFBFUl9QTEFURSIsIkdFQVIiLCJDSVJDVUlUIiwiZ2V0UmVzb3VyY2VOYW1lIiwicmVwbGFjZSIsImwiLCJ0b1VwcGVyQ2FzZSIsIlN0YXRzUGFuZWwiLCJyZXNvdXJjZXMiLCJzdGF0aXN0aWNzIiwiY29tcG9uZW50cyIsImdhbWVUaW1lIiwiaXNSdW5uaW5nIiwiZ2V0UGVyZm9ybWFuY2VNZXRyaWNzIiwiZXhwb3J0R2FtZVN0YXRlIiwiaW1wb3J0R2FtZVN0YXRlIiwicmVzZXRHYW1lIiwiZXhwYW5kZWRTZWN0aW9ucyIsInNldEV4cGFuZGVkU2VjdGlvbnMiLCJTZXQiLCJ0b2dnbGVTZWN0aW9uIiwic2VjdGlvbiIsIm5ld0V4cGFuZGVkIiwiaGFzIiwiZGVsZXRlIiwiYWRkIiwibWV0cmljcyIsImhhbmRsZUV4cG9ydCIsImRhdGEiLCJibG9iIiwiQmxvYiIsInVybCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImEiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJEYXRlIiwibm93IiwiY2xpY2siLCJyZXZva2VPYmplY3RVUkwiLCJoYW5kbGVJbXBvcnQiLCJpbnB1dCIsImFjY2VwdCIsIm9uY2hhbmdlIiwiZSIsImZpbGUiLCJ0YXJnZXQiLCJmaWxlcyIsInJlYWRlciIsIkZpbGVSZWFkZXIiLCJvbmxvYWQiLCJjb250ZW50IiwicmVzdWx0IiwiYWxlcnQiLCJyZWFkQXNUZXh0IiwiY29tcG9uZW50Q291bnRzIiwiQXJyYXkiLCJmcm9tIiwidmFsdWVzIiwicmVkdWNlIiwiYWNjIiwiY29tcG9uZW50IiwiaDIiLCJlZmZpY2llbmN5IiwidG9GaXhlZCIsInRvdGFsUHJvZHVjdGlvbiIsImVudHJpZXMiLCJtYXAiLCJyZXNvdXJjZSIsInNpemUiLCJwIiwiT2JqZWN0IiwiY291bnQiLCJrZXlzIiwibGVuZ3RoIiwiYm90dGxlbmVja3MiLCJjb21wb25lbnRJZCIsImluZGV4Iiwic2xpY2UiLCJoMyIsImNvbmZpcm0iLCJmaWx0ZXIiLCJjIiwiaXNBY3RpdmUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StatsPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/engine/simulation.ts":
/*!**********************************!*\
  !*** ./src/engine/simulation.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimulationEngine: () => (/* binding */ SimulationEngine)\n/* harmony export */ });\n/* harmony import */ var _types_game__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/game */ \"(ssr)/./src/types/game.ts\");\n\nclass SimulationEngine {\n    constructor(){\n        this.lastUpdateTime = 0;\n        this.UPDATE_INTERVAL = 100 // 100ms updates\n        ;\n        this.lastUpdateTime = Date.now();\n    }\n    updateSimulation(gameState) {\n        const currentTime = Date.now();\n        const deltaTime = currentTime - this.lastUpdateTime;\n        if (deltaTime < this.UPDATE_INTERVAL) {\n            return {};\n        }\n        this.lastUpdateTime = currentTime;\n        const updatedComponents = new Map(gameState.components);\n        const updatedResources = new Map(gameState.resources);\n        const productionStats = new Map();\n        const consumptionStats = new Map();\n        // Process each component\n        for (const [id, component] of updatedComponents){\n            const updatedComponent = this.processComponent(component, updatedComponents, deltaTime, productionStats, consumptionStats);\n            updatedComponents.set(id, updatedComponent);\n        }\n        // Update global resources (for miners)\n        this.updateGlobalResources(updatedComponents, updatedResources, deltaTime);\n        // Calculate performance metrics\n        const metrics = this.calculatePerformanceMetrics(updatedComponents, productionStats, consumptionStats);\n        return {\n            components: updatedComponents,\n            resources: updatedResources,\n            gameTime: gameState.gameTime + deltaTime,\n            statistics: {\n                totalProduction: productionStats,\n                totalConsumption: consumptionStats,\n                efficiency: metrics.efficiency,\n                bottlenecks: metrics.bottlenecks\n            }\n        };\n    }\n    processComponent(component, allComponents, deltaTime, productionStats, consumptionStats) {\n        const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n        const updatedComponent = {\n            ...component\n        };\n        switch(component.type){\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MINER:\n                this.processMiner(updatedComponent, deltaTime, productionStats);\n                break;\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.ASSEMBLER:\n                this.processAssembler(updatedComponent, deltaTime, productionStats, consumptionStats);\n                break;\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR:\n                this.processConveyor(updatedComponent, allComponents, deltaTime);\n                break;\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.STORAGE:\n                this.processStorage(updatedComponent, allComponents, deltaTime);\n                break;\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.SPLITTER:\n                this.processSplitter(updatedComponent, allComponents, deltaTime);\n                break;\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MERGER:\n                this.processMerger(updatedComponent, allComponents, deltaTime);\n                break;\n        }\n        return updatedComponent;\n    }\n    processMiner(component, deltaTime, productionStats) {\n        const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n        const timeSinceLastProcess = Date.now() - component.lastProcessTime;\n        const processInterval = 1000 / definition.speed; // Convert speed to interval\n        if (timeSinceLastProcess >= processInterval) {\n            // For simplicity, miners produce iron ore\n            const resourceType = _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.IRON_ORE;\n            const currentAmount = component.inventory.get(resourceType) || 0;\n            const maxStorage = 50; // Max items a miner can store\n            if (currentAmount < maxStorage) {\n                component.inventory.set(resourceType, currentAmount + 1);\n                component.lastProcessTime = Date.now();\n                component.isActive = true;\n                // Update production stats\n                const currentProduction = productionStats.get(resourceType) || 0;\n                productionStats.set(resourceType, currentProduction + 1);\n            } else {\n                component.isActive = false; // Storage full\n            }\n        }\n    }\n    processAssembler(component, deltaTime, productionStats, consumptionStats) {\n        if (!component.recipe) {\n            component.isActive = false;\n            return;\n        }\n        const timeSinceLastProcess = Date.now() - component.lastProcessTime;\n        if (timeSinceLastProcess >= component.recipe.processingTime) {\n            // Check if we have enough inputs\n            const canProcess = component.recipe.inputs.every((input)=>{\n                const available = component.inventory.get(input.resource) || 0;\n                return available >= input.amount;\n            });\n            if (canProcess) {\n                // Consume inputs\n                component.recipe.inputs.forEach((input)=>{\n                    const current = component.inventory.get(input.resource) || 0;\n                    component.inventory.set(input.resource, current - input.amount);\n                    // Update consumption stats\n                    const currentConsumption = consumptionStats.get(input.resource) || 0;\n                    consumptionStats.set(input.resource, currentConsumption + input.amount);\n                });\n                // Produce outputs\n                component.recipe.outputs.forEach((output)=>{\n                    const current = component.inventory.get(output.resource) || 0;\n                    component.inventory.set(output.resource, current + output.amount);\n                    // Update production stats\n                    const currentProduction = productionStats.get(output.resource) || 0;\n                    productionStats.set(output.resource, currentProduction + output.amount);\n                });\n                component.lastProcessTime = Date.now();\n                component.isActive = true;\n            } else {\n                component.isActive = false; // Waiting for inputs\n            }\n        }\n    }\n    processConveyor(component, allComponents, deltaTime) {\n        const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n        const transferRate = definition.speed * (deltaTime / 1000); // Items per update\n        // Try to move items to connected outputs\n        component.connections.outputs.forEach((outputId)=>{\n            const outputComponent = allComponents.get(outputId);\n            if (!outputComponent) return;\n            // Transfer items from this component to the output\n            for (const [resourceType, amount] of component.inventory){\n                if (amount > 0) {\n                    const transferAmount = Math.min(amount, Math.floor(transferRate));\n                    if (transferAmount > 0) {\n                        const outputCurrent = outputComponent.inventory.get(resourceType) || 0;\n                        const maxCapacity = this.getComponentCapacity(outputComponent);\n                        const canAccept = Math.min(transferAmount, maxCapacity - outputCurrent);\n                        if (canAccept > 0) {\n                            component.inventory.set(resourceType, amount - canAccept);\n                            outputComponent.inventory.set(resourceType, outputCurrent + canAccept);\n                            component.isActive = true;\n                        }\n                    }\n                    break; // Only transfer one resource type per update\n                }\n            }\n        });\n    }\n    processStorage(component, allComponents, deltaTime) {\n        // Storage just holds items, but can pass them through\n        this.processConveyor(component, allComponents, deltaTime);\n    }\n    processSplitter(component, allComponents, deltaTime) {\n        const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n        const transferRate = definition.speed * (deltaTime / 1000);\n        // Split items evenly between outputs\n        const outputs = component.connections.outputs.map((id)=>allComponents.get(id)).filter((comp)=>comp !== undefined);\n        if (outputs.length === 0) return;\n        for (const [resourceType, amount] of component.inventory){\n            if (amount > 0) {\n                const transferAmount = Math.min(amount, Math.floor(transferRate));\n                const perOutput = Math.floor(transferAmount / outputs.length);\n                if (perOutput > 0) {\n                    outputs.forEach((output)=>{\n                        const outputCurrent = output.inventory.get(resourceType) || 0;\n                        const maxCapacity = this.getComponentCapacity(output);\n                        const canAccept = Math.min(perOutput, maxCapacity - outputCurrent);\n                        if (canAccept > 0) {\n                            const currentAmount = component.inventory.get(resourceType) || 0;\n                            component.inventory.set(resourceType, currentAmount - canAccept);\n                            output.inventory.set(resourceType, outputCurrent + canAccept);\n                            component.isActive = true;\n                        }\n                    });\n                }\n                break; // Only process one resource type per update\n            }\n        }\n    }\n    processMerger(component, allComponents, deltaTime) {\n        // Merger just passes items through like a conveyor\n        this.processConveyor(component, allComponents, deltaTime);\n    }\n    getComponentCapacity(component) {\n        // Return max capacity for different component types\n        switch(component.type){\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.STORAGE:\n                return 1000;\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.ASSEMBLER:\n                return 100;\n            case _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MINER:\n                return 50;\n            default:\n                return 10; // Default for conveyors, splitters, etc.\n        }\n    }\n    updateGlobalResources(components, resources, deltaTime) {\n    // This could be used for global resource depletion, but for now we keep it simple\n    }\n    calculatePerformanceMetrics(components, productionStats, consumptionStats) {\n        const throughput = new Map();\n        const utilization = new Map();\n        const bottlenecks = [];\n        // Calculate throughput (items per minute)\n        for (const [resource, amount] of productionStats){\n            throughput.set(resource, amount * 600); // Convert to per minute\n        }\n        // Calculate utilization for each component\n        let totalUtilization = 0;\n        let activeComponents = 0;\n        for (const [id, component] of components){\n            const util = component.isActive ? 100 : 0;\n            utilization.set(id, util);\n            totalUtilization += util;\n            activeComponents++;\n            // Identify bottlenecks (components that are inactive due to full outputs or empty inputs)\n            if (!component.isActive && component.type !== _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.STORAGE) {\n                bottlenecks.push(id);\n            }\n        }\n        const efficiency = activeComponents > 0 ? totalUtilization / activeComponents / 100 : 0;\n        return {\n            throughput,\n            utilization,\n            bottlenecks,\n            efficiency\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/engine/simulation.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/gameStore.ts":
/*!********************************!*\
  !*** ./src/store/gameStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGameStore: () => (/* binding */ useGameStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _types_game__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/game */ \"(ssr)/./src/types/game.ts\");\n/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/helpers */ \"(ssr)/./src/utils/helpers.ts\");\n/* harmony import */ var _engine_simulation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/engine/simulation */ \"(ssr)/./src/engine/simulation.ts\");\n/* harmony import */ var _analytics_performanceAnalyzer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/analytics/performanceAnalyzer */ \"(ssr)/./src/analytics/performanceAnalyzer.ts\");\n\n\n\n\n\n\nconst GRID_SIZE = {\n    width: 50,\n    height: 50\n};\nconst simulationEngine = new _engine_simulation__WEBPACK_IMPORTED_MODULE_2__.SimulationEngine();\nconst performanceAnalyzer = new _analytics_performanceAnalyzer__WEBPACK_IMPORTED_MODULE_3__.PerformanceAnalyzer();\nconst initialState = {\n    components: new Map(),\n    gridSize: GRID_SIZE,\n    isRunning: false,\n    gameTime: 0,\n    resources: new Map([\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.IRON_ORE,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COPPER_ORE,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COAL,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.IRON_PLATE,\n            100\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COPPER_PLATE,\n            100\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.GEAR,\n            50\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.CIRCUIT,\n            50\n        ]\n    ]),\n    statistics: {\n        totalProduction: new Map(),\n        totalConsumption: new Map(),\n        efficiency: 0,\n        bottlenecks: []\n    }\n};\nconst initialStoreState = {\n    ...initialState,\n    factoryAnalytics: null\n};\nconst useGameStore = (0,zustand__WEBPACK_IMPORTED_MODULE_4__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_5__.subscribeWithSelector)((set, get)=>({\n        ...initialStoreState,\n        addComponent: (type, position, direction = _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.NORTH)=>{\n            const state = get();\n            const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[type];\n            // Check if position is valid and not occupied\n            if (!isPositionValid(position, definition.size, state)) {\n                return null;\n            }\n            const id = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_1__.generateId)();\n            const component = {\n                id,\n                type,\n                position,\n                direction,\n                inventory: new Map(),\n                connections: {\n                    inputs: [],\n                    outputs: []\n                },\n                isActive: false,\n                lastProcessTime: 0\n            };\n            set((state)=>{\n                const newComponents = new Map(state.components).set(id, component);\n                // Auto-connect to adjacent components\n                autoConnectComponent(id, component, newComponents);\n                return {\n                    components: newComponents\n                };\n            });\n            return id;\n        },\n        removeComponent: (id)=>{\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const component = newComponents.get(id);\n                if (component) {\n                    // Remove all connections to this component\n                    newComponents.forEach((comp)=>{\n                        comp.connections.inputs = comp.connections.inputs.filter((connId)=>connId !== id);\n                        comp.connections.outputs = comp.connections.outputs.filter((connId)=>connId !== id);\n                    });\n                    newComponents.delete(id);\n                }\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        moveComponent: (id, position)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n                if (!isPositionValid(position, definition.size, state, id)) {\n                    return state;\n                }\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    position\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        rotateComponent: (id)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const newDirection = (component.direction + 1) % 4;\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    direction: newDirection\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        connectComponents: (fromId, toId)=>{\n            const state = get();\n            const fromComponent = state.components.get(fromId);\n            const toComponent = state.components.get(toId);\n            if (!fromComponent || !toComponent || fromId === toId) {\n                return false;\n            }\n            const fromDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[fromComponent.type];\n            const toDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[toComponent.type];\n            // Check connection limits\n            if (fromComponent.connections.outputs.length >= fromDef.maxOutputs || toComponent.connections.inputs.length >= toDef.maxInputs) {\n                return false;\n            }\n            // Check if already connected\n            if (fromComponent.connections.outputs.includes(toId)) {\n                return false;\n            }\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const newFromComponent = {\n                    ...fromComponent\n                };\n                const newToComponent = {\n                    ...toComponent\n                };\n                newFromComponent.connections.outputs.push(toId);\n                newToComponent.connections.inputs.push(fromId);\n                newComponents.set(fromId, newFromComponent);\n                newComponents.set(toId, newToComponent);\n                return {\n                    components: newComponents\n                };\n            });\n            return true;\n        },\n        disconnectComponents: (fromId, toId)=>{\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const fromComponent = newComponents.get(fromId);\n                const toComponent = newComponents.get(toId);\n                if (fromComponent && toComponent) {\n                    fromComponent.connections.outputs = fromComponent.connections.outputs.filter((id)=>id !== toId);\n                    toComponent.connections.inputs = toComponent.connections.inputs.filter((id)=>id !== fromId);\n                }\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        setComponentRecipe: (id, recipeId)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const recipe = _types_game__WEBPACK_IMPORTED_MODULE_0__.RECIPES[recipeId];\n                if (!recipe) return state;\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    recipe\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        toggleSimulation: ()=>{\n            set((state)=>({\n                    isRunning: !state.isRunning\n                }));\n        },\n        updateSimulation: ()=>{\n            const state = get();\n            if (!state.isRunning) return;\n            const updates = simulationEngine.updateSimulation(state);\n            if (Object.keys(updates).length > 0) {\n                const newState = {\n                    ...state,\n                    ...updates\n                };\n                const analytics = performanceAnalyzer.analyzeFactory(newState);\n                set((currentState)=>({\n                        ...currentState,\n                        ...updates,\n                        factoryAnalytics: analytics\n                    }));\n            }\n        },\n        getPerformanceMetrics: ()=>{\n            const state = get();\n            return {\n                throughput: state.statistics.totalProduction,\n                utilization: new Map(),\n                bottlenecks: state.statistics.bottlenecks,\n                efficiency: state.statistics.efficiency\n            };\n        },\n        getFactoryAnalytics: ()=>{\n            const state = get();\n            if (state.factoryAnalytics) {\n                return state.factoryAnalytics;\n            }\n            // Generate analytics on demand if not available\n            return performanceAnalyzer.analyzeFactory(state);\n        },\n        getHistoricalData: (key)=>{\n            return performanceAnalyzer.getHistoricalData(key);\n        },\n        exportGameState: ()=>{\n            const state = get();\n            const exportData = {\n                components: Array.from(state.components.entries()),\n                gridSize: state.gridSize,\n                gameTime: state.gameTime,\n                resources: Array.from(state.resources.entries())\n            };\n            return JSON.stringify(exportData, null, 2);\n        },\n        importGameState: (jsonState)=>{\n            try {\n                const data = JSON.parse(jsonState);\n                set({\n                    components: new Map(data.components),\n                    gridSize: data.gridSize || GRID_SIZE,\n                    gameTime: data.gameTime || 0,\n                    resources: new Map(data.resources),\n                    isRunning: false\n                });\n                return true;\n            } catch (error) {\n                console.error('Failed to import game state:', error);\n                return false;\n            }\n        },\n        resetGame: ()=>{\n            set(initialState);\n        }\n    })));\n// Helper functions\nfunction isPositionValid(position, size, state, excludeId) {\n    // Check bounds\n    if (position.x < 0 || position.y < 0 || position.x + size.width > state.gridSize.width || position.y + size.height > state.gridSize.height) {\n        return false;\n    }\n    // Check for overlaps with existing components\n    for (const [id, component] of state.components){\n        if (excludeId && id === excludeId) continue;\n        const compDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n        const compPos = component.position;\n        // Check if rectangles overlap\n        if (!(position.x >= compPos.x + compDef.size.width || position.x + size.width <= compPos.x || position.y >= compPos.y + compDef.size.height || position.y + size.height <= compPos.y)) {\n            return false;\n        }\n    }\n    return true;\n}\n// Auto-connect components based on adjacency and direction\nfunction autoConnectComponent(newId, newComponent, components) {\n    const newDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[newComponent.type];\n    // Find adjacent components\n    for (const [existingId, existingComponent] of components){\n        if (existingId === newId) continue;\n        const existingDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[existingComponent.type];\n        // Check if components are adjacent and can connect\n        if (areComponentsAdjacent(newComponent, existingComponent)) {\n            const connectionDirection = getConnectionDirection(newComponent, existingComponent);\n            // Determine which component should be input/output based on direction and type\n            const shouldConnect = shouldAutoConnect(newComponent, newDef, existingComponent, existingDef, connectionDirection);\n            if (shouldConnect.connect) {\n                if (shouldConnect.newIsOutput) {\n                    // New component outputs to existing component\n                    if (newComponent.connections.outputs.length < newDef.maxOutputs && existingComponent.connections.inputs.length < existingDef.maxInputs) {\n                        newComponent.connections.outputs.push(existingId);\n                        existingComponent.connections.inputs.push(newId);\n                    }\n                } else {\n                    // Existing component outputs to new component\n                    if (existingComponent.connections.outputs.length < existingDef.maxOutputs && newComponent.connections.inputs.length < newDef.maxInputs) {\n                        existingComponent.connections.outputs.push(newId);\n                        newComponent.connections.inputs.push(existingId);\n                    }\n                }\n            }\n        }\n    }\n}\n// Check if two components are adjacent (within 1 grid unit)\nfunction areComponentsAdjacent(comp1, comp2) {\n    const def1 = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[comp1.type];\n    const def2 = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[comp2.type];\n    // Calculate component bounds\n    const comp1Bounds = {\n        left: comp1.position.x,\n        right: comp1.position.x + def1.size.width,\n        top: comp1.position.y,\n        bottom: comp1.position.y + def1.size.height\n    };\n    const comp2Bounds = {\n        left: comp2.position.x,\n        right: comp2.position.x + def2.size.width,\n        top: comp2.position.y,\n        bottom: comp2.position.y + def2.size.height\n    };\n    // Check if they're adjacent (touching but not overlapping)\n    const horizontallyAdjacent = (comp1Bounds.right === comp2Bounds.left || comp2Bounds.right === comp1Bounds.left) && !(comp1Bounds.bottom <= comp2Bounds.top || comp2Bounds.bottom <= comp1Bounds.top);\n    const verticallyAdjacent = (comp1Bounds.bottom === comp2Bounds.top || comp2Bounds.bottom === comp1Bounds.top) && !(comp1Bounds.right <= comp2Bounds.left || comp2Bounds.right <= comp1Bounds.left);\n    return horizontallyAdjacent || verticallyAdjacent;\n}\n// Get the direction from comp1 to comp2\nfunction getConnectionDirection(comp1, comp2) {\n    const dx = comp2.position.x - comp1.position.x;\n    const dy = comp2.position.y - comp1.position.y;\n    if (Math.abs(dx) > Math.abs(dy)) {\n        return dx > 0 ? _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.EAST : _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.WEST;\n    } else {\n        return dy > 0 ? _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.SOUTH : _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.NORTH;\n    }\n}\n// Determine if components should auto-connect and in which direction\nfunction shouldAutoConnect(comp1, def1, comp2, def2, direction) {\n    // Miners always output\n    if (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MINER && def2.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: true\n        };\n    }\n    if (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MINER && def1.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: false\n        };\n    }\n    // Assemblers prefer to output to storage or conveyors\n    if (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.ASSEMBLER && (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.STORAGE || comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR)) {\n        return {\n            connect: true,\n            newIsOutput: true\n        };\n    }\n    if (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.ASSEMBLER && (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.STORAGE || comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR)) {\n        return {\n            connect: true,\n            newIsOutput: false\n        };\n    }\n    // Conveyors connect in the direction they're facing\n    if (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR && comp1.direction === direction && def2.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: true\n        };\n    }\n    if (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR && comp2.direction === (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_1__.getOppositeDirection)(direction) && def1.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: false\n        };\n    }\n    // Default: don't auto-connect\n    return {\n        connect: false,\n        newIsOutput: false\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/gameStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/game.ts":
/*!***************************!*\
  !*** ./src/types/game.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMPONENT_DEFINITIONS: () => (/* binding */ COMPONENT_DEFINITIONS),\n/* harmony export */   ComponentType: () => (/* binding */ ComponentType),\n/* harmony export */   Direction: () => (/* binding */ Direction),\n/* harmony export */   RECIPES: () => (/* binding */ RECIPES),\n/* harmony export */   ResourceType: () => (/* binding */ ResourceType)\n/* harmony export */ });\n// Core game types and interfaces\nvar ComponentType = /*#__PURE__*/ function(ComponentType) {\n    ComponentType[\"CONVEYOR\"] = \"conveyor\";\n    ComponentType[\"MINER\"] = \"miner\";\n    ComponentType[\"ASSEMBLER\"] = \"assembler\";\n    ComponentType[\"STORAGE\"] = \"storage\";\n    ComponentType[\"SPLITTER\"] = \"splitter\";\n    ComponentType[\"MERGER\"] = \"merger\";\n    return ComponentType;\n}({});\nvar ResourceType = /*#__PURE__*/ function(ResourceType) {\n    ResourceType[\"IRON_ORE\"] = \"iron_ore\";\n    ResourceType[\"COPPER_ORE\"] = \"copper_ore\";\n    ResourceType[\"COAL\"] = \"coal\";\n    ResourceType[\"IRON_PLATE\"] = \"iron_plate\";\n    ResourceType[\"COPPER_PLATE\"] = \"copper_plate\";\n    ResourceType[\"GEAR\"] = \"gear\";\n    ResourceType[\"CIRCUIT\"] = \"circuit\";\n    return ResourceType;\n}({});\nvar Direction = /*#__PURE__*/ function(Direction) {\n    Direction[Direction[\"NORTH\"] = 0] = \"NORTH\";\n    Direction[Direction[\"EAST\"] = 1] = \"EAST\";\n    Direction[Direction[\"SOUTH\"] = 2] = \"SOUTH\";\n    Direction[Direction[\"WEST\"] = 3] = \"WEST\";\n    return Direction;\n}({});\n// Component definitions for the game\nconst COMPONENT_DEFINITIONS = {\n    [\"conveyor\"]: {\n        type: \"conveyor\",\n        name: 'Conveyor Belt',\n        description: 'Transports items between components',\n        size: {\n            width: 1,\n            height: 1\n        },\n        maxInputs: 1,\n        maxOutputs: 1,\n        speed: 15,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 1\n            }\n        ]\n    },\n    [\"miner\"]: {\n        type: \"miner\",\n        name: 'Mining Drill',\n        description: 'Extracts raw resources from the ground',\n        size: {\n            width: 2,\n            height: 2\n        },\n        maxInputs: 0,\n        maxOutputs: 1,\n        speed: 0.5,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 10\n            },\n            {\n                resource: \"gear\",\n                amount: 5\n            }\n        ]\n    },\n    [\"assembler\"]: {\n        type: \"assembler\",\n        name: 'Assembling Machine',\n        description: 'Crafts items from raw materials',\n        size: {\n            width: 3,\n            height: 3\n        },\n        maxInputs: 2,\n        maxOutputs: 1,\n        speed: 0.75,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 9\n            },\n            {\n                resource: \"gear\",\n                amount: 5\n            },\n            {\n                resource: \"circuit\",\n                amount: 3\n            }\n        ]\n    },\n    [\"storage\"]: {\n        type: \"storage\",\n        name: 'Storage Chest',\n        description: 'Stores items for later use',\n        size: {\n            width: 1,\n            height: 1\n        },\n        maxInputs: 1,\n        maxOutputs: 1,\n        speed: 30,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 8\n            }\n        ]\n    },\n    [\"splitter\"]: {\n        type: \"splitter\",\n        name: 'Splitter',\n        description: 'Splits input into multiple outputs',\n        size: {\n            width: 2,\n            height: 1\n        },\n        maxInputs: 1,\n        maxOutputs: 2,\n        speed: 15,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 5\n            },\n            {\n                resource: \"circuit\",\n                amount: 5\n            }\n        ]\n    },\n    [\"merger\"]: {\n        type: \"merger\",\n        name: 'Merger',\n        description: 'Merges multiple inputs into one output',\n        size: {\n            width: 2,\n            height: 1\n        },\n        maxInputs: 2,\n        maxOutputs: 1,\n        speed: 15,\n        cost: [\n            {\n                resource: \"iron_plate\",\n                amount: 5\n            },\n            {\n                resource: \"circuit\",\n                amount: 5\n            }\n        ]\n    }\n};\n// Recipe definitions\nconst RECIPES = {\n    iron_plate: {\n        id: 'iron_plate',\n        name: 'Iron Plate',\n        inputs: [\n            {\n                resource: \"iron_ore\",\n                amount: 1\n            }\n        ],\n        outputs: [\n            {\n                resource: \"iron_plate\",\n                amount: 1\n            }\n        ],\n        processingTime: 3200\n    },\n    copper_plate: {\n        id: 'copper_plate',\n        name: 'Copper Plate',\n        inputs: [\n            {\n                resource: \"copper_ore\",\n                amount: 1\n            }\n        ],\n        outputs: [\n            {\n                resource: \"copper_plate\",\n                amount: 1\n            }\n        ],\n        processingTime: 3200\n    },\n    gear: {\n        id: 'gear',\n        name: 'Iron Gear Wheel',\n        inputs: [\n            {\n                resource: \"iron_plate\",\n                amount: 2\n            }\n        ],\n        outputs: [\n            {\n                resource: \"gear\",\n                amount: 1\n            }\n        ],\n        processingTime: 500\n    },\n    circuit: {\n        id: 'circuit',\n        name: 'Electronic Circuit',\n        inputs: [\n            {\n                resource: \"iron_plate\",\n                amount: 1\n            },\n            {\n                resource: \"copper_plate\",\n                amount: 3\n            }\n        ],\n        outputs: [\n            {\n                resource: \"circuit\",\n                amount: 1\n            }\n        ],\n        processingTime: 500\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/game.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/helpers.ts":
/*!******************************!*\
  !*** ./src/utils/helpers.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDistance: () => (/* binding */ calculateDistance),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getDirectionVector: () => (/* binding */ getDirectionVector),\n/* harmony export */   getOppositeDirection: () => (/* binding */ getOppositeDirection),\n/* harmony export */   getRotationTransform: () => (/* binding */ getRotationTransform),\n/* harmony export */   gridToScreen: () => (/* binding */ gridToScreen),\n/* harmony export */   lerp: () => (/* binding */ lerp),\n/* harmony export */   pointInRect: () => (/* binding */ pointInRect),\n/* harmony export */   rectanglesOverlap: () => (/* binding */ rectanglesOverlap),\n/* harmony export */   screenToGrid: () => (/* binding */ screenToGrid),\n/* harmony export */   snapToGrid: () => (/* binding */ snapToGrid),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var _types_game__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/game */ \"(ssr)/./src/types/game.ts\");\n\n// Generate unique IDs for components\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// Calculate distance between two positions\nfunction calculateDistance(pos1, pos2) {\n    return Math.sqrt(Math.pow(pos2.x - pos1.x, 2) + Math.pow(pos2.y - pos1.y, 2));\n}\n// Get direction vector from Direction enum\nfunction getDirectionVector(direction) {\n    switch(direction){\n        case _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.NORTH:\n            return {\n                x: 0,\n                y: -1\n            };\n        case _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.EAST:\n            return {\n                x: 1,\n                y: 0\n            };\n        case _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.SOUTH:\n            return {\n                x: 0,\n                y: 1\n            };\n        case _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.WEST:\n            return {\n                x: -1,\n                y: 0\n            };\n        default:\n            return {\n                x: 0,\n                y: 0\n            };\n    }\n}\n// Get opposite direction\nfunction getOppositeDirection(direction) {\n    return (direction + 2) % 4;\n}\n// Check if two rectangles overlap\nfunction rectanglesOverlap(pos1, size1, pos2, size2) {\n    return !(pos1.x >= pos2.x + size2.width || pos1.x + size1.width <= pos2.x || pos1.y >= pos2.y + size2.height || pos1.y + size1.height <= pos2.y);\n}\n// Snap position to grid\nfunction snapToGrid(position, gridSize = 1) {\n    return {\n        x: Math.floor(position.x / gridSize) * gridSize,\n        y: Math.floor(position.y / gridSize) * gridSize\n    };\n}\n// Convert screen coordinates to grid coordinates\nfunction screenToGrid(screenPos, cellSize, offset = {\n    x: 0,\n    y: 0\n}) {\n    return {\n        x: Math.floor((screenPos.x - offset.x) / cellSize),\n        y: Math.floor((screenPos.y - offset.y) / cellSize)\n    };\n}\n// Convert grid coordinates to screen coordinates\nfunction gridToScreen(gridPos, cellSize, offset = {\n    x: 0,\n    y: 0\n}) {\n    return {\n        x: gridPos.x * cellSize + offset.x,\n        y: gridPos.y * cellSize + offset.y\n    };\n}\n// Format numbers for display\nfunction formatNumber(num) {\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n}\n// Format time duration\nfunction formatTime(milliseconds) {\n    const seconds = Math.floor(milliseconds / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    if (hours > 0) {\n        return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;\n    } else if (minutes > 0) {\n        return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;\n    } else {\n        return `${seconds}s`;\n    }\n}\n// Clamp value between min and max\nfunction clamp(value, min, max) {\n    return Math.min(Math.max(value, min), max);\n}\n// Linear interpolation\nfunction lerp(start, end, factor) {\n    return start + (end - start) * factor;\n}\n// Check if point is inside rectangle\nfunction pointInRect(point, rect, size) {\n    return point.x >= rect.x && point.x < rect.x + size.width && point.y >= rect.y && point.y < rect.y + size.height;\n}\n// Get rotation transform for CSS\nfunction getRotationTransform(direction) {\n    const degrees = direction * 90;\n    return `rotate(${degrees}deg)`;\n}\n// Debounce function\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n// Throttle function\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvaGVscGVycy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF5RDtBQUV6RCxxQ0FBcUM7QUFDOUIsU0FBU0M7SUFDZCxPQUFPQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRztBQUM5QztBQUVBLDJDQUEyQztBQUNwQyxTQUFTQyxrQkFBa0JDLElBQWMsRUFBRUMsSUFBYztJQUM5RCxPQUFPTixLQUFLTyxJQUFJLENBQUNQLEtBQUtRLEdBQUcsQ0FBQ0YsS0FBS0csQ0FBQyxHQUFHSixLQUFLSSxDQUFDLEVBQUUsS0FBS1QsS0FBS1EsR0FBRyxDQUFDRixLQUFLSSxDQUFDLEdBQUdMLEtBQUtLLENBQUMsRUFBRTtBQUM1RTtBQUVBLDJDQUEyQztBQUNwQyxTQUFTQyxtQkFBbUJDLFNBQW9CO0lBQ3JELE9BQVFBO1FBQ04sS0FBS2Qsa0RBQVNBLENBQUNlLEtBQUs7WUFDbEIsT0FBTztnQkFBRUosR0FBRztnQkFBR0MsR0FBRyxDQUFDO1lBQUU7UUFDdkIsS0FBS1osa0RBQVNBLENBQUNnQixJQUFJO1lBQ2pCLE9BQU87Z0JBQUVMLEdBQUc7Z0JBQUdDLEdBQUc7WUFBRTtRQUN0QixLQUFLWixrREFBU0EsQ0FBQ2lCLEtBQUs7WUFDbEIsT0FBTztnQkFBRU4sR0FBRztnQkFBR0MsR0FBRztZQUFFO1FBQ3RCLEtBQUtaLGtEQUFTQSxDQUFDa0IsSUFBSTtZQUNqQixPQUFPO2dCQUFFUCxHQUFHLENBQUM7Z0JBQUdDLEdBQUc7WUFBRTtRQUN2QjtZQUNFLE9BQU87Z0JBQUVELEdBQUc7Z0JBQUdDLEdBQUc7WUFBRTtJQUN4QjtBQUNGO0FBRUEseUJBQXlCO0FBQ2xCLFNBQVNPLHFCQUFxQkwsU0FBb0I7SUFDdkQsT0FBTyxDQUFDQSxZQUFZLEtBQUs7QUFDM0I7QUFFQSxrQ0FBa0M7QUFDM0IsU0FBU00sa0JBQ2RiLElBQWMsRUFDZGMsS0FBVyxFQUNYYixJQUFjLEVBQ2RjLEtBQVc7SUFFWCxPQUFPLENBQ0xmLENBQUFBLEtBQUtJLENBQUMsSUFBSUgsS0FBS0csQ0FBQyxHQUFHVyxNQUFNQyxLQUFLLElBQzlCaEIsS0FBS0ksQ0FBQyxHQUFHVSxNQUFNRSxLQUFLLElBQUlmLEtBQUtHLENBQUMsSUFDOUJKLEtBQUtLLENBQUMsSUFBSUosS0FBS0ksQ0FBQyxHQUFHVSxNQUFNRSxNQUFNLElBQy9CakIsS0FBS0ssQ0FBQyxHQUFHUyxNQUFNRyxNQUFNLElBQUloQixLQUFLSSxDQUFDO0FBRW5DO0FBRUEsd0JBQXdCO0FBQ2pCLFNBQVNhLFdBQVdDLFFBQWtCLEVBQUVDLFdBQW1CLENBQUM7SUFDakUsT0FBTztRQUNMaEIsR0FBR1QsS0FBSzBCLEtBQUssQ0FBQ0YsU0FBU2YsQ0FBQyxHQUFHZ0IsWUFBWUE7UUFDdkNmLEdBQUdWLEtBQUswQixLQUFLLENBQUNGLFNBQVNkLENBQUMsR0FBR2UsWUFBWUE7SUFDekM7QUFDRjtBQUVBLGlEQUFpRDtBQUMxQyxTQUFTRSxhQUNkQyxTQUFtQixFQUNuQkMsUUFBZ0IsRUFDaEJDLFNBQW1CO0lBQUVyQixHQUFHO0lBQUdDLEdBQUc7QUFBRSxDQUFDO0lBRWpDLE9BQU87UUFDTEQsR0FBR1QsS0FBSzBCLEtBQUssQ0FBQyxDQUFDRSxVQUFVbkIsQ0FBQyxHQUFHcUIsT0FBT3JCLENBQUMsSUFBSW9CO1FBQ3pDbkIsR0FBR1YsS0FBSzBCLEtBQUssQ0FBQyxDQUFDRSxVQUFVbEIsQ0FBQyxHQUFHb0IsT0FBT3BCLENBQUMsSUFBSW1CO0lBQzNDO0FBQ0Y7QUFFQSxpREFBaUQ7QUFDMUMsU0FBU0UsYUFDZEMsT0FBaUIsRUFDakJILFFBQWdCLEVBQ2hCQyxTQUFtQjtJQUFFckIsR0FBRztJQUFHQyxHQUFHO0FBQUUsQ0FBQztJQUVqQyxPQUFPO1FBQ0xELEdBQUd1QixRQUFRdkIsQ0FBQyxHQUFHb0IsV0FBV0MsT0FBT3JCLENBQUM7UUFDbENDLEdBQUdzQixRQUFRdEIsQ0FBQyxHQUFHbUIsV0FBV0MsT0FBT3BCLENBQUM7SUFDcEM7QUFDRjtBQUVBLDZCQUE2QjtBQUN0QixTQUFTdUIsYUFBYUMsR0FBVztJQUN0QyxJQUFJQSxPQUFPLFNBQVM7UUFDbEIsT0FBTyxDQUFDQSxNQUFNLE9BQU0sRUFBR0MsT0FBTyxDQUFDLEtBQUs7SUFDdEMsT0FBTyxJQUFJRCxPQUFPLE1BQU07UUFDdEIsT0FBTyxDQUFDQSxNQUFNLElBQUcsRUFBR0MsT0FBTyxDQUFDLEtBQUs7SUFDbkM7SUFDQSxPQUFPRCxJQUFJaEMsUUFBUTtBQUNyQjtBQUVBLHVCQUF1QjtBQUNoQixTQUFTa0MsV0FBV0MsWUFBb0I7SUFDN0MsTUFBTUMsVUFBVXRDLEtBQUswQixLQUFLLENBQUNXLGVBQWU7SUFDMUMsTUFBTUUsVUFBVXZDLEtBQUswQixLQUFLLENBQUNZLFVBQVU7SUFDckMsTUFBTUUsUUFBUXhDLEtBQUswQixLQUFLLENBQUNhLFVBQVU7SUFFbkMsSUFBSUMsUUFBUSxHQUFHO1FBQ2IsT0FBTyxHQUFHQSxNQUFNLENBQUMsRUFBRSxDQUFDRCxVQUFVLEVBQUMsRUFBR3JDLFFBQVEsR0FBR3VDLFFBQVEsQ0FBQyxHQUFHLEtBQUssQ0FBQyxFQUFFLENBQUNILFVBQVUsRUFBQyxFQUFHcEMsUUFBUSxHQUFHdUMsUUFBUSxDQUFDLEdBQUcsTUFBTTtJQUMvRyxPQUFPLElBQUlGLFVBQVUsR0FBRztRQUN0QixPQUFPLEdBQUdBLFFBQVEsQ0FBQyxFQUFFLENBQUNELFVBQVUsRUFBQyxFQUFHcEMsUUFBUSxHQUFHdUMsUUFBUSxDQUFDLEdBQUcsTUFBTTtJQUNuRSxPQUFPO1FBQ0wsT0FBTyxHQUFHSCxRQUFRLENBQUMsQ0FBQztJQUN0QjtBQUNGO0FBRUEsa0NBQWtDO0FBQzNCLFNBQVNJLE1BQU1DLEtBQWEsRUFBRUMsR0FBVyxFQUFFQyxHQUFXO0lBQzNELE9BQU83QyxLQUFLNEMsR0FBRyxDQUFDNUMsS0FBSzZDLEdBQUcsQ0FBQ0YsT0FBT0MsTUFBTUM7QUFDeEM7QUFFQSx1QkFBdUI7QUFDaEIsU0FBU0MsS0FBS0MsS0FBYSxFQUFFQyxHQUFXLEVBQUVDLE1BQWM7SUFDN0QsT0FBT0YsUUFBUSxDQUFDQyxNQUFNRCxLQUFJLElBQUtFO0FBQ2pDO0FBRUEscUNBQXFDO0FBQzlCLFNBQVNDLFlBQVlDLEtBQWUsRUFBRUMsSUFBYyxFQUFFQyxJQUFVO0lBQ3JFLE9BQ0VGLE1BQU0xQyxDQUFDLElBQUkyQyxLQUFLM0MsQ0FBQyxJQUNqQjBDLE1BQU0xQyxDQUFDLEdBQUcyQyxLQUFLM0MsQ0FBQyxHQUFHNEMsS0FBS2hDLEtBQUssSUFDN0I4QixNQUFNekMsQ0FBQyxJQUFJMEMsS0FBSzFDLENBQUMsSUFDakJ5QyxNQUFNekMsQ0FBQyxHQUFHMEMsS0FBSzFDLENBQUMsR0FBRzJDLEtBQUsvQixNQUFNO0FBRWxDO0FBRUEsaUNBQWlDO0FBQzFCLFNBQVNnQyxxQkFBcUIxQyxTQUFvQjtJQUN2RCxNQUFNMkMsVUFBVTNDLFlBQVk7SUFDNUIsT0FBTyxDQUFDLE9BQU8sRUFBRTJDLFFBQVEsSUFBSSxDQUFDO0FBQ2hDO0FBRUEsb0JBQW9CO0FBQ2IsU0FBU0MsU0FDZEMsSUFBTyxFQUNQQyxJQUFZO0lBRVosSUFBSUM7SUFDSixPQUFPLENBQUMsR0FBR0M7UUFDVEMsYUFBYUY7UUFDYkEsVUFBVUcsV0FBVyxJQUFNTCxRQUFRRyxPQUFPRjtJQUM1QztBQUNGO0FBRUEsb0JBQW9CO0FBQ2IsU0FBU0ssU0FDZE4sSUFBTyxFQUNQTyxLQUFhO0lBRWIsSUFBSUM7SUFDSixPQUFPLENBQUMsR0FBR0w7UUFDVCxJQUFJLENBQUNLLFlBQVk7WUFDZlIsUUFBUUc7WUFDUkssYUFBYTtZQUNiSCxXQUFXLElBQU9HLGFBQWEsT0FBUUQ7UUFDekM7SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXHNyY1xcdXRpbHNcXGhlbHBlcnMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUG9zaXRpb24sIERpcmVjdGlvbiwgU2l6ZSB9IGZyb20gJ0AvdHlwZXMvZ2FtZSc7XG5cbi8vIEdlbmVyYXRlIHVuaXF1ZSBJRHMgZm9yIGNvbXBvbmVudHNcbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUlkKCk6IHN0cmluZyB7XG4gIHJldHVybiBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSk7XG59XG5cbi8vIENhbGN1bGF0ZSBkaXN0YW5jZSBiZXR3ZWVuIHR3byBwb3NpdGlvbnNcbmV4cG9ydCBmdW5jdGlvbiBjYWxjdWxhdGVEaXN0YW5jZShwb3MxOiBQb3NpdGlvbiwgcG9zMjogUG9zaXRpb24pOiBudW1iZXIge1xuICByZXR1cm4gTWF0aC5zcXJ0KE1hdGgucG93KHBvczIueCAtIHBvczEueCwgMikgKyBNYXRoLnBvdyhwb3MyLnkgLSBwb3MxLnksIDIpKTtcbn1cblxuLy8gR2V0IGRpcmVjdGlvbiB2ZWN0b3IgZnJvbSBEaXJlY3Rpb24gZW51bVxuZXhwb3J0IGZ1bmN0aW9uIGdldERpcmVjdGlvblZlY3RvcihkaXJlY3Rpb246IERpcmVjdGlvbik6IFBvc2l0aW9uIHtcbiAgc3dpdGNoIChkaXJlY3Rpb24pIHtcbiAgICBjYXNlIERpcmVjdGlvbi5OT1JUSDpcbiAgICAgIHJldHVybiB7IHg6IDAsIHk6IC0xIH07XG4gICAgY2FzZSBEaXJlY3Rpb24uRUFTVDpcbiAgICAgIHJldHVybiB7IHg6IDEsIHk6IDAgfTtcbiAgICBjYXNlIERpcmVjdGlvbi5TT1VUSDpcbiAgICAgIHJldHVybiB7IHg6IDAsIHk6IDEgfTtcbiAgICBjYXNlIERpcmVjdGlvbi5XRVNUOlxuICAgICAgcmV0dXJuIHsgeDogLTEsIHk6IDAgfTtcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIHsgeDogMCwgeTogMCB9O1xuICB9XG59XG5cbi8vIEdldCBvcHBvc2l0ZSBkaXJlY3Rpb25cbmV4cG9ydCBmdW5jdGlvbiBnZXRPcHBvc2l0ZURpcmVjdGlvbihkaXJlY3Rpb246IERpcmVjdGlvbik6IERpcmVjdGlvbiB7XG4gIHJldHVybiAoZGlyZWN0aW9uICsgMikgJSA0O1xufVxuXG4vLyBDaGVjayBpZiB0d28gcmVjdGFuZ2xlcyBvdmVybGFwXG5leHBvcnQgZnVuY3Rpb24gcmVjdGFuZ2xlc092ZXJsYXAoXG4gIHBvczE6IFBvc2l0aW9uLFxuICBzaXplMTogU2l6ZSxcbiAgcG9zMjogUG9zaXRpb24sXG4gIHNpemUyOiBTaXplXG4pOiBib29sZWFuIHtcbiAgcmV0dXJuICEoXG4gICAgcG9zMS54ID49IHBvczIueCArIHNpemUyLndpZHRoIHx8XG4gICAgcG9zMS54ICsgc2l6ZTEud2lkdGggPD0gcG9zMi54IHx8XG4gICAgcG9zMS55ID49IHBvczIueSArIHNpemUyLmhlaWdodCB8fFxuICAgIHBvczEueSArIHNpemUxLmhlaWdodCA8PSBwb3MyLnlcbiAgKTtcbn1cblxuLy8gU25hcCBwb3NpdGlvbiB0byBncmlkXG5leHBvcnQgZnVuY3Rpb24gc25hcFRvR3JpZChwb3NpdGlvbjogUG9zaXRpb24sIGdyaWRTaXplOiBudW1iZXIgPSAxKTogUG9zaXRpb24ge1xuICByZXR1cm4ge1xuICAgIHg6IE1hdGguZmxvb3IocG9zaXRpb24ueCAvIGdyaWRTaXplKSAqIGdyaWRTaXplLFxuICAgIHk6IE1hdGguZmxvb3IocG9zaXRpb24ueSAvIGdyaWRTaXplKSAqIGdyaWRTaXplLFxuICB9O1xufVxuXG4vLyBDb252ZXJ0IHNjcmVlbiBjb29yZGluYXRlcyB0byBncmlkIGNvb3JkaW5hdGVzXG5leHBvcnQgZnVuY3Rpb24gc2NyZWVuVG9HcmlkKFxuICBzY3JlZW5Qb3M6IFBvc2l0aW9uLFxuICBjZWxsU2l6ZTogbnVtYmVyLFxuICBvZmZzZXQ6IFBvc2l0aW9uID0geyB4OiAwLCB5OiAwIH1cbik6IFBvc2l0aW9uIHtcbiAgcmV0dXJuIHtcbiAgICB4OiBNYXRoLmZsb29yKChzY3JlZW5Qb3MueCAtIG9mZnNldC54KSAvIGNlbGxTaXplKSxcbiAgICB5OiBNYXRoLmZsb29yKChzY3JlZW5Qb3MueSAtIG9mZnNldC55KSAvIGNlbGxTaXplKSxcbiAgfTtcbn1cblxuLy8gQ29udmVydCBncmlkIGNvb3JkaW5hdGVzIHRvIHNjcmVlbiBjb29yZGluYXRlc1xuZXhwb3J0IGZ1bmN0aW9uIGdyaWRUb1NjcmVlbihcbiAgZ3JpZFBvczogUG9zaXRpb24sXG4gIGNlbGxTaXplOiBudW1iZXIsXG4gIG9mZnNldDogUG9zaXRpb24gPSB7IHg6IDAsIHk6IDAgfVxuKTogUG9zaXRpb24ge1xuICByZXR1cm4ge1xuICAgIHg6IGdyaWRQb3MueCAqIGNlbGxTaXplICsgb2Zmc2V0LngsXG4gICAgeTogZ3JpZFBvcy55ICogY2VsbFNpemUgKyBvZmZzZXQueSxcbiAgfTtcbn1cblxuLy8gRm9ybWF0IG51bWJlcnMgZm9yIGRpc3BsYXlcbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXROdW1iZXIobnVtOiBudW1iZXIpOiBzdHJpbmcge1xuICBpZiAobnVtID49IDEwMDAwMDApIHtcbiAgICByZXR1cm4gKG51bSAvIDEwMDAwMDApLnRvRml4ZWQoMSkgKyAnTSc7XG4gIH0gZWxzZSBpZiAobnVtID49IDEwMDApIHtcbiAgICByZXR1cm4gKG51bSAvIDEwMDApLnRvRml4ZWQoMSkgKyAnSyc7XG4gIH1cbiAgcmV0dXJuIG51bS50b1N0cmluZygpO1xufVxuXG4vLyBGb3JtYXQgdGltZSBkdXJhdGlvblxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdFRpbWUobWlsbGlzZWNvbmRzOiBudW1iZXIpOiBzdHJpbmcge1xuICBjb25zdCBzZWNvbmRzID0gTWF0aC5mbG9vcihtaWxsaXNlY29uZHMgLyAxMDAwKTtcbiAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDYwKTtcbiAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKG1pbnV0ZXMgLyA2MCk7XG5cbiAgaWYgKGhvdXJzID4gMCkge1xuICAgIHJldHVybiBgJHtob3Vyc306JHsobWludXRlcyAlIDYwKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyl9OiR7KHNlY29uZHMgJSA2MCkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfWA7XG4gIH0gZWxzZSBpZiAobWludXRlcyA+IDApIHtcbiAgICByZXR1cm4gYCR7bWludXRlc306JHsoc2Vjb25kcyAlIDYwKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyl9YDtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gYCR7c2Vjb25kc31zYDtcbiAgfVxufVxuXG4vLyBDbGFtcCB2YWx1ZSBiZXR3ZWVuIG1pbiBhbmQgbWF4XG5leHBvcnQgZnVuY3Rpb24gY2xhbXAodmFsdWU6IG51bWJlciwgbWluOiBudW1iZXIsIG1heDogbnVtYmVyKTogbnVtYmVyIHtcbiAgcmV0dXJuIE1hdGgubWluKE1hdGgubWF4KHZhbHVlLCBtaW4pLCBtYXgpO1xufVxuXG4vLyBMaW5lYXIgaW50ZXJwb2xhdGlvblxuZXhwb3J0IGZ1bmN0aW9uIGxlcnAoc3RhcnQ6IG51bWJlciwgZW5kOiBudW1iZXIsIGZhY3RvcjogbnVtYmVyKTogbnVtYmVyIHtcbiAgcmV0dXJuIHN0YXJ0ICsgKGVuZCAtIHN0YXJ0KSAqIGZhY3Rvcjtcbn1cblxuLy8gQ2hlY2sgaWYgcG9pbnQgaXMgaW5zaWRlIHJlY3RhbmdsZVxuZXhwb3J0IGZ1bmN0aW9uIHBvaW50SW5SZWN0KHBvaW50OiBQb3NpdGlvbiwgcmVjdDogUG9zaXRpb24sIHNpemU6IFNpemUpOiBib29sZWFuIHtcbiAgcmV0dXJuIChcbiAgICBwb2ludC54ID49IHJlY3QueCAmJlxuICAgIHBvaW50LnggPCByZWN0LnggKyBzaXplLndpZHRoICYmXG4gICAgcG9pbnQueSA+PSByZWN0LnkgJiZcbiAgICBwb2ludC55IDwgcmVjdC55ICsgc2l6ZS5oZWlnaHRcbiAgKTtcbn1cblxuLy8gR2V0IHJvdGF0aW9uIHRyYW5zZm9ybSBmb3IgQ1NTXG5leHBvcnQgZnVuY3Rpb24gZ2V0Um90YXRpb25UcmFuc2Zvcm0oZGlyZWN0aW9uOiBEaXJlY3Rpb24pOiBzdHJpbmcge1xuICBjb25zdCBkZWdyZWVzID0gZGlyZWN0aW9uICogOTA7XG4gIHJldHVybiBgcm90YXRlKCR7ZGVncmVlc31kZWcpYDtcbn1cblxuLy8gRGVib3VuY2UgZnVuY3Rpb25cbmV4cG9ydCBmdW5jdGlvbiBkZWJvdW5jZTxUIGV4dGVuZHMgKC4uLmFyZ3M6IGFueVtdKSA9PiBhbnk+KFxuICBmdW5jOiBULFxuICB3YWl0OiBudW1iZXJcbik6ICguLi5hcmdzOiBQYXJhbWV0ZXJzPFQ+KSA9PiB2b2lkIHtcbiAgbGV0IHRpbWVvdXQ6IE5vZGVKUy5UaW1lb3V0O1xuICByZXR1cm4gKC4uLmFyZ3M6IFBhcmFtZXRlcnM8VD4pID0+IHtcbiAgICBjbGVhclRpbWVvdXQodGltZW91dCk7XG4gICAgdGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4gZnVuYyguLi5hcmdzKSwgd2FpdCk7XG4gIH07XG59XG5cbi8vIFRocm90dGxlIGZ1bmN0aW9uXG5leHBvcnQgZnVuY3Rpb24gdGhyb3R0bGU8VCBleHRlbmRzICguLi5hcmdzOiBhbnlbXSkgPT4gYW55PihcbiAgZnVuYzogVCxcbiAgbGltaXQ6IG51bWJlclxuKTogKC4uLmFyZ3M6IFBhcmFtZXRlcnM8VD4pID0+IHZvaWQge1xuICBsZXQgaW5UaHJvdHRsZTogYm9vbGVhbjtcbiAgcmV0dXJuICguLi5hcmdzOiBQYXJhbWV0ZXJzPFQ+KSA9PiB7XG4gICAgaWYgKCFpblRocm90dGxlKSB7XG4gICAgICBmdW5jKC4uLmFyZ3MpO1xuICAgICAgaW5UaHJvdHRsZSA9IHRydWU7XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IChpblRocm90dGxlID0gZmFsc2UpLCBsaW1pdCk7XG4gICAgfVxuICB9O1xufVxuIl0sIm5hbWVzIjpbIkRpcmVjdGlvbiIsImdlbmVyYXRlSWQiLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJjYWxjdWxhdGVEaXN0YW5jZSIsInBvczEiLCJwb3MyIiwic3FydCIsInBvdyIsIngiLCJ5IiwiZ2V0RGlyZWN0aW9uVmVjdG9yIiwiZGlyZWN0aW9uIiwiTk9SVEgiLCJFQVNUIiwiU09VVEgiLCJXRVNUIiwiZ2V0T3Bwb3NpdGVEaXJlY3Rpb24iLCJyZWN0YW5nbGVzT3ZlcmxhcCIsInNpemUxIiwic2l6ZTIiLCJ3aWR0aCIsImhlaWdodCIsInNuYXBUb0dyaWQiLCJwb3NpdGlvbiIsImdyaWRTaXplIiwiZmxvb3IiLCJzY3JlZW5Ub0dyaWQiLCJzY3JlZW5Qb3MiLCJjZWxsU2l6ZSIsIm9mZnNldCIsImdyaWRUb1NjcmVlbiIsImdyaWRQb3MiLCJmb3JtYXROdW1iZXIiLCJudW0iLCJ0b0ZpeGVkIiwiZm9ybWF0VGltZSIsIm1pbGxpc2Vjb25kcyIsInNlY29uZHMiLCJtaW51dGVzIiwiaG91cnMiLCJwYWRTdGFydCIsImNsYW1wIiwidmFsdWUiLCJtaW4iLCJtYXgiLCJsZXJwIiwic3RhcnQiLCJlbmQiLCJmYWN0b3IiLCJwb2ludEluUmVjdCIsInBvaW50IiwicmVjdCIsInNpemUiLCJnZXRSb3RhdGlvblRyYW5zZm9ybSIsImRlZ3JlZXMiLCJkZWJvdW5jZSIsImZ1bmMiLCJ3YWl0IiwidGltZW91dCIsImFyZ3MiLCJjbGVhclRpbWVvdXQiLCJzZXRUaW1lb3V0IiwidGhyb3R0bGUiLCJsaW1pdCIsImluVGhyb3R0bGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/helpers.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-dnd","vendor-chunks/dnd-core","vendor-chunks/lucide-react","vendor-chunks/react-dnd-html5-backend","vendor-chunks/@react-dnd","vendor-chunks/@swc","vendor-chunks/@babel","vendor-chunks/zustand","vendor-chunks/clsx","vendor-chunks/redux","vendor-chunks/fast-deep-equal"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDownloads%5Cfactorygame&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();