"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/GameBoard.tsx":
/*!**************************************!*\
  !*** ./src/components/GameBoard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-dnd */ \"(app-pages-browser)/./node_modules/react-dnd/dist/hooks/useDrop/useDrop.js\");\n/* harmony import */ var _store_gameStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/gameStore */ \"(app-pages-browser)/./src/store/gameStore.ts\");\n/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/helpers */ \"(app-pages-browser)/./src/utils/helpers.ts\");\n/* harmony import */ var _FactoryComponent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FactoryComponent */ \"(app-pages-browser)/./src/components/FactoryComponent.tsx\");\n/* harmony import */ var _GridOverlay__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./GridOverlay */ \"(app-pages-browser)/./src/components/GridOverlay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CELL_SIZE = 40;\nconst BOARD_WIDTH = 2000;\nconst BOARD_HEIGHT = 2000;\nconst GameBoard = ()=>{\n    _s();\n    const boardRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [viewOffset, setViewOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [selectedComponent, setSelectedComponent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { components, addComponent, moveComponent, removeComponent, isRunning, updateSimulation } = (0,_store_gameStore__WEBPACK_IMPORTED_MODULE_2__.useGameStore)();\n    // Animation loop for simulation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GameBoard.useEffect\": ()=>{\n            if (!isRunning) return;\n            const interval = setInterval({\n                \"GameBoard.useEffect.interval\": ()=>{\n                    updateSimulation();\n                }\n            }[\"GameBoard.useEffect.interval\"], 100);\n            return ({\n                \"GameBoard.useEffect\": ()=>clearInterval(interval)\n            })[\"GameBoard.useEffect\"];\n        }\n    }[\"GameBoard.useEffect\"], [\n        isRunning,\n        updateSimulation\n    ]);\n    // Drop handler for adding components\n    const [{ isOver }, drop] = (0,react_dnd__WEBPACK_IMPORTED_MODULE_6__.useDrop)({\n        accept: 'component',\n        drop: {\n            \"GameBoard.useDrop\": (item, monitor)=>{\n                const clientOffset = monitor.getClientOffset();\n                if (!clientOffset || !boardRef.current) return;\n                const boardRect = boardRef.current.getBoundingClientRect();\n                const screenPos = {\n                    x: clientOffset.x - boardRect.left,\n                    y: clientOffset.y - boardRect.top\n                };\n                const gridPos = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.screenToGrid)(screenPos, CELL_SIZE, viewOffset);\n                addComponent(item.type, gridPos);\n            }\n        }[\"GameBoard.useDrop\"],\n        collect: {\n            \"GameBoard.useDrop\": (monitor)=>({\n                    isOver: monitor.isOver()\n                })\n        }[\"GameBoard.useDrop\"]\n    });\n    // Pan handling\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GameBoard.useCallback[handleMouseDown]\": (e)=>{\n            if (e.button === 1 || e.button === 0 && e.ctrlKey) {\n                setIsDragging(true);\n                setDragStart({\n                    x: e.clientX - viewOffset.x,\n                    y: e.clientY - viewOffset.y\n                });\n                e.preventDefault();\n            }\n        }\n    }[\"GameBoard.useCallback[handleMouseDown]\"], [\n        viewOffset\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GameBoard.useCallback[handleMouseMove]\": (e)=>{\n            if (isDragging) {\n                setViewOffset({\n                    x: e.clientX - dragStart.x,\n                    y: e.clientY - dragStart.y\n                });\n            }\n        }\n    }[\"GameBoard.useCallback[handleMouseMove]\"], [\n        isDragging,\n        dragStart\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GameBoard.useCallback[handleMouseUp]\": ()=>{\n            setIsDragging(false);\n        }\n    }[\"GameBoard.useCallback[handleMouseUp]\"], []);\n    // Keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GameBoard.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"GameBoard.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Delete' && selectedComponent) {\n                        removeComponent(selectedComponent);\n                        setSelectedComponent(null);\n                    }\n                }\n            }[\"GameBoard.useEffect.handleKeyDown\"];\n            window.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"GameBoard.useEffect\": ()=>window.removeEventListener('keydown', handleKeyDown)\n            })[\"GameBoard.useEffect\"];\n        }\n    }[\"GameBoard.useEffect\"], [\n        selectedComponent,\n        removeComponent\n    ]);\n    // Combine refs\n    const combinedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"GameBoard.useCallback[combinedRef]\": (node)=>{\n            boardRef.current = node;\n            drop(node);\n        }\n    }[\"GameBoard.useCallback[combinedRef]\"], [\n        drop\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: combinedRef,\n        className: \"relative w-full h-full overflow-hidden bg-gray-900 cursor-\".concat(isDragging ? 'grabbing' : 'grab', \" \").concat(isOver ? 'bg-gray-800' : ''),\n        onMouseDown: handleMouseDown,\n        onMouseMove: handleMouseMove,\n        onMouseUp: handleMouseUp,\n        onMouseLeave: handleMouseUp,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GridOverlay__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                cellSize: CELL_SIZE,\n                offset: viewOffset,\n                width: BOARD_WIDTH,\n                height: BOARD_HEIGHT\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute\",\n                style: {\n                    transform: \"translate(\".concat(viewOffset.x, \"px, \").concat(viewOffset.y, \"px)\"),\n                    width: BOARD_WIDTH,\n                    height: BOARD_HEIGHT\n                },\n                children: Array.from(components.entries()).map((param)=>{\n                    let [id, component] = param;\n                    const screenPos = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.gridToScreen)(component.position, CELL_SIZE);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FactoryComponent__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        component: component,\n                        position: screenPos,\n                        cellSize: CELL_SIZE,\n                        isSelected: selectedComponent === id,\n                        onSelect: ()=>setSelectedComponent(id),\n                        onMove: (newGridPos)=>moveComponent(id, newGridPos)\n                    }, id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, undefined),\n            components.size === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold mb-2\",\n                            children: \"Welcome to Factory Builder!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-1\",\n                            children: \"Drag components from the left panel to start building\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-1\",\n                            children: \"Middle-click or Ctrl+click to pan the view\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Press Delete to remove selected components\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, undefined),\n            isOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-blue-500 bg-opacity-20 border-2 border-blue-500 border-dashed pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\factorygame\\\\src\\\\components\\\\GameBoard.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GameBoard, \"Vyy+UBxxwp21x5ipfJqPLx3mV/U=\", false, function() {\n    return [\n        _store_gameStore__WEBPACK_IMPORTED_MODULE_2__.useGameStore,\n        react_dnd__WEBPACK_IMPORTED_MODULE_6__.useDrop\n    ];\n});\n_c = GameBoard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GameBoard);\nvar _c;\n$RefreshReg$(_c, \"GameBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/GameBoard.tsx\n"));

/***/ })

});