'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useDrop } from 'react-dnd';
import { useGameStore } from '@/store/gameStore';
import { ComponentType, Position } from '@/types/game';
import { screenToGrid, gridToScreen } from '@/utils/helpers';
import FactoryComponent from './FactoryComponent';
import GridOverlay from './GridOverlay';
import ConnectionLines from './ConnectionLines';

const CELL_SIZE = 40;
const BOARD_WIDTH = 2000;
const BOARD_HEIGHT = 2000;

const GameBoard: React.FC = () => {
  const boardRef = useRef<HTMLDivElement>(null);
  const [viewOffset, setViewOffset] = useState<Position>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<Position>({ x: 0, y: 0 });
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);

  const {
    components,
    addComponent,
    moveComponent,
    removeComponent,
    isRunning,
    updateSimulation,
  } = useGameStore();

  // Animation loop for simulation
  useEffect(() => {
    if (!isRunning) return;

    const interval = setInterval(() => {
      updateSimulation();
    }, 100);

    return () => clearInterval(interval);
  }, [isRunning, updateSimulation]);

  // Drop handler for adding components
  const [{ isOver }, drop] = useDrop({
    accept: 'component',
    drop: (item: { type: ComponentType }, monitor) => {
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset || !boardRef.current) return;

      const boardRect = boardRef.current.getBoundingClientRect();
      const screenPos = {
        x: clientOffset.x - boardRect.left,
        y: clientOffset.y - boardRect.top,
      };

      const gridPos = screenToGrid(screenPos, CELL_SIZE, viewOffset);
      addComponent(item.type, gridPos);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  // Pan handling
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button === 1 || (e.button === 0 && e.ctrlKey)) { // Middle mouse or Ctrl+click
      setIsDragging(true);
      setDragStart({ x: e.clientX - viewOffset.x, y: e.clientY - viewOffset.y });
      e.preventDefault();
    }
  }, [viewOffset]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging) {
      setViewOffset({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete' && selectedComponent) {
        removeComponent(selectedComponent);
        setSelectedComponent(null);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedComponent, removeComponent]);

  // Combine refs
  const combinedRef = useCallback((node: HTMLDivElement) => {
    boardRef.current = node;
    drop(node);
  }, [drop]);

  return (
    <div
      ref={combinedRef}
      className={`relative w-full h-full overflow-hidden bg-gray-900 cursor-${isDragging ? 'grabbing' : 'grab'} ${
        isOver ? 'bg-gray-800' : ''
      }`}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
    >
      {/* Grid overlay */}
      <GridOverlay
        cellSize={CELL_SIZE}
        offset={viewOffset}
        width={BOARD_WIDTH}
        height={BOARD_HEIGHT}
      />

      {/* Connection lines */}
      <ConnectionLines
        components={components}
        cellSize={CELL_SIZE}
        viewOffset={viewOffset}
      />

      {/* Components */}
      <div
        className="absolute"
        style={{
          transform: `translate(${viewOffset.x}px, ${viewOffset.y}px)`,
          width: BOARD_WIDTH,
          height: BOARD_HEIGHT,
        }}
      >
        {Array.from(components.entries()).map(([id, component]) => {
          const screenPos = gridToScreen(component.position, CELL_SIZE);
          return (
            <FactoryComponent
              key={id}
              component={component}
              position={screenPos}
              cellSize={CELL_SIZE}
              isSelected={selectedComponent === id}
              onSelect={() => setSelectedComponent(id)}
              onMove={(newGridPos) => moveComponent(id, newGridPos)}
            />
          );
        })}
      </div>

      {/* Instructions overlay */}
      {components.size === 0 && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center text-gray-400">
            <h3 className="text-xl font-semibold mb-2">Welcome to Factory Builder!</h3>
            <p className="mb-1">Drag components from the left panel to start building</p>
            <p className="mb-1">Middle-click or Ctrl+click to pan the view</p>
            <p>Press Delete to remove selected components</p>
          </div>
        </div>
      )}

      {/* Drop indicator */}
      {isOver && (
        <div className="absolute inset-0 bg-blue-500 bg-opacity-20 border-2 border-blue-500 border-dashed pointer-events-none" />
      )}
    </div>
  );
};

export default GameBoard;
