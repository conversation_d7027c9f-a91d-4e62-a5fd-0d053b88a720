"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dnd-core";
exports.ids = ["vendor-chunks/dnd-core"];
exports.modules = {

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js":
/*!******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBeginDrag: () => (/* binding */ createBeginDrag)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./local/setClientOffset.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\n\nconst ResetCoordinatesAction = {\n    type: _types_js__WEBPACK_IMPORTED_MODULE_1__.INIT_COORDS,\n    payload: {\n        clientOffset: null,\n        sourceClientOffset: null\n    }\n};\nfunction createBeginDrag(manager) {\n    return function beginDrag(sourceIds = [], options = {\n        publishSource: true\n    }) {\n        const { publishSource =true , clientOffset , getSourceClientOffset ,  } = options;\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        // Initialize the coordinates using the client offset\n        manager.dispatch((0,_local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__.setClientOffset)(clientOffset));\n        verifyInvariants(sourceIds, monitor, registry);\n        // Get the draggable source\n        const sourceId = getDraggableSource(sourceIds, monitor);\n        if (sourceId == null) {\n            manager.dispatch(ResetCoordinatesAction);\n            return;\n        }\n        // Get the source client offset\n        let sourceClientOffset = null;\n        if (clientOffset) {\n            if (!getSourceClientOffset) {\n                throw new Error('getSourceClientOffset must be defined');\n            }\n            verifyGetSourceClientOffsetIsFunction(getSourceClientOffset);\n            sourceClientOffset = getSourceClientOffset(sourceId);\n        }\n        // Initialize the full coordinates\n        manager.dispatch((0,_local_setClientOffset_js__WEBPACK_IMPORTED_MODULE_2__.setClientOffset)(clientOffset, sourceClientOffset));\n        const source = registry.getSource(sourceId);\n        const item = source.beginDrag(monitor, sourceId);\n        // If source.beginDrag returns null, this is an indicator to cancel the drag\n        if (item == null) {\n            return undefined;\n        }\n        verifyItemIsObject(item);\n        registry.pinSource(sourceId);\n        const itemType = registry.getSourceType(sourceId);\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.BEGIN_DRAG,\n            payload: {\n                itemType,\n                item,\n                sourceId,\n                clientOffset: clientOffset || null,\n                sourceClientOffset: sourceClientOffset || null,\n                isSourcePublic: !!publishSource\n            }\n        };\n    };\n}\nfunction verifyInvariants(sourceIds, monitor, registry) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.isDragging(), 'Cannot call beginDrag while dragging.');\n    sourceIds.forEach(function(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(registry.getSource(sourceId), 'Expected sourceIds to be registered.');\n    });\n}\nfunction verifyGetSourceClientOffsetIsFunction(getSourceClientOffset) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof getSourceClientOffset === 'function', 'When clientOffset is provided, getSourceClientOffset must be a function.');\n}\nfunction verifyItemIsObject(item) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)((0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__.isObject)(item), 'Item must be an object.');\n}\nfunction getDraggableSource(sourceIds, monitor) {\n    let sourceId = null;\n    for(let i = sourceIds.length - 1; i >= 0; i--){\n        if (monitor.canDragSource(sourceIds[i])) {\n            sourceId = sourceIds[i];\n            break;\n        }\n    }\n    return sourceId;\n}\n\n//# sourceMappingURL=beginDrag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/drop.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDrop: () => (/* binding */ createDrop)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\nfunction createDrop(manager) {\n    return function drop(options = {}) {\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        verifyInvariants(monitor);\n        const targetIds = getDroppableTargets(monitor);\n        // Multiple actions are dispatched here, which is why this doesn't return an action\n        targetIds.forEach((targetId, index)=>{\n            const dropResult = determineDropResult(targetId, index, registry, monitor);\n            const action = {\n                type: _types_js__WEBPACK_IMPORTED_MODULE_1__.DROP,\n                payload: {\n                    dropResult: _objectSpread({}, options, dropResult)\n                }\n            };\n            manager.dispatch(action);\n        });\n    };\n}\nfunction verifyInvariants(monitor) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call drop while not dragging.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.didDrop(), 'Cannot call drop twice during one drag operation.');\n}\nfunction determineDropResult(targetId, index, registry, monitor) {\n    const target = registry.getTarget(targetId);\n    let dropResult = target ? target.drop(monitor, targetId) : undefined;\n    verifyDropResultType(dropResult);\n    if (typeof dropResult === 'undefined') {\n        dropResult = index === 0 ? {} : monitor.getDropResult();\n    }\n    return dropResult;\n}\nfunction verifyDropResultType(dropResult) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof dropResult === 'undefined' || (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(dropResult), 'Drop result must either be an object or undefined.');\n}\nfunction getDroppableTargets(monitor) {\n    const targetIds = monitor.getTargetIds().filter(monitor.canDropOnTarget, monitor);\n    targetIds.reverse();\n    return targetIds;\n}\n\n//# sourceMappingURL=drop.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js":
/*!****************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEndDrag: () => (/* binding */ createEndDrag)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\nfunction createEndDrag(manager) {\n    return function endDrag() {\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        verifyIsDragging(monitor);\n        const sourceId = monitor.getSourceId();\n        if (sourceId != null) {\n            const source = registry.getSource(sourceId, true);\n            source.endDrag(monitor, sourceId);\n            registry.unpinSource();\n        }\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.END_DRAG\n        };\n    };\n}\nfunction verifyIsDragging(monitor) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call endDrag while not dragging.');\n}\n\n//# sourceMappingURL=endDrag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2VuZERyYWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlEO0FBQ1g7QUFDL0I7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLCtDQUFRO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrREFBUztBQUNiOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXGFjdGlvbnNcXGRyYWdEcm9wXFxlbmREcmFnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludmFyaWFudCB9IGZyb20gJ0ByZWFjdC1kbmQvaW52YXJpYW50JztcbmltcG9ydCB7IEVORF9EUkFHIH0gZnJvbSAnLi90eXBlcy5qcyc7XG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlRW5kRHJhZyhtYW5hZ2VyKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIGVuZERyYWcoKSB7XG4gICAgICAgIGNvbnN0IG1vbml0b3IgPSBtYW5hZ2VyLmdldE1vbml0b3IoKTtcbiAgICAgICAgY29uc3QgcmVnaXN0cnkgPSBtYW5hZ2VyLmdldFJlZ2lzdHJ5KCk7XG4gICAgICAgIHZlcmlmeUlzRHJhZ2dpbmcobW9uaXRvcik7XG4gICAgICAgIGNvbnN0IHNvdXJjZUlkID0gbW9uaXRvci5nZXRTb3VyY2VJZCgpO1xuICAgICAgICBpZiAoc291cmNlSWQgIT0gbnVsbCkge1xuICAgICAgICAgICAgY29uc3Qgc291cmNlID0gcmVnaXN0cnkuZ2V0U291cmNlKHNvdXJjZUlkLCB0cnVlKTtcbiAgICAgICAgICAgIHNvdXJjZS5lbmREcmFnKG1vbml0b3IsIHNvdXJjZUlkKTtcbiAgICAgICAgICAgIHJlZ2lzdHJ5LnVucGluU291cmNlKCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHR5cGU6IEVORF9EUkFHXG4gICAgICAgIH07XG4gICAgfTtcbn1cbmZ1bmN0aW9uIHZlcmlmeUlzRHJhZ2dpbmcobW9uaXRvcikge1xuICAgIGludmFyaWFudChtb25pdG9yLmlzRHJhZ2dpbmcoKSwgJ0Nhbm5vdCBjYWxsIGVuZERyYWcgd2hpbGUgbm90IGRyYWdnaW5nLicpO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1lbmREcmFnLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/hover.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHover: () => (/* binding */ createHover)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/matchesType.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\nfunction createHover(manager) {\n    return function hover(targetIdsArg, { clientOffset  } = {}) {\n        verifyTargetIdsIsArray(targetIdsArg);\n        const targetIds = targetIdsArg.slice(0);\n        const monitor = manager.getMonitor();\n        const registry = manager.getRegistry();\n        const draggedItemType = monitor.getItemType();\n        removeNonMatchingTargetIds(targetIds, registry, draggedItemType);\n        checkInvariants(targetIds, monitor, registry);\n        hoverAllTargets(targetIds, monitor, registry);\n        return {\n            type: _types_js__WEBPACK_IMPORTED_MODULE_1__.HOVER,\n            payload: {\n                targetIds,\n                clientOffset: clientOffset || null\n            }\n        };\n    };\n}\nfunction verifyTargetIdsIsArray(targetIdsArg) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(Array.isArray(targetIdsArg), 'Expected targetIds to be an array.');\n}\nfunction checkInvariants(targetIds, monitor, registry) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), 'Cannot call hover while not dragging.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.didDrop(), 'Cannot call hover after drop.');\n    for(let i = 0; i < targetIds.length; i++){\n        const targetId = targetIds[i];\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(targetIds.lastIndexOf(targetId) === i, 'Expected targetIds to be unique in the passed array.');\n        const target = registry.getTarget(targetId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(target, 'Expected targetIds to be registered.');\n    }\n}\nfunction removeNonMatchingTargetIds(targetIds, registry, draggedItemType) {\n    // Remove those targetIds that don't match the targetType.  This\n    // fixes shallow isOver which would only be non-shallow because of\n    // non-matching targets.\n    for(let i = targetIds.length - 1; i >= 0; i--){\n        const targetId = targetIds[i];\n        const targetType = registry.getTargetType(targetId);\n        if (!(0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType)) {\n            targetIds.splice(i, 1);\n        }\n    }\n}\nfunction hoverAllTargets(targetIds, monitor, registry) {\n    // Finally call hover on all matching targets.\n    targetIds.forEach(function(targetId) {\n        const target = registry.getTarget(targetId);\n        target.hover(monitor, targetId);\n    });\n}\n\n//# sourceMappingURL=hover.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BEGIN_DRAG: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG),\n/* harmony export */   DROP: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.DROP),\n/* harmony export */   END_DRAG: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG),\n/* harmony export */   HOVER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.HOVER),\n/* harmony export */   INIT_COORDS: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS),\n/* harmony export */   PUBLISH_DRAG_SOURCE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE),\n/* harmony export */   createDragDropActions: () => (/* binding */ createDragDropActions)\n/* harmony export */ });\n/* harmony import */ var _beginDrag_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./beginDrag.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/beginDrag.js\");\n/* harmony import */ var _drop_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./drop.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/drop.js\");\n/* harmony import */ var _endDrag_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./endDrag.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/endDrag.js\");\n/* harmony import */ var _hover_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hover.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/hover.js\");\n/* harmony import */ var _publishDragSource_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./publishDragSource.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\n\n\n\n\n\nfunction createDragDropActions(manager) {\n    return {\n        beginDrag: (0,_beginDrag_js__WEBPACK_IMPORTED_MODULE_1__.createBeginDrag)(manager),\n        publishDragSource: (0,_publishDragSource_js__WEBPACK_IMPORTED_MODULE_2__.createPublishDragSource)(manager),\n        hover: (0,_hover_js__WEBPACK_IMPORTED_MODULE_3__.createHover)(manager),\n        drop: (0,_drop_js__WEBPACK_IMPORTED_MODULE_4__.createDrop)(manager),\n        endDrag: (0,_endDrag_js__WEBPACK_IMPORTED_MODULE_5__.createEndDrag)(manager)\n    };\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBaUQ7QUFDVjtBQUNNO0FBQ0o7QUFDd0I7QUFDdEM7QUFDcEI7QUFDUDtBQUNBLG1CQUFtQiw4REFBZTtBQUNsQywyQkFBMkIsOEVBQXVCO0FBQ2xELGVBQWUsc0RBQVc7QUFDMUIsY0FBYyxvREFBVTtBQUN4QixpQkFBaUIsMERBQWE7QUFDOUI7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb3dubG9hZHNcXGZhY3RvcnlnYW1lXFxub2RlX21vZHVsZXNcXGRuZC1jb3JlXFxkaXN0XFxhY3Rpb25zXFxkcmFnRHJvcFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQmVnaW5EcmFnIH0gZnJvbSAnLi9iZWdpbkRyYWcuanMnO1xuaW1wb3J0IHsgY3JlYXRlRHJvcCB9IGZyb20gJy4vZHJvcC5qcyc7XG5pbXBvcnQgeyBjcmVhdGVFbmREcmFnIH0gZnJvbSAnLi9lbmREcmFnLmpzJztcbmltcG9ydCB7IGNyZWF0ZUhvdmVyIH0gZnJvbSAnLi9ob3Zlci5qcyc7XG5pbXBvcnQgeyBjcmVhdGVQdWJsaXNoRHJhZ1NvdXJjZSB9IGZyb20gJy4vcHVibGlzaERyYWdTb3VyY2UuanMnO1xuZXhwb3J0ICogZnJvbSAnLi90eXBlcy5qcyc7XG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlRHJhZ0Ryb3BBY3Rpb25zKG1hbmFnZXIpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBiZWdpbkRyYWc6IGNyZWF0ZUJlZ2luRHJhZyhtYW5hZ2VyKSxcbiAgICAgICAgcHVibGlzaERyYWdTb3VyY2U6IGNyZWF0ZVB1Ymxpc2hEcmFnU291cmNlKG1hbmFnZXIpLFxuICAgICAgICBob3ZlcjogY3JlYXRlSG92ZXIobWFuYWdlciksXG4gICAgICAgIGRyb3A6IGNyZWF0ZURyb3AobWFuYWdlciksXG4gICAgICAgIGVuZERyYWc6IGNyZWF0ZUVuZERyYWcobWFuYWdlcilcbiAgICB9O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js":
/*!******************************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setClientOffset: () => (/* binding */ setClientOffset)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\nfunction setClientOffset(clientOffset, sourceClientOffset) {\n    return {\n        type: _types_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS,\n        payload: {\n            sourceClientOffset: sourceClientOffset || null,\n            clientOffset: clientOffset || null\n        }\n    };\n}\n\n//# sourceMappingURL=setClientOffset.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL2xvY2FsL3NldENsaWVudE9mZnNldC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQztBQUNuQztBQUNQO0FBQ0EsY0FBYyxrREFBVztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxkbmQtY29yZVxcZGlzdFxcYWN0aW9uc1xcZHJhZ0Ryb3BcXGxvY2FsXFxzZXRDbGllbnRPZmZzZXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSU5JVF9DT09SRFMgfSBmcm9tICcuLi90eXBlcy5qcyc7XG5leHBvcnQgZnVuY3Rpb24gc2V0Q2xpZW50T2Zmc2V0KGNsaWVudE9mZnNldCwgc291cmNlQ2xpZW50T2Zmc2V0KSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgdHlwZTogSU5JVF9DT09SRFMsXG4gICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgIHNvdXJjZUNsaWVudE9mZnNldDogc291cmNlQ2xpZW50T2Zmc2V0IHx8IG51bGwsXG4gICAgICAgICAgICBjbGllbnRPZmZzZXQ6IGNsaWVudE9mZnNldCB8fCBudWxsXG4gICAgICAgIH1cbiAgICB9O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1zZXRDbGllbnRPZmZzZXQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/local/setClientOffset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js":
/*!**************************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPublishDragSource: () => (/* binding */ createPublishDragSource)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n\nfunction createPublishDragSource(manager) {\n    return function publishDragSource() {\n        const monitor = manager.getMonitor();\n        if (monitor.isDragging()) {\n            return {\n                type: _types_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE\n            };\n        }\n        return;\n    };\n}\n\n//# sourceMappingURL=publishDragSource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL3B1Ymxpc2hEcmFnU291cmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlEO0FBQzFDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMERBQW1CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxkbmQtY29yZVxcZGlzdFxcYWN0aW9uc1xcZHJhZ0Ryb3BcXHB1Ymxpc2hEcmFnU291cmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBVQkxJU0hfRFJBR19TT1VSQ0UgfSBmcm9tICcuL3R5cGVzLmpzJztcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVQdWJsaXNoRHJhZ1NvdXJjZShtYW5hZ2VyKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIHB1Ymxpc2hEcmFnU291cmNlKCkge1xuICAgICAgICBjb25zdCBtb25pdG9yID0gbWFuYWdlci5nZXRNb25pdG9yKCk7XG4gICAgICAgIGlmIChtb25pdG9yLmlzRHJhZ2dpbmcoKSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICB0eXBlOiBQVUJMSVNIX0RSQUdfU09VUkNFXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybjtcbiAgICB9O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wdWJsaXNoRHJhZ1NvdXJjZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/publishDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/dragDrop/types.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BEGIN_DRAG: () => (/* binding */ BEGIN_DRAG),\n/* harmony export */   DROP: () => (/* binding */ DROP),\n/* harmony export */   END_DRAG: () => (/* binding */ END_DRAG),\n/* harmony export */   HOVER: () => (/* binding */ HOVER),\n/* harmony export */   INIT_COORDS: () => (/* binding */ INIT_COORDS),\n/* harmony export */   PUBLISH_DRAG_SOURCE: () => (/* binding */ PUBLISH_DRAG_SOURCE)\n/* harmony export */ });\nconst INIT_COORDS = 'dnd-core/INIT_COORDS';\nconst BEGIN_DRAG = 'dnd-core/BEGIN_DRAG';\nconst PUBLISH_DRAG_SOURCE = 'dnd-core/PUBLISH_DRAG_SOURCE';\nconst HOVER = 'dnd-core/HOVER';\nconst DROP = 'dnd-core/DROP';\nconst END_DRAG = 'dnd-core/END_DRAG';\n\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL2RyYWdEcm9wL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb3dubG9hZHNcXGZhY3RvcnlnYW1lXFxub2RlX21vZHVsZXNcXGRuZC1jb3JlXFxkaXN0XFxhY3Rpb25zXFxkcmFnRHJvcFxcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IElOSVRfQ09PUkRTID0gJ2RuZC1jb3JlL0lOSVRfQ09PUkRTJztcbmV4cG9ydCBjb25zdCBCRUdJTl9EUkFHID0gJ2RuZC1jb3JlL0JFR0lOX0RSQUcnO1xuZXhwb3J0IGNvbnN0IFBVQkxJU0hfRFJBR19TT1VSQ0UgPSAnZG5kLWNvcmUvUFVCTElTSF9EUkFHX1NPVVJDRSc7XG5leHBvcnQgY29uc3QgSE9WRVIgPSAnZG5kLWNvcmUvSE9WRVInO1xuZXhwb3J0IGNvbnN0IERST1AgPSAnZG5kLWNvcmUvRFJPUCc7XG5leHBvcnQgY29uc3QgRU5EX0RSQUcgPSAnZG5kLWNvcmUvRU5EX0RSQUcnO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/actions/registry.js":
/*!********************************************************!*\
  !*** ./node_modules/dnd-core/dist/actions/registry.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_SOURCE: () => (/* binding */ ADD_SOURCE),\n/* harmony export */   ADD_TARGET: () => (/* binding */ ADD_TARGET),\n/* harmony export */   REMOVE_SOURCE: () => (/* binding */ REMOVE_SOURCE),\n/* harmony export */   REMOVE_TARGET: () => (/* binding */ REMOVE_TARGET),\n/* harmony export */   addSource: () => (/* binding */ addSource),\n/* harmony export */   addTarget: () => (/* binding */ addTarget),\n/* harmony export */   removeSource: () => (/* binding */ removeSource),\n/* harmony export */   removeTarget: () => (/* binding */ removeTarget)\n/* harmony export */ });\nconst ADD_SOURCE = 'dnd-core/ADD_SOURCE';\nconst ADD_TARGET = 'dnd-core/ADD_TARGET';\nconst REMOVE_SOURCE = 'dnd-core/REMOVE_SOURCE';\nconst REMOVE_TARGET = 'dnd-core/REMOVE_TARGET';\nfunction addSource(sourceId) {\n    return {\n        type: ADD_SOURCE,\n        payload: {\n            sourceId\n        }\n    };\n}\nfunction addTarget(targetId) {\n    return {\n        type: ADD_TARGET,\n        payload: {\n            targetId\n        }\n    };\n}\nfunction removeSource(sourceId) {\n    return {\n        type: REMOVE_SOURCE,\n        payload: {\n            sourceId\n        }\n    };\n}\nfunction removeTarget(targetId) {\n    return {\n        type: REMOVE_TARGET,\n        payload: {\n            targetId\n        }\n    };\n}\n\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9hY3Rpb25zL3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXGFjdGlvbnNcXHJlZ2lzdHJ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBBRERfU09VUkNFID0gJ2RuZC1jb3JlL0FERF9TT1VSQ0UnO1xuZXhwb3J0IGNvbnN0IEFERF9UQVJHRVQgPSAnZG5kLWNvcmUvQUREX1RBUkdFVCc7XG5leHBvcnQgY29uc3QgUkVNT1ZFX1NPVVJDRSA9ICdkbmQtY29yZS9SRU1PVkVfU09VUkNFJztcbmV4cG9ydCBjb25zdCBSRU1PVkVfVEFSR0VUID0gJ2RuZC1jb3JlL1JFTU9WRV9UQVJHRVQnO1xuZXhwb3J0IGZ1bmN0aW9uIGFkZFNvdXJjZShzb3VyY2VJZCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IEFERF9TT1VSQ0UsXG4gICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgIHNvdXJjZUlkXG4gICAgICAgIH1cbiAgICB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIGFkZFRhcmdldCh0YXJnZXRJZCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IEFERF9UQVJHRVQsXG4gICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgIHRhcmdldElkXG4gICAgICAgIH1cbiAgICB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIHJlbW92ZVNvdXJjZShzb3VyY2VJZCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IFJFTU9WRV9TT1VSQ0UsXG4gICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgIHNvdXJjZUlkXG4gICAgICAgIH1cbiAgICB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIHJlbW92ZVRhcmdldCh0YXJnZXRJZCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHR5cGU6IFJFTU9WRV9UQVJHRVQsXG4gICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgIHRhcmdldElkXG4gICAgICAgIH1cbiAgICB9O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWdpc3RyeS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/actions/registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragDropManagerImpl: () => (/* binding */ DragDropManagerImpl)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/index.js\");\n\nclass DragDropManagerImpl {\n    receiveBackend(backend) {\n        this.backend = backend;\n    }\n    getMonitor() {\n        return this.monitor;\n    }\n    getBackend() {\n        return this.backend;\n    }\n    getRegistry() {\n        return this.monitor.registry;\n    }\n    getActions() {\n        /* eslint-disable-next-line @typescript-eslint/no-this-alias */ const manager = this;\n        const { dispatch  } = this.store;\n        function bindActionCreator(actionCreator) {\n            return (...args)=>{\n                const action = actionCreator.apply(manager, args);\n                if (typeof action !== 'undefined') {\n                    dispatch(action);\n                }\n            };\n        }\n        const actions = (0,_actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.createDragDropActions)(this);\n        return Object.keys(actions).reduce((boundActions, key)=>{\n            const action = actions[key];\n            boundActions[key] = bindActionCreator(action);\n            return boundActions;\n        }, {});\n    }\n    dispatch(action) {\n        this.store.dispatch(action);\n    }\n    constructor(store, monitor){\n        this.isSetUp = false;\n        this.handleRefCountChange = ()=>{\n            const shouldSetUp = this.store.getState().refCount > 0;\n            if (this.backend) {\n                if (shouldSetUp && !this.isSetUp) {\n                    this.backend.setup();\n                    this.isSetUp = true;\n                } else if (!shouldSetUp && this.isSetUp) {\n                    this.backend.teardown();\n                    this.isSetUp = false;\n                }\n            }\n        };\n        this.store = store;\n        this.monitor = monitor;\n        store.subscribe(this.handleRefCountChange);\n    }\n}\n\n//# sourceMappingURL=DragDropManagerImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragDropMonitorImpl: () => (/* binding */ DragDropMonitorImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _utils_coords_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/coords.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/coords.js\");\n/* harmony import */ var _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/dirtiness.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\");\n/* harmony import */ var _utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/matchesType.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\");\n\n\n\n\nclass DragDropMonitorImpl {\n    subscribeToStateChange(listener, options = {}) {\n        const { handlerIds  } = options;\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof listener === 'function', 'listener must be a function.');\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof handlerIds === 'undefined' || Array.isArray(handlerIds), 'handlerIds, when specified, must be an array of strings.');\n        let prevStateId = this.store.getState().stateId;\n        const handleChange = ()=>{\n            const state = this.store.getState();\n            const currentStateId = state.stateId;\n            try {\n                const canSkipListener = currentStateId === prevStateId || currentStateId === prevStateId + 1 && !(0,_utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_1__.areDirty)(state.dirtyHandlerIds, handlerIds);\n                if (!canSkipListener) {\n                    listener();\n                }\n            } finally{\n                prevStateId = currentStateId;\n            }\n        };\n        return this.store.subscribe(handleChange);\n    }\n    subscribeToOffsetChange(listener) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof listener === 'function', 'listener must be a function.');\n        let previousState = this.store.getState().dragOffset;\n        const handleChange = ()=>{\n            const nextState = this.store.getState().dragOffset;\n            if (nextState === previousState) {\n                return;\n            }\n            previousState = nextState;\n            listener();\n        };\n        return this.store.subscribe(handleChange);\n    }\n    canDragSource(sourceId) {\n        if (!sourceId) {\n            return false;\n        }\n        const source = this.registry.getSource(sourceId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(source, `Expected to find a valid source. sourceId=${sourceId}`);\n        if (this.isDragging()) {\n            return false;\n        }\n        return source.canDrag(this, sourceId);\n    }\n    canDropOnTarget(targetId) {\n        // undefined on initial render\n        if (!targetId) {\n            return false;\n        }\n        const target = this.registry.getTarget(targetId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(target, `Expected to find a valid target. targetId=${targetId}`);\n        if (!this.isDragging() || this.didDrop()) {\n            return false;\n        }\n        const targetType = this.registry.getTargetType(targetId);\n        const draggedItemType = this.getItemType();\n        return (0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType) && target.canDrop(this, targetId);\n    }\n    isDragging() {\n        return Boolean(this.getItemType());\n    }\n    isDraggingSource(sourceId) {\n        // undefined on initial render\n        if (!sourceId) {\n            return false;\n        }\n        const source = this.registry.getSource(sourceId, true);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(source, `Expected to find a valid source. sourceId=${sourceId}`);\n        if (!this.isDragging() || !this.isSourcePublic()) {\n            return false;\n        }\n        const sourceType = this.registry.getSourceType(sourceId);\n        const draggedItemType = this.getItemType();\n        if (sourceType !== draggedItemType) {\n            return false;\n        }\n        return source.isDragging(this, sourceId);\n    }\n    isOverTarget(targetId, options = {\n        shallow: false\n    }) {\n        // undefined on initial render\n        if (!targetId) {\n            return false;\n        }\n        const { shallow  } = options;\n        if (!this.isDragging()) {\n            return false;\n        }\n        const targetType = this.registry.getTargetType(targetId);\n        const draggedItemType = this.getItemType();\n        if (draggedItemType && !(0,_utils_matchesType_js__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType)) {\n            return false;\n        }\n        const targetIds = this.getTargetIds();\n        if (!targetIds.length) {\n            return false;\n        }\n        const index = targetIds.indexOf(targetId);\n        if (shallow) {\n            return index === targetIds.length - 1;\n        } else {\n            return index > -1;\n        }\n    }\n    getItemType() {\n        return this.store.getState().dragOperation.itemType;\n    }\n    getItem() {\n        return this.store.getState().dragOperation.item;\n    }\n    getSourceId() {\n        return this.store.getState().dragOperation.sourceId;\n    }\n    getTargetIds() {\n        return this.store.getState().dragOperation.targetIds;\n    }\n    getDropResult() {\n        return this.store.getState().dragOperation.dropResult;\n    }\n    didDrop() {\n        return this.store.getState().dragOperation.didDrop;\n    }\n    isSourcePublic() {\n        return Boolean(this.store.getState().dragOperation.isSourcePublic);\n    }\n    getInitialClientOffset() {\n        return this.store.getState().dragOffset.initialClientOffset;\n    }\n    getInitialSourceClientOffset() {\n        return this.store.getState().dragOffset.initialSourceClientOffset;\n    }\n    getClientOffset() {\n        return this.store.getState().dragOffset.clientOffset;\n    }\n    getSourceClientOffset() {\n        return (0,_utils_coords_js__WEBPACK_IMPORTED_MODULE_3__.getSourceClientOffset)(this.store.getState().dragOffset);\n    }\n    getDifferenceFromInitialOffset() {\n        return (0,_utils_coords_js__WEBPACK_IMPORTED_MODULE_3__.getDifferenceFromInitialOffset)(this.store.getState().dragOffset);\n    }\n    constructor(store, registry){\n        this.store = store;\n        this.registry = registry;\n    }\n}\n\n//# sourceMappingURL=DragDropMonitorImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js":
/*!*******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandlerRegistryImpl: () => (/* binding */ HandlerRegistryImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_asap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/asap */ \"(ssr)/./node_modules/@react-dnd/asap/dist/index.js\");\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _contracts_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contracts.js */ \"(ssr)/./node_modules/dnd-core/dist/contracts.js\");\n/* harmony import */ var _interfaces_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interfaces.js */ \"(ssr)/./node_modules/dnd-core/dist/interfaces.js\");\n/* harmony import */ var _utils_getNextUniqueId_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getNextUniqueId.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js\");\n\n\n\n\n\n\nfunction getNextHandlerId(role) {\n    const id = (0,_utils_getNextUniqueId_js__WEBPACK_IMPORTED_MODULE_2__.getNextUniqueId)().toString();\n    switch(role){\n        case _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE:\n            return `S${id}`;\n        case _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET:\n            return `T${id}`;\n        default:\n            throw new Error(`Unknown Handler Role: ${role}`);\n    }\n}\nfunction parseRoleFromHandlerId(handlerId) {\n    switch(handlerId[0]){\n        case 'S':\n            return _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE;\n        case 'T':\n            return _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET;\n        default:\n            throw new Error(`Cannot parse handler ID: ${handlerId}`);\n    }\n}\nfunction mapContainsValue(map, searchValue) {\n    const entries = map.entries();\n    let isDone = false;\n    do {\n        const { done , value: [, value] ,  } = entries.next();\n        if (value === searchValue) {\n            return true;\n        }\n        isDone = !!done;\n    }while (!isDone)\n    return false;\n}\nclass HandlerRegistryImpl {\n    addSource(type, source) {\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateType)(type);\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateSourceContract)(source);\n        const sourceId = this.addHandler(_interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE, type, source);\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.addSource)(sourceId));\n        return sourceId;\n    }\n    addTarget(type, target) {\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateType)(type, true);\n        (0,_contracts_js__WEBPACK_IMPORTED_MODULE_4__.validateTargetContract)(target);\n        const targetId = this.addHandler(_interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET, type, target);\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.addTarget)(targetId));\n        return targetId;\n    }\n    containsHandler(handler) {\n        return mapContainsValue(this.dragSources, handler) || mapContainsValue(this.dropTargets, handler);\n    }\n    getSource(sourceId, includePinned = false) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isSourceId(sourceId), 'Expected a valid source ID.');\n        const isPinned = includePinned && sourceId === this.pinnedSourceId;\n        const source = isPinned ? this.pinnedSource : this.dragSources.get(sourceId);\n        return source;\n    }\n    getTarget(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isTargetId(targetId), 'Expected a valid target ID.');\n        return this.dropTargets.get(targetId);\n    }\n    getSourceType(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isSourceId(sourceId), 'Expected a valid source ID.');\n        return this.types.get(sourceId);\n    }\n    getTargetType(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.isTargetId(targetId), 'Expected a valid target ID.');\n        return this.types.get(targetId);\n    }\n    isSourceId(handlerId) {\n        const role = parseRoleFromHandlerId(handlerId);\n        return role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE;\n    }\n    isTargetId(handlerId) {\n        const role = parseRoleFromHandlerId(handlerId);\n        return role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET;\n    }\n    removeSource(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.getSource(sourceId), 'Expected an existing source.');\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.removeSource)(sourceId));\n        (0,_react_dnd_asap__WEBPACK_IMPORTED_MODULE_0__.asap)(()=>{\n            this.dragSources.delete(sourceId);\n            this.types.delete(sourceId);\n        });\n    }\n    removeTarget(targetId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.getTarget(targetId), 'Expected an existing target.');\n        this.store.dispatch((0,_actions_registry_js__WEBPACK_IMPORTED_MODULE_5__.removeTarget)(targetId));\n        this.dropTargets.delete(targetId);\n        this.types.delete(targetId);\n    }\n    pinSource(sourceId) {\n        const source = this.getSource(sourceId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(source, 'Expected an existing source.');\n        this.pinnedSourceId = sourceId;\n        this.pinnedSource = source;\n    }\n    unpinSource() {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(this.pinnedSource, 'No source is pinned at the time.');\n        this.pinnedSourceId = null;\n        this.pinnedSource = null;\n    }\n    addHandler(role, type, handler) {\n        const id = getNextHandlerId(role);\n        this.types.set(id, type);\n        if (role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE) {\n            this.dragSources.set(id, handler);\n        } else if (role === _interfaces_js__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET) {\n            this.dropTargets.set(id, handler);\n        }\n        return id;\n    }\n    constructor(store){\n        this.types = new Map();\n        this.dragSources = new Map();\n        this.dropTargets = new Map();\n        this.pinnedSourceId = null;\n        this.pinnedSource = null;\n        this.store = store;\n    }\n}\n\n//# sourceMappingURL=HandlerRegistryImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/contracts.js":
/*!*************************************************!*\
  !*** ./node_modules/dnd-core/dist/contracts.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateSourceContract: () => (/* binding */ validateSourceContract),\n/* harmony export */   validateTargetContract: () => (/* binding */ validateTargetContract),\n/* harmony export */   validateType: () => (/* binding */ validateType)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n\nfunction validateSourceContract(source) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.canDrag === 'function', 'Expected canDrag to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.beginDrag === 'function', 'Expected beginDrag to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.endDrag === 'function', 'Expected endDrag to be a function.');\n}\nfunction validateTargetContract(target) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.canDrop === 'function', 'Expected canDrop to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.hover === 'function', 'Expected hover to be a function.');\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.drop === 'function', 'Expected beginDrag to be a function.');\n}\nfunction validateType(type, allowArray) {\n    if (allowArray && Array.isArray(type)) {\n        type.forEach((t)=>validateType(t, false)\n        );\n        return;\n    }\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof type === 'string' || typeof type === 'symbol', allowArray ? 'Type can only be a string, a symbol, or an array of either.' : 'Type can only be a string or a symbol.');\n}\n\n//# sourceMappingURL=contracts.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/contracts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/createDragDropManager.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/createDragDropManager.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDragDropManager: () => (/* binding */ createDragDropManager)\n/* harmony export */ });\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/es/redux.js\");\n/* harmony import */ var _classes_DragDropManagerImpl_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./classes/DragDropManagerImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/DragDropManagerImpl.js\");\n/* harmony import */ var _classes_DragDropMonitorImpl_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./classes/DragDropMonitorImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/DragDropMonitorImpl.js\");\n/* harmony import */ var _classes_HandlerRegistryImpl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./classes/HandlerRegistryImpl.js */ \"(ssr)/./node_modules/dnd-core/dist/classes/HandlerRegistryImpl.js\");\n/* harmony import */ var _reducers_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./reducers/index.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/index.js\");\n\n\n\n\n\nfunction createDragDropManager(backendFactory, globalContext = undefined, backendOptions = {}, debugMode = false) {\n    const store = makeStoreInstance(debugMode);\n    const monitor = new _classes_DragDropMonitorImpl_js__WEBPACK_IMPORTED_MODULE_0__.DragDropMonitorImpl(store, new _classes_HandlerRegistryImpl_js__WEBPACK_IMPORTED_MODULE_1__.HandlerRegistryImpl(store));\n    const manager = new _classes_DragDropManagerImpl_js__WEBPACK_IMPORTED_MODULE_2__.DragDropManagerImpl(store, monitor);\n    const backend = backendFactory(manager, globalContext, backendOptions);\n    manager.receiveBackend(backend);\n    return manager;\n}\nfunction makeStoreInstance(debugMode) {\n    // TODO: if we ever make a react-native version of this,\n    // we'll need to consider how to pull off dev-tooling\n    const reduxDevTools = typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION__;\n    return (0,redux__WEBPACK_IMPORTED_MODULE_3__.createStore)(_reducers_index_js__WEBPACK_IMPORTED_MODULE_4__.reduce, debugMode && reduxDevTools && reduxDevTools({\n        name: 'dnd-core',\n        instanceId: 'dnd-core'\n    }));\n}\n\n//# sourceMappingURL=createDragDropManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9jcmVhdGVEcmFnRHJvcE1hbmFnZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW9DO0FBQ21DO0FBQ0E7QUFDQTtBQUMxQjtBQUN0Qyw2RkFBNkY7QUFDcEc7QUFDQSx3QkFBd0IsZ0ZBQW1CLFlBQVksZ0ZBQW1CO0FBQzFFLHdCQUF3QixnRkFBbUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsa0RBQVcsQ0FBQyxzREFBTTtBQUM3QjtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXGNyZWF0ZURyYWdEcm9wTWFuYWdlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVTdG9yZSB9IGZyb20gJ3JlZHV4JztcbmltcG9ydCB7IERyYWdEcm9wTWFuYWdlckltcGwgfSBmcm9tICcuL2NsYXNzZXMvRHJhZ0Ryb3BNYW5hZ2VySW1wbC5qcyc7XG5pbXBvcnQgeyBEcmFnRHJvcE1vbml0b3JJbXBsIH0gZnJvbSAnLi9jbGFzc2VzL0RyYWdEcm9wTW9uaXRvckltcGwuanMnO1xuaW1wb3J0IHsgSGFuZGxlclJlZ2lzdHJ5SW1wbCB9IGZyb20gJy4vY2xhc3Nlcy9IYW5kbGVyUmVnaXN0cnlJbXBsLmpzJztcbmltcG9ydCB7IHJlZHVjZSB9IGZyb20gJy4vcmVkdWNlcnMvaW5kZXguanMnO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZURyYWdEcm9wTWFuYWdlcihiYWNrZW5kRmFjdG9yeSwgZ2xvYmFsQ29udGV4dCA9IHVuZGVmaW5lZCwgYmFja2VuZE9wdGlvbnMgPSB7fSwgZGVidWdNb2RlID0gZmFsc2UpIHtcbiAgICBjb25zdCBzdG9yZSA9IG1ha2VTdG9yZUluc3RhbmNlKGRlYnVnTW9kZSk7XG4gICAgY29uc3QgbW9uaXRvciA9IG5ldyBEcmFnRHJvcE1vbml0b3JJbXBsKHN0b3JlLCBuZXcgSGFuZGxlclJlZ2lzdHJ5SW1wbChzdG9yZSkpO1xuICAgIGNvbnN0IG1hbmFnZXIgPSBuZXcgRHJhZ0Ryb3BNYW5hZ2VySW1wbChzdG9yZSwgbW9uaXRvcik7XG4gICAgY29uc3QgYmFja2VuZCA9IGJhY2tlbmRGYWN0b3J5KG1hbmFnZXIsIGdsb2JhbENvbnRleHQsIGJhY2tlbmRPcHRpb25zKTtcbiAgICBtYW5hZ2VyLnJlY2VpdmVCYWNrZW5kKGJhY2tlbmQpO1xuICAgIHJldHVybiBtYW5hZ2VyO1xufVxuZnVuY3Rpb24gbWFrZVN0b3JlSW5zdGFuY2UoZGVidWdNb2RlKSB7XG4gICAgLy8gVE9ETzogaWYgd2UgZXZlciBtYWtlIGEgcmVhY3QtbmF0aXZlIHZlcnNpb24gb2YgdGhpcyxcbiAgICAvLyB3ZSdsbCBuZWVkIHRvIGNvbnNpZGVyIGhvdyB0byBwdWxsIG9mZiBkZXYtdG9vbGluZ1xuICAgIGNvbnN0IHJlZHV4RGV2VG9vbHMgPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cuX19SRURVWF9ERVZUT09MU19FWFRFTlNJT05fXztcbiAgICByZXR1cm4gY3JlYXRlU3RvcmUocmVkdWNlLCBkZWJ1Z01vZGUgJiYgcmVkdXhEZXZUb29scyAmJiByZWR1eERldlRvb2xzKHtcbiAgICAgICAgbmFtZTogJ2RuZC1jb3JlJyxcbiAgICAgICAgaW5zdGFuY2VJZDogJ2RuZC1jb3JlJ1xuICAgIH0pKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3JlYXRlRHJhZ0Ryb3BNYW5hZ2VyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/createDragDropManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/interfaces.js":
/*!**************************************************!*\
  !*** ./node_modules/dnd-core/dist/interfaces.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandlerRole: () => (/* binding */ HandlerRole)\n/* harmony export */ });\nvar HandlerRole;\n(function(HandlerRole) {\n    HandlerRole[\"SOURCE\"] = \"SOURCE\";\n    HandlerRole[\"TARGET\"] = \"TARGET\";\n})(HandlerRole || (HandlerRole = {}));\n\n//# sourceMappingURL=interfaces.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9pbnRlcmZhY2VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLENBQUMsa0NBQWtDOztBQUVuQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb3dubG9hZHNcXGZhY3RvcnlnYW1lXFxub2RlX21vZHVsZXNcXGRuZC1jb3JlXFxkaXN0XFxpbnRlcmZhY2VzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgSGFuZGxlclJvbGU7XG4oZnVuY3Rpb24oSGFuZGxlclJvbGUpIHtcbiAgICBIYW5kbGVyUm9sZVtcIlNPVVJDRVwiXSA9IFwiU09VUkNFXCI7XG4gICAgSGFuZGxlclJvbGVbXCJUQVJHRVRcIl0gPSBcIlRBUkdFVFwiO1xufSkoSGFuZGxlclJvbGUgfHwgKEhhbmRsZXJSb2xlID0ge30pKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW50ZXJmYWNlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/interfaces.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js":
/*!****************************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/dirtiness.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\");\n/* harmony import */ var _utils_equality_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/equality.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/equality.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n\n\n\n\n\nfunction reduce(// eslint-disable-next-line @typescript-eslint/no-unused-vars\n_state = _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE, action) {\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.HOVER:\n            break;\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.ADD_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.ADD_TARGET:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.REMOVE_TARGET:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_2__.REMOVE_SOURCE:\n            return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE;\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.BEGIN_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.PUBLISH_DRAG_SOURCE:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.END_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_1__.DROP:\n        default:\n            return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.ALL;\n    }\n    const { targetIds =[] , prevTargetIds =[]  } = action.payload;\n    const result = (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_3__.xor)(targetIds, prevTargetIds);\n    const didChange = result.length > 0 || !(0,_utils_equality_js__WEBPACK_IMPORTED_MODULE_4__.areArraysEqual)(targetIds, prevTargetIds);\n    if (!didChange) {\n        return _utils_dirtiness_js__WEBPACK_IMPORTED_MODULE_0__.NONE;\n    }\n    // Check the target ids at the innermost position. If they are valid, add them\n    // to the result\n    const prevInnermostTargetId = prevTargetIds[prevTargetIds.length - 1];\n    const innermostTargetId = targetIds[targetIds.length - 1];\n    if (prevInnermostTargetId !== innermostTargetId) {\n        if (prevInnermostTargetId) {\n            result.push(prevInnermostTargetId);\n        }\n        if (innermostTargetId) {\n            result.push(innermostTargetId);\n        }\n    }\n    return result;\n}\n\n//# sourceMappingURL=dirtyHandlerIds.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js":
/*!***********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dragOffset.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _utils_equality_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/equality.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/equality.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\nconst initialState = {\n    initialSourceClientOffset: null,\n    initialClientOffset: null,\n    clientOffset: null\n};\nfunction reduce(state = initialState, action) {\n    const { payload  } = action;\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG:\n            return {\n                initialSourceClientOffset: payload.sourceClientOffset,\n                initialClientOffset: payload.clientOffset,\n                clientOffset: payload.clientOffset\n            };\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.HOVER:\n            if ((0,_utils_equality_js__WEBPACK_IMPORTED_MODULE_1__.areCoordsEqual)(state.clientOffset, payload.clientOffset)) {\n                return state;\n            }\n            return _objectSpread({}, state, {\n                clientOffset: payload.clientOffset\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG:\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.DROP:\n            return initialState;\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=dragOffset.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9yZWR1Y2Vycy9kcmFnT2Zmc2V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHNCQUFzQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDOEY7QUFDeEM7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsWUFBWSxXQUFXO0FBQ3ZCO0FBQ0EsYUFBYSxtRUFBVztBQUN4QixhQUFhLGtFQUFVO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLDZEQUFLO0FBQ2xCLGdCQUFnQixrRUFBYztBQUM5QjtBQUNBO0FBQ0EsbUNBQW1DO0FBQ25DO0FBQ0EsYUFBYTtBQUNiLGFBQWEsZ0VBQVE7QUFDckIsYUFBYSw0REFBSTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXHJlZHVjZXJzXFxkcmFnT2Zmc2V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIF9kZWZpbmVQcm9wZXJ0eShvYmosIGtleSwgdmFsdWUpIHtcbiAgICBpZiAoa2V5IGluIG9iaikge1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqLCBrZXksIHtcbiAgICAgICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZVxuICAgICAgICB9KTtcbiAgICB9IGVsc2Uge1xuICAgICAgICBvYmpba2V5XSA9IHZhbHVlO1xuICAgIH1cbiAgICByZXR1cm4gb2JqO1xufVxuZnVuY3Rpb24gX29iamVjdFNwcmVhZCh0YXJnZXQpIHtcbiAgICBmb3IodmFyIGkgPSAxOyBpIDwgYXJndW1lbnRzLmxlbmd0aDsgaSsrKXtcbiAgICAgICAgdmFyIHNvdXJjZSA9IGFyZ3VtZW50c1tpXSAhPSBudWxsID8gYXJndW1lbnRzW2ldIDoge307XG4gICAgICAgIHZhciBvd25LZXlzID0gT2JqZWN0LmtleXMoc291cmNlKTtcbiAgICAgICAgaWYgKHR5cGVvZiBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICBvd25LZXlzID0gb3duS2V5cy5jb25jYXQoT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyhzb3VyY2UpLmZpbHRlcihmdW5jdGlvbihzeW0pIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihzb3VyY2UsIHN5bSkuZW51bWVyYWJsZTtcbiAgICAgICAgICAgIH0pKTtcbiAgICAgICAgfVxuICAgICAgICBvd25LZXlzLmZvckVhY2goZnVuY3Rpb24oa2V5KSB7XG4gICAgICAgICAgICBfZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBrZXksIHNvdXJjZVtrZXldKTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiB0YXJnZXQ7XG59XG5pbXBvcnQgeyBCRUdJTl9EUkFHLCBEUk9QLCBFTkRfRFJBRywgSE9WRVIsIElOSVRfQ09PUkRTIH0gZnJvbSAnLi4vYWN0aW9ucy9kcmFnRHJvcC9pbmRleC5qcyc7XG5pbXBvcnQgeyBhcmVDb29yZHNFcXVhbCB9IGZyb20gJy4uL3V0aWxzL2VxdWFsaXR5LmpzJztcbmNvbnN0IGluaXRpYWxTdGF0ZSA9IHtcbiAgICBpbml0aWFsU291cmNlQ2xpZW50T2Zmc2V0OiBudWxsLFxuICAgIGluaXRpYWxDbGllbnRPZmZzZXQ6IG51bGwsXG4gICAgY2xpZW50T2Zmc2V0OiBudWxsXG59O1xuZXhwb3J0IGZ1bmN0aW9uIHJlZHVjZShzdGF0ZSA9IGluaXRpYWxTdGF0ZSwgYWN0aW9uKSB7XG4gICAgY29uc3QgeyBwYXlsb2FkICB9ID0gYWN0aW9uO1xuICAgIHN3aXRjaChhY3Rpb24udHlwZSl7XG4gICAgICAgIGNhc2UgSU5JVF9DT09SRFM6XG4gICAgICAgIGNhc2UgQkVHSU5fRFJBRzpcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgaW5pdGlhbFNvdXJjZUNsaWVudE9mZnNldDogcGF5bG9hZC5zb3VyY2VDbGllbnRPZmZzZXQsXG4gICAgICAgICAgICAgICAgaW5pdGlhbENsaWVudE9mZnNldDogcGF5bG9hZC5jbGllbnRPZmZzZXQsXG4gICAgICAgICAgICAgICAgY2xpZW50T2Zmc2V0OiBwYXlsb2FkLmNsaWVudE9mZnNldFxuICAgICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBIT1ZFUjpcbiAgICAgICAgICAgIGlmIChhcmVDb29yZHNFcXVhbChzdGF0ZS5jbGllbnRPZmZzZXQsIHBheWxvYWQuY2xpZW50T2Zmc2V0KSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBzdGF0ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKHt9LCBzdGF0ZSwge1xuICAgICAgICAgICAgICAgIGNsaWVudE9mZnNldDogcGF5bG9hZC5jbGllbnRPZmZzZXRcbiAgICAgICAgICAgIH0pO1xuICAgICAgICBjYXNlIEVORF9EUkFHOlxuICAgICAgICBjYXNlIERST1A6XG4gICAgICAgICAgICByZXR1cm4gaW5pdGlhbFN0YXRlO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgcmV0dXJuIHN0YXRlO1xuICAgIH1cbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZHJhZ09mZnNldC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/dragOperation.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop/index.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/dragDrop/types.js\");\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\nconst initialState = {\n    itemType: null,\n    item: null,\n    sourceId: null,\n    targetIds: [],\n    dropResult: null,\n    didDrop: false,\n    isSourcePublic: null\n};\nfunction reduce(state = initialState, action) {\n    const { payload  } = action;\n    switch(action.type){\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG:\n            return _objectSpread({}, state, {\n                itemType: payload.itemType,\n                item: payload.item,\n                sourceId: payload.sourceId,\n                isSourcePublic: payload.isSourcePublic,\n                dropResult: null,\n                didDrop: false\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE:\n            return _objectSpread({}, state, {\n                isSourcePublic: true\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.HOVER:\n            return _objectSpread({}, state, {\n                targetIds: payload.targetIds\n            });\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_1__.REMOVE_TARGET:\n            if (state.targetIds.indexOf(payload.targetId) === -1) {\n                return state;\n            }\n            return _objectSpread({}, state, {\n                targetIds: (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_2__.without)(state.targetIds, payload.targetId)\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.DROP:\n            return _objectSpread({}, state, {\n                dropResult: payload.dropResult,\n                didDrop: true,\n                targetIds: []\n            });\n        case _actions_dragDrop_index_js__WEBPACK_IMPORTED_MODULE_0__.END_DRAG:\n            return _objectSpread({}, state, {\n                itemType: null,\n                item: null,\n                sourceId: null,\n                dropResult: null,\n                didDrop: false,\n                isSourcePublic: null,\n                targetIds: []\n            });\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=dragOperation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/index.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _utils_js_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n/* harmony import */ var _dirtyHandlerIds_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dirtyHandlerIds.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dirtyHandlerIds.js\");\n/* harmony import */ var _dragOffset_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dragOffset.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dragOffset.js\");\n/* harmony import */ var _dragOperation_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dragOperation.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/dragOperation.js\");\n/* harmony import */ var _refCount_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./refCount.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js\");\n/* harmony import */ var _stateId_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./stateId.js */ \"(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js\");\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === 'function') {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        });\n    }\n    return target;\n}\n\n\n\n\n\n\nfunction reduce(state = {}, action) {\n    return {\n        dirtyHandlerIds: (0,_dirtyHandlerIds_js__WEBPACK_IMPORTED_MODULE_0__.reduce)(state.dirtyHandlerIds, {\n            type: action.type,\n            payload: _objectSpread({}, action.payload, {\n                prevTargetIds: (0,_utils_js_utils_js__WEBPACK_IMPORTED_MODULE_1__.get)(state, 'dragOperation.targetIds', [])\n            })\n        }),\n        dragOffset: (0,_dragOffset_js__WEBPACK_IMPORTED_MODULE_2__.reduce)(state.dragOffset, action),\n        refCount: (0,_refCount_js__WEBPACK_IMPORTED_MODULE_3__.reduce)(state.refCount, action),\n        dragOperation: (0,_dragOperation_js__WEBPACK_IMPORTED_MODULE_4__.reduce)(state.dragOperation, action),\n        stateId: (0,_stateId_js__WEBPACK_IMPORTED_MODULE_5__.reduce)(state.stateId)\n    };\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js":
/*!*********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/refCount.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/registry.js */ \"(ssr)/./node_modules/dnd-core/dist/actions/registry.js\");\n\nfunction reduce(state = 0, action) {\n    switch(action.type){\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.ADD_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.ADD_TARGET:\n            return state + 1;\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.REMOVE_SOURCE:\n        case _actions_registry_js__WEBPACK_IMPORTED_MODULE_0__.REMOVE_TARGET:\n            return state - 1;\n        default:\n            return state;\n    }\n}\n\n//# sourceMappingURL=refCount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9yZWR1Y2Vycy9yZWZDb3VudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4RjtBQUN2RjtBQUNQO0FBQ0EsYUFBYSw0REFBVTtBQUN2QixhQUFhLDREQUFVO0FBQ3ZCO0FBQ0EsYUFBYSwrREFBYTtBQUMxQixhQUFhLCtEQUFhO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxkbmQtY29yZVxcZGlzdFxccmVkdWNlcnNcXHJlZkNvdW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFERF9TT1VSQ0UsIEFERF9UQVJHRVQsIFJFTU9WRV9TT1VSQ0UsIFJFTU9WRV9UQVJHRVQgfSBmcm9tICcuLi9hY3Rpb25zL3JlZ2lzdHJ5LmpzJztcbmV4cG9ydCBmdW5jdGlvbiByZWR1Y2Uoc3RhdGUgPSAwLCBhY3Rpb24pIHtcbiAgICBzd2l0Y2goYWN0aW9uLnR5cGUpe1xuICAgICAgICBjYXNlIEFERF9TT1VSQ0U6XG4gICAgICAgIGNhc2UgQUREX1RBUkdFVDpcbiAgICAgICAgICAgIHJldHVybiBzdGF0ZSArIDE7XG4gICAgICAgIGNhc2UgUkVNT1ZFX1NPVVJDRTpcbiAgICAgICAgY2FzZSBSRU1PVkVfVEFSR0VUOlxuICAgICAgICAgICAgcmV0dXJuIHN0YXRlIC0gMTtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiBzdGF0ZTtcbiAgICB9XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlZkNvdW50LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/refCount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js":
/*!********************************************************!*\
  !*** ./node_modules/dnd-core/dist/reducers/stateId.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\nfunction reduce(state = 0) {\n    return state + 1;\n}\n\n//# sourceMappingURL=stateId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9yZWR1Y2Vycy9zdGF0ZUlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxkbmQtY29yZVxcZGlzdFxccmVkdWNlcnNcXHN0YXRlSWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHJlZHVjZShzdGF0ZSA9IDApIHtcbiAgICByZXR1cm4gc3RhdGUgKyAxO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdGF0ZUlkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/reducers/stateId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/coords.js":
/*!****************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/coords.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   getDifferenceFromInitialOffset: () => (/* binding */ getDifferenceFromInitialOffset),\n/* harmony export */   getSourceClientOffset: () => (/* binding */ getSourceClientOffset),\n/* harmony export */   subtract: () => (/* binding */ subtract)\n/* harmony export */ });\n/**\n * Coordinate addition\n * @param a The first coordinate\n * @param b The second coordinate\n */ function add(a, b) {\n    return {\n        x: a.x + b.x,\n        y: a.y + b.y\n    };\n}\n/**\n * Coordinate subtraction\n * @param a The first coordinate\n * @param b The second coordinate\n */ function subtract(a, b) {\n    return {\n        x: a.x - b.x,\n        y: a.y - b.y\n    };\n}\n/**\n * Returns the cartesian distance of the drag source component's position, based on its position\n * at the time when the current drag operation has started, and the movement difference.\n *\n * Returns null if no item is being dragged.\n *\n * @param state The offset state to compute from\n */ function getSourceClientOffset(state) {\n    const { clientOffset , initialClientOffset , initialSourceClientOffset  } = state;\n    if (!clientOffset || !initialClientOffset || !initialSourceClientOffset) {\n        return null;\n    }\n    return subtract(add(clientOffset, initialSourceClientOffset), initialClientOffset);\n}\n/**\n * Determines the x,y offset between the client offset and the initial client offset\n *\n * @param state The offset state to compute from\n */ function getDifferenceFromInitialOffset(state) {\n    const { clientOffset , initialClientOffset  } = state;\n    if (!clientOffset || !initialClientOffset) {\n        return null;\n    }\n    return subtract(clientOffset, initialClientOffset);\n}\n\n//# sourceMappingURL=coords.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/coords.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js":
/*!*******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/dirtiness.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL: () => (/* binding */ ALL),\n/* harmony export */   NONE: () => (/* binding */ NONE),\n/* harmony export */   areDirty: () => (/* binding */ areDirty)\n/* harmony export */ });\n/* harmony import */ var _js_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./js_utils.js */ \"(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\");\n\nconst NONE = [];\nconst ALL = [];\nNONE.__IS_NONE__ = true;\nALL.__IS_ALL__ = true;\n/**\n * Determines if the given handler IDs are dirty or not.\n *\n * @param dirtyIds The set of dirty handler ids\n * @param handlerIds The set of handler ids to check\n */ function areDirty(dirtyIds, handlerIds) {\n    if (dirtyIds === NONE) {\n        return false;\n    }\n    if (dirtyIds === ALL || typeof handlerIds === 'undefined') {\n        return true;\n    }\n    const commonIds = (0,_js_utils_js__WEBPACK_IMPORTED_MODULE_0__.intersection)(handlerIds, dirtyIds);\n    return commonIds.length > 0;\n}\n\n//# sourceMappingURL=dirtiness.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9kaXJ0aW5lc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE2QztBQUN0QztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDBEQUFZO0FBQ2xDO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxkbmQtY29yZVxcZGlzdFxcdXRpbHNcXGRpcnRpbmVzcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpbnRlcnNlY3Rpb24gfSBmcm9tICcuL2pzX3V0aWxzLmpzJztcbmV4cG9ydCBjb25zdCBOT05FID0gW107XG5leHBvcnQgY29uc3QgQUxMID0gW107XG5OT05FLl9fSVNfTk9ORV9fID0gdHJ1ZTtcbkFMTC5fX0lTX0FMTF9fID0gdHJ1ZTtcbi8qKlxuICogRGV0ZXJtaW5lcyBpZiB0aGUgZ2l2ZW4gaGFuZGxlciBJRHMgYXJlIGRpcnR5IG9yIG5vdC5cbiAqXG4gKiBAcGFyYW0gZGlydHlJZHMgVGhlIHNldCBvZiBkaXJ0eSBoYW5kbGVyIGlkc1xuICogQHBhcmFtIGhhbmRsZXJJZHMgVGhlIHNldCBvZiBoYW5kbGVyIGlkcyB0byBjaGVja1xuICovIGV4cG9ydCBmdW5jdGlvbiBhcmVEaXJ0eShkaXJ0eUlkcywgaGFuZGxlcklkcykge1xuICAgIGlmIChkaXJ0eUlkcyA9PT0gTk9ORSkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmIChkaXJ0eUlkcyA9PT0gQUxMIHx8IHR5cGVvZiBoYW5kbGVySWRzID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgY29uc3QgY29tbW9uSWRzID0gaW50ZXJzZWN0aW9uKGhhbmRsZXJJZHMsIGRpcnR5SWRzKTtcbiAgICByZXR1cm4gY29tbW9uSWRzLmxlbmd0aCA+IDA7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRpcnRpbmVzcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/dirtiness.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/equality.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/equality.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areArraysEqual: () => (/* binding */ areArraysEqual),\n/* harmony export */   areCoordsEqual: () => (/* binding */ areCoordsEqual),\n/* harmony export */   strictEquality: () => (/* binding */ strictEquality)\n/* harmony export */ });\nconst strictEquality = (a, b)=>a === b\n;\n/**\n * Determine if two cartesian coordinate offsets are equal\n * @param offsetA\n * @param offsetB\n */ function areCoordsEqual(offsetA, offsetB) {\n    if (!offsetA && !offsetB) {\n        return true;\n    } else if (!offsetA || !offsetB) {\n        return false;\n    } else {\n        return offsetA.x === offsetB.x && offsetA.y === offsetB.y;\n    }\n}\n/**\n * Determines if two arrays of items are equal\n * @param a The first array of items\n * @param b The second array of items\n */ function areArraysEqual(a, b, isEqual = strictEquality) {\n    if (a.length !== b.length) {\n        return false;\n    }\n    for(let i = 0; i < a.length; ++i){\n        if (!isEqual(a[i], b[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\n//# sourceMappingURL=equality.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9lcXVhbGl0eS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFXO0FBQ1g7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsY0FBYztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxkbmQtY29yZVxcZGlzdFxcdXRpbHNcXGVxdWFsaXR5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBzdHJpY3RFcXVhbGl0eSA9IChhLCBiKT0+YSA9PT0gYlxuO1xuLyoqXG4gKiBEZXRlcm1pbmUgaWYgdHdvIGNhcnRlc2lhbiBjb29yZGluYXRlIG9mZnNldHMgYXJlIGVxdWFsXG4gKiBAcGFyYW0gb2Zmc2V0QVxuICogQHBhcmFtIG9mZnNldEJcbiAqLyBleHBvcnQgZnVuY3Rpb24gYXJlQ29vcmRzRXF1YWwob2Zmc2V0QSwgb2Zmc2V0Qikge1xuICAgIGlmICghb2Zmc2V0QSAmJiAhb2Zmc2V0Qikge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGVsc2UgaWYgKCFvZmZzZXRBIHx8ICFvZmZzZXRCKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gb2Zmc2V0QS54ID09PSBvZmZzZXRCLnggJiYgb2Zmc2V0QS55ID09PSBvZmZzZXRCLnk7XG4gICAgfVxufVxuLyoqXG4gKiBEZXRlcm1pbmVzIGlmIHR3byBhcnJheXMgb2YgaXRlbXMgYXJlIGVxdWFsXG4gKiBAcGFyYW0gYSBUaGUgZmlyc3QgYXJyYXkgb2YgaXRlbXNcbiAqIEBwYXJhbSBiIFRoZSBzZWNvbmQgYXJyYXkgb2YgaXRlbXNcbiAqLyBleHBvcnQgZnVuY3Rpb24gYXJlQXJyYXlzRXF1YWwoYSwgYiwgaXNFcXVhbCA9IHN0cmljdEVxdWFsaXR5KSB7XG4gICAgaWYgKGEubGVuZ3RoICE9PSBiLmxlbmd0aCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGZvcihsZXQgaSA9IDA7IGkgPCBhLmxlbmd0aDsgKytpKXtcbiAgICAgICAgaWYgKCFpc0VxdWFsKGFbaV0sIGJbaV0pKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWVxdWFsaXR5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/equality.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/getNextUniqueId.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNextUniqueId: () => (/* binding */ getNextUniqueId)\n/* harmony export */ });\nlet nextUniqueId = 0;\nfunction getNextUniqueId() {\n    return nextUniqueId++;\n}\n\n//# sourceMappingURL=getNextUniqueId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9nZXROZXh0VW5pcXVlSWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUDtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXG5vZGVfbW9kdWxlc1xcZG5kLWNvcmVcXGRpc3RcXHV0aWxzXFxnZXROZXh0VW5pcXVlSWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IG5leHRVbmlxdWVJZCA9IDA7XG5leHBvcnQgZnVuY3Rpb24gZ2V0TmV4dFVuaXF1ZUlkKCkge1xuICAgIHJldHVybiBuZXh0VW5pcXVlSWQrKztcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0TmV4dFVuaXF1ZUlkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/getNextUniqueId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/js_utils.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   intersection: () => (/* binding */ intersection),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   without: () => (/* binding */ without),\n/* harmony export */   xor: () => (/* binding */ xor)\n/* harmony export */ });\n// cheap lodash replacements\n/**\n * drop-in replacement for _.get\n * @param obj\n * @param path\n * @param defaultValue\n */ function get(obj, path, defaultValue) {\n    return path.split('.').reduce((a, c)=>a && a[c] ? a[c] : defaultValue || null\n    , obj);\n}\n/**\n * drop-in replacement for _.without\n */ function without(items, item) {\n    return items.filter((i)=>i !== item\n    );\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */ function isString(input) {\n    return typeof input === 'string';\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */ function isObject(input) {\n    return typeof input === 'object';\n}\n/**\n * replacement for _.xor\n * @param itemsA\n * @param itemsB\n */ function xor(itemsA, itemsB) {\n    const map = new Map();\n    const insertItem = (item)=>{\n        map.set(item, map.has(item) ? map.get(item) + 1 : 1);\n    };\n    itemsA.forEach(insertItem);\n    itemsB.forEach(insertItem);\n    const result = [];\n    map.forEach((count, key)=>{\n        if (count === 1) {\n            result.push(key);\n        }\n    });\n    return result;\n}\n/**\n * replacement for _.intersection\n * @param itemsA\n * @param itemsB\n */ function intersection(itemsA, itemsB) {\n    return itemsA.filter((t)=>itemsB.indexOf(t) > -1\n    );\n}\n\n//# sourceMappingURL=js_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/js_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js":
/*!*********************************************************!*\
  !*** ./node_modules/dnd-core/dist/utils/matchesType.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   matchesType: () => (/* binding */ matchesType)\n/* harmony export */ });\nfunction matchesType(targetType, draggedItemType) {\n    if (draggedItemType === null) {\n        return targetType === null;\n    }\n    return Array.isArray(targetType) ? targetType.some((t)=>t === draggedItemType\n    ) : targetType === draggedItemType;\n}\n\n//# sourceMappingURL=matchesType.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC91dGlscy9tYXRjaGVzVHlwZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxkbmQtY29yZVxcZGlzdFxcdXRpbHNcXG1hdGNoZXNUeXBlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBtYXRjaGVzVHlwZSh0YXJnZXRUeXBlLCBkcmFnZ2VkSXRlbVR5cGUpIHtcbiAgICBpZiAoZHJhZ2dlZEl0ZW1UeXBlID09PSBudWxsKSB7XG4gICAgICAgIHJldHVybiB0YXJnZXRUeXBlID09PSBudWxsO1xuICAgIH1cbiAgICByZXR1cm4gQXJyYXkuaXNBcnJheSh0YXJnZXRUeXBlKSA/IHRhcmdldFR5cGUuc29tZSgodCk9PnQgPT09IGRyYWdnZWRJdGVtVHlwZVxuICAgICkgOiB0YXJnZXRUeXBlID09PSBkcmFnZ2VkSXRlbVR5cGU7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1hdGNoZXNUeXBlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/utils/matchesType.js\n");

/***/ })

};
;