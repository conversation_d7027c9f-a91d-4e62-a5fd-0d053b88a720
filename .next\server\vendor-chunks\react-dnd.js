"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-dnd";
exports.ids = ["vendor-chunks/react-dnd"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-dnd/dist/core/DndContext.js":
/*!********************************************************!*\
  !*** ./node_modules/react-dnd/dist/core/DndContext.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DndContext: () => (/* binding */ DndContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n/**\n * Create the React Context\n */ const DndContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    dragDropManager: undefined\n});\n\n//# sourceMappingURL=DndContext.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvY29yZS9EbmRDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDO0FBQ3RDO0FBQ0E7QUFDQSxJQUFXLG1CQUFtQixvREFBYTtBQUMzQztBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1kbmRcXGRpc3RcXGNvcmVcXERuZENvbnRleHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ29udGV4dCB9IGZyb20gJ3JlYWN0Jztcbi8qKlxuICogQ3JlYXRlIHRoZSBSZWFjdCBDb250ZXh0XG4gKi8gZXhwb3J0IGNvbnN0IERuZENvbnRleHQgPSBjcmVhdGVDb250ZXh0KHtcbiAgICBkcmFnRHJvcE1hbmFnZXI6IHVuZGVmaW5lZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPURuZENvbnRleHQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/core/DndContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/core/DndProvider.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-dnd/dist/core/DndProvider.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DndProvider: () => (/* binding */ DndProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var dnd_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dnd-core */ \"(ssr)/./node_modules/dnd-core/dist/createDragDropManager.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _DndContext_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DndContext.js */ \"(ssr)/./node_modules/react-dnd/dist/core/DndContext.js\");\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n\n\n\n\nlet refCount = 0;\nconst INSTANCE_SYM = Symbol.for('__REACT_DND_CONTEXT_INSTANCE__');\nvar DndProvider = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(function DndProvider(_param) {\n    var { children  } = _param, props = _objectWithoutProperties(_param, [\n        \"children\"\n    ]);\n    const [manager, isGlobalInstance] = getDndContextValue(props) // memoized from props\n    ;\n    /**\n\t\t * If the global context was used to store the DND context\n\t\t * then where theres no more references to it we should\n\t\t * clean it up to avoid memory leaks\n\t\t */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isGlobalInstance) {\n            const context = getGlobalContext();\n            ++refCount;\n            return ()=>{\n                if (--refCount === 0) {\n                    context[INSTANCE_SYM] = null;\n                }\n            };\n        }\n        return;\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_DndContext_js__WEBPACK_IMPORTED_MODULE_2__.DndContext.Provider, {\n        value: manager,\n        children: children\n    });\n});\n/**\n * A React component that provides the React-DnD context\n */ \nfunction getDndContextValue(props) {\n    if ('manager' in props) {\n        const manager = {\n            dragDropManager: props.manager\n        };\n        return [\n            manager,\n            false\n        ];\n    }\n    const manager = createSingletonDndContext(props.backend, props.context, props.options, props.debugMode);\n    const isGlobalInstance = !props.context;\n    return [\n        manager,\n        isGlobalInstance\n    ];\n}\nfunction createSingletonDndContext(backend, context = getGlobalContext(), options, debugMode) {\n    const ctx = context;\n    if (!ctx[INSTANCE_SYM]) {\n        ctx[INSTANCE_SYM] = {\n            dragDropManager: (0,dnd_core__WEBPACK_IMPORTED_MODULE_3__.createDragDropManager)(backend, context, options, debugMode)\n        };\n    }\n    return ctx[INSTANCE_SYM];\n}\nfunction getGlobalContext() {\n    return typeof global !== 'undefined' ? global : window;\n}\n\n//# sourceMappingURL=DndProvider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/core/DndProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useCollectedProps.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useCollectedProps.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCollectedProps: () => (/* binding */ useCollectedProps)\n/* harmony export */ });\n/* harmony import */ var _useMonitorOutput_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useMonitorOutput.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useMonitorOutput.js\");\n\nfunction useCollectedProps(collector, monitor, connector) {\n    return (0,_useMonitorOutput_js__WEBPACK_IMPORTED_MODULE_0__.useMonitorOutput)(monitor, collector || (()=>({})\n    ), ()=>connector.reconnect()\n    );\n}\n\n//# sourceMappingURL=useCollectedProps.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlQ29sbGVjdGVkUHJvcHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUQ7QUFDbEQ7QUFDUCxXQUFXLHNFQUFnQiwrQkFBK0I7QUFDMUQ7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXG5vZGVfbW9kdWxlc1xccmVhY3QtZG5kXFxkaXN0XFxob29rc1xcdXNlQ29sbGVjdGVkUHJvcHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTW9uaXRvck91dHB1dCB9IGZyb20gJy4vdXNlTW9uaXRvck91dHB1dC5qcyc7XG5leHBvcnQgZnVuY3Rpb24gdXNlQ29sbGVjdGVkUHJvcHMoY29sbGVjdG9yLCBtb25pdG9yLCBjb25uZWN0b3IpIHtcbiAgICByZXR1cm4gdXNlTW9uaXRvck91dHB1dChtb25pdG9yLCBjb2xsZWN0b3IgfHwgKCgpPT4oe30pXG4gICAgKSwgKCk9PmNvbm5lY3Rvci5yZWNvbm5lY3QoKVxuICAgICk7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVzZUNvbGxlY3RlZFByb3BzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useCollectedProps.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useCollector.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useCollector.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCollector: () => (/* binding */ useCollector)\n/* harmony export */ });\n/* harmony import */ var fast_deep_equal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fast-deep-equal */ \"(ssr)/./node_modules/fast-deep-equal/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useIsomorphicLayoutEffect.js\");\n\n\n\n/**\n *\n * @param monitor The monitor to collect state from\n * @param collect The collecting function\n * @param onUpdate A method to invoke when updates occur\n */ function useCollector(monitor, collect, onUpdate) {\n    const [collected, setCollected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>collect(monitor)\n    );\n    const updateCollected = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const nextValue = collect(monitor);\n        // This needs to be a deep-equality check because some monitor-collected values\n        // include XYCoord objects that may be equivalent, but do not have instance equality.\n        if (!fast_deep_equal__WEBPACK_IMPORTED_MODULE_0__(collected, nextValue)) {\n            setCollected(nextValue);\n            if (onUpdate) {\n                onUpdate();\n            }\n        }\n    }, [\n        collected,\n        monitor,\n        onUpdate\n    ]);\n    // update the collected properties after react renders.\n    // Note that the \"Dustbin Stress Test\" fails if this is not\n    // done when the component updates\n    (0,_useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(updateCollected);\n    return [\n        collected,\n        updateCollected\n    ];\n}\n\n//# sourceMappingURL=useCollector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useCollector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/DragSourceImpl.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrag/DragSourceImpl.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragSourceImpl: () => (/* binding */ DragSourceImpl)\n/* harmony export */ });\nclass DragSourceImpl {\n    beginDrag() {\n        const spec = this.spec;\n        const monitor = this.monitor;\n        let result = null;\n        if (typeof spec.item === 'object') {\n            result = spec.item;\n        } else if (typeof spec.item === 'function') {\n            result = spec.item(monitor);\n        } else {\n            result = {};\n        }\n        return result !== null && result !== void 0 ? result : null;\n    }\n    canDrag() {\n        const spec = this.spec;\n        const monitor = this.monitor;\n        if (typeof spec.canDrag === 'boolean') {\n            return spec.canDrag;\n        } else if (typeof spec.canDrag === 'function') {\n            return spec.canDrag(monitor);\n        } else {\n            return true;\n        }\n    }\n    isDragging(globalMonitor, target) {\n        const spec = this.spec;\n        const monitor = this.monitor;\n        const { isDragging  } = spec;\n        return isDragging ? isDragging(monitor) : target === globalMonitor.getSourceId();\n    }\n    endDrag() {\n        const spec = this.spec;\n        const monitor = this.monitor;\n        const connector = this.connector;\n        const { end  } = spec;\n        if (end) {\n            end(monitor.getItem(), monitor);\n        }\n        connector.reconnect();\n    }\n    constructor(spec, monitor, connector){\n        this.spec = spec;\n        this.monitor = monitor;\n        this.connector = connector;\n    }\n}\n\n//# sourceMappingURL=DragSourceImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/DragSourceImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/connectors.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrag/connectors.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConnectDragPreview: () => (/* binding */ useConnectDragPreview),\n/* harmony export */   useConnectDragSource: () => (/* binding */ useConnectDragSource)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction useConnectDragSource(connector) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>connector.hooks.dragSource()\n    , [\n        connector\n    ]);\n}\nfunction useConnectDragPreview(connector) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>connector.hooks.dragPreview()\n    , [\n        connector\n    ]);\n}\n\n//# sourceMappingURL=connectors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlRHJhZy9jb25uZWN0b3JzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnQztBQUN6QjtBQUNQLFdBQVcsOENBQU87QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFdBQVcsOENBQU87QUFDbEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1kbmRcXGRpc3RcXGhvb2tzXFx1c2VEcmFnXFxjb25uZWN0b3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5leHBvcnQgZnVuY3Rpb24gdXNlQ29ubmVjdERyYWdTb3VyY2UoY29ubmVjdG9yKSB7XG4gICAgcmV0dXJuIHVzZU1lbW8oKCk9PmNvbm5lY3Rvci5ob29rcy5kcmFnU291cmNlKClcbiAgICAsIFtcbiAgICAgICAgY29ubmVjdG9yXG4gICAgXSk7XG59XG5leHBvcnQgZnVuY3Rpb24gdXNlQ29ubmVjdERyYWdQcmV2aWV3KGNvbm5lY3Rvcikge1xuICAgIHJldHVybiB1c2VNZW1vKCgpPT5jb25uZWN0b3IuaG9va3MuZHJhZ1ByZXZpZXcoKVxuICAgICwgW1xuICAgICAgICBjb25uZWN0b3JcbiAgICBdKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29ubmVjdG9ycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/connectors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDrag.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrag/useDrag.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDrag: () => (/* binding */ useDrag)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var _useCollectedProps_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../useCollectedProps.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useCollectedProps.js\");\n/* harmony import */ var _useOptionalFactory_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useOptionalFactory.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useOptionalFactory.js\");\n/* harmony import */ var _connectors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./connectors.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/connectors.js\");\n/* harmony import */ var _useDragSourceConnector_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useDragSourceConnector.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragSourceConnector.js\");\n/* harmony import */ var _useDragSourceMonitor_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useDragSourceMonitor.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragSourceMonitor.js\");\n/* harmony import */ var _useRegisteredDragSource_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useRegisteredDragSource.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useRegisteredDragSource.js\");\n\n\n\n\n\n\n\n/**\n * useDragSource hook\n * @param sourceSpec The drag source specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */ function useDrag(specArg, deps) {\n    const spec = (0,_useOptionalFactory_js__WEBPACK_IMPORTED_MODULE_1__.useOptionalFactory)(specArg, deps);\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!spec.begin, `useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)`);\n    const monitor = (0,_useDragSourceMonitor_js__WEBPACK_IMPORTED_MODULE_2__.useDragSourceMonitor)();\n    const connector = (0,_useDragSourceConnector_js__WEBPACK_IMPORTED_MODULE_3__.useDragSourceConnector)(spec.options, spec.previewOptions);\n    (0,_useRegisteredDragSource_js__WEBPACK_IMPORTED_MODULE_4__.useRegisteredDragSource)(spec, monitor, connector);\n    return [\n        (0,_useCollectedProps_js__WEBPACK_IMPORTED_MODULE_5__.useCollectedProps)(spec.collect, monitor, connector),\n        (0,_connectors_js__WEBPACK_IMPORTED_MODULE_6__.useConnectDragSource)(connector),\n        (0,_connectors_js__WEBPACK_IMPORTED_MODULE_6__.useConnectDragPreview)(connector), \n    ];\n}\n\n//# sourceMappingURL=useDrag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragSource.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrag/useDragSource.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragSource: () => (/* binding */ useDragSource)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _DragSourceImpl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DragSourceImpl.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/DragSourceImpl.js\");\n\n\nfunction useDragSource(spec, monitor, connector) {\n    const handler = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new _DragSourceImpl_js__WEBPACK_IMPORTED_MODULE_1__.DragSourceImpl(spec, monitor, connector)\n    , [\n        monitor,\n        connector\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        handler.spec = spec;\n    }, [\n        spec\n    ]);\n    return handler;\n}\n\n//# sourceMappingURL=useDragSource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlRHJhZy91c2VEcmFnU291cmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyQztBQUNVO0FBQzlDO0FBQ1Asb0JBQW9CLDhDQUFPLFNBQVMsOERBQWM7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLGdEQUFTO0FBQ2I7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1kbmRcXGRpc3RcXGhvb2tzXFx1c2VEcmFnXFx1c2VEcmFnU291cmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IERyYWdTb3VyY2VJbXBsIH0gZnJvbSAnLi9EcmFnU291cmNlSW1wbC5qcyc7XG5leHBvcnQgZnVuY3Rpb24gdXNlRHJhZ1NvdXJjZShzcGVjLCBtb25pdG9yLCBjb25uZWN0b3IpIHtcbiAgICBjb25zdCBoYW5kbGVyID0gdXNlTWVtbygoKT0+bmV3IERyYWdTb3VyY2VJbXBsKHNwZWMsIG1vbml0b3IsIGNvbm5lY3RvcilcbiAgICAsIFtcbiAgICAgICAgbW9uaXRvcixcbiAgICAgICAgY29ubmVjdG9yXG4gICAgXSk7XG4gICAgdXNlRWZmZWN0KCgpPT57XG4gICAgICAgIGhhbmRsZXIuc3BlYyA9IHNwZWM7XG4gICAgfSwgW1xuICAgICAgICBzcGVjXG4gICAgXSk7XG4gICAgcmV0dXJuIGhhbmRsZXI7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVzZURyYWdTb3VyY2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragSourceConnector.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrag/useDragSourceConnector.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragSourceConnector: () => (/* binding */ useDragSourceConnector)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _internals_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internals/index.js */ \"(ssr)/./node_modules/react-dnd/dist/internals/SourceConnector.js\");\n/* harmony import */ var _useDragDropManager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useDragDropManager.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDragDropManager.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useIsomorphicLayoutEffect.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useIsomorphicLayoutEffect.js\");\n\n\n\n\nfunction useDragSourceConnector(dragSourceOptions, dragPreviewOptions) {\n    const manager = (0,_useDragDropManager_js__WEBPACK_IMPORTED_MODULE_1__.useDragDropManager)();\n    const connector = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new _internals_index_js__WEBPACK_IMPORTED_MODULE_2__.SourceConnector(manager.getBackend())\n    , [\n        manager\n    ]);\n    (0,_useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n        connector.dragSourceOptions = dragSourceOptions || null;\n        connector.reconnect();\n        return ()=>connector.disconnectDragSource()\n        ;\n    }, [\n        connector,\n        dragSourceOptions\n    ]);\n    (0,_useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n        connector.dragPreviewOptions = dragPreviewOptions || null;\n        connector.reconnect();\n        return ()=>connector.disconnectDragPreview()\n        ;\n    }, [\n        connector,\n        dragPreviewOptions\n    ]);\n    return connector;\n}\n\n//# sourceMappingURL=useDragSourceConnector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragSourceConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragSourceMonitor.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrag/useDragSourceMonitor.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragSourceMonitor: () => (/* binding */ useDragSourceMonitor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _internals_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internals/index.js */ \"(ssr)/./node_modules/react-dnd/dist/internals/DragSourceMonitorImpl.js\");\n/* harmony import */ var _useDragDropManager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useDragDropManager.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDragDropManager.js\");\n\n\n\nfunction useDragSourceMonitor() {\n    const manager = (0,_useDragDropManager_js__WEBPACK_IMPORTED_MODULE_1__.useDragDropManager)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new _internals_index_js__WEBPACK_IMPORTED_MODULE_2__.DragSourceMonitorImpl(manager)\n    , [\n        manager\n    ]);\n}\n\n//# sourceMappingURL=useDragSourceMonitor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlRHJhZy91c2VEcmFnU291cmNlTW9uaXRvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdDO0FBQ2lDO0FBQ0g7QUFDdkQ7QUFDUCxvQkFBb0IsMEVBQWtCO0FBQ3RDLFdBQVcsOENBQU8sU0FBUyxzRUFBcUI7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1kbmRcXGRpc3RcXGhvb2tzXFx1c2VEcmFnXFx1c2VEcmFnU291cmNlTW9uaXRvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgRHJhZ1NvdXJjZU1vbml0b3JJbXBsIH0gZnJvbSAnLi4vLi4vaW50ZXJuYWxzL2luZGV4LmpzJztcbmltcG9ydCB7IHVzZURyYWdEcm9wTWFuYWdlciB9IGZyb20gJy4uL3VzZURyYWdEcm9wTWFuYWdlci5qcyc7XG5leHBvcnQgZnVuY3Rpb24gdXNlRHJhZ1NvdXJjZU1vbml0b3IoKSB7XG4gICAgY29uc3QgbWFuYWdlciA9IHVzZURyYWdEcm9wTWFuYWdlcigpO1xuICAgIHJldHVybiB1c2VNZW1vKCgpPT5uZXcgRHJhZ1NvdXJjZU1vbml0b3JJbXBsKG1hbmFnZXIpXG4gICAgLCBbXG4gICAgICAgIG1hbmFnZXJcbiAgICBdKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXNlRHJhZ1NvdXJjZU1vbml0b3IuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragSourceMonitor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragType.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrag/useDragType.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragType: () => (/* binding */ useDragType)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\nfunction useDragType(spec) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const result = spec.type;\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(result != null, 'spec.type must be defined');\n        return result;\n    }, [\n        spec\n    ]);\n}\n\n//# sourceMappingURL=useDragType.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlRHJhZy91c2VEcmFnVHlwZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUQ7QUFDakI7QUFDekI7QUFDUCxXQUFXLDhDQUFPO0FBQ2xCO0FBQ0EsUUFBUSwrREFBUztBQUNqQjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1kbmRcXGRpc3RcXGhvb2tzXFx1c2VEcmFnXFx1c2VEcmFnVHlwZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpbnZhcmlhbnQgfSBmcm9tICdAcmVhY3QtZG5kL2ludmFyaWFudCc7XG5pbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuZXhwb3J0IGZ1bmN0aW9uIHVzZURyYWdUeXBlKHNwZWMpIHtcbiAgICByZXR1cm4gdXNlTWVtbygoKT0+e1xuICAgICAgICBjb25zdCByZXN1bHQgPSBzcGVjLnR5cGU7XG4gICAgICAgIGludmFyaWFudChyZXN1bHQgIT0gbnVsbCwgJ3NwZWMudHlwZSBtdXN0IGJlIGRlZmluZWQnKTtcbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9LCBbXG4gICAgICAgIHNwZWNcbiAgICBdKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXNlRHJhZ1R5cGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragType.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useRegisteredDragSource.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrag/useRegisteredDragSource.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRegisteredDragSource: () => (/* binding */ useRegisteredDragSource)\n/* harmony export */ });\n/* harmony import */ var _internals_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internals/index.js */ \"(ssr)/./node_modules/react-dnd/dist/internals/registration.js\");\n/* harmony import */ var _useDragDropManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useDragDropManager.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDragDropManager.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useIsomorphicLayoutEffect.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useDragSource_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useDragSource.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragSource.js\");\n/* harmony import */ var _useDragType_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useDragType.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useDragType.js\");\n\n\n\n\n\nfunction useRegisteredDragSource(spec, monitor, connector) {\n    const manager = (0,_useDragDropManager_js__WEBPACK_IMPORTED_MODULE_0__.useDragDropManager)();\n    const handler = (0,_useDragSource_js__WEBPACK_IMPORTED_MODULE_1__.useDragSource)(spec, monitor, connector);\n    const itemType = (0,_useDragType_js__WEBPACK_IMPORTED_MODULE_2__.useDragType)(spec);\n    (0,_useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(function registerDragSource() {\n        if (itemType != null) {\n            const [handlerId, unregister] = (0,_internals_index_js__WEBPACK_IMPORTED_MODULE_4__.registerSource)(itemType, handler, manager);\n            monitor.receiveHandlerId(handlerId);\n            connector.receiveHandlerId(handlerId);\n            return unregister;\n        }\n        return;\n    }, [\n        manager,\n        monitor,\n        connector,\n        handler,\n        itemType\n    ]);\n}\n\n//# sourceMappingURL=useRegisteredDragSource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrag/useRegisteredDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDragDropManager.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDragDropManager.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragDropManager: () => (/* binding */ useDragDropManager)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _core_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/index.js */ \"(ssr)/./node_modules/react-dnd/dist/core/DndContext.js\");\n\n\n\n/**\n * A hook to retrieve the DragDropManager from Context\n */ function useDragDropManager() {\n    const { dragDropManager  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_core_index_js__WEBPACK_IMPORTED_MODULE_2__.DndContext);\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(dragDropManager != null, 'Expected drag drop context');\n    return dragDropManager;\n}\n\n//# sourceMappingURL=useDragDropManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlRHJhZ0Ryb3BNYW5hZ2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUQ7QUFDZDtBQUNXO0FBQzlDO0FBQ0E7QUFDQSxJQUFXO0FBQ1gsWUFBWSxtQkFBbUIsRUFBRSxpREFBVSxDQUFDLHNEQUFVO0FBQ3RELElBQUksK0RBQVM7QUFDYjtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXG5vZGVfbW9kdWxlc1xccmVhY3QtZG5kXFxkaXN0XFxob29rc1xcdXNlRHJhZ0Ryb3BNYW5hZ2VyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludmFyaWFudCB9IGZyb20gJ0ByZWFjdC1kbmQvaW52YXJpYW50JztcbmltcG9ydCB7IHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBEbmRDb250ZXh0IH0gZnJvbSAnLi4vY29yZS9pbmRleC5qcyc7XG4vKipcbiAqIEEgaG9vayB0byByZXRyaWV2ZSB0aGUgRHJhZ0Ryb3BNYW5hZ2VyIGZyb20gQ29udGV4dFxuICovIGV4cG9ydCBmdW5jdGlvbiB1c2VEcmFnRHJvcE1hbmFnZXIoKSB7XG4gICAgY29uc3QgeyBkcmFnRHJvcE1hbmFnZXIgIH0gPSB1c2VDb250ZXh0KERuZENvbnRleHQpO1xuICAgIGludmFyaWFudChkcmFnRHJvcE1hbmFnZXIgIT0gbnVsbCwgJ0V4cGVjdGVkIGRyYWcgZHJvcCBjb250ZXh0Jyk7XG4gICAgcmV0dXJuIGRyYWdEcm9wTWFuYWdlcjtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXNlRHJhZ0Ryb3BNYW5hZ2VyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDragDropManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/DropTargetImpl.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrop/DropTargetImpl.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropTargetImpl: () => (/* binding */ DropTargetImpl)\n/* harmony export */ });\nclass DropTargetImpl {\n    canDrop() {\n        const spec = this.spec;\n        const monitor = this.monitor;\n        return spec.canDrop ? spec.canDrop(monitor.getItem(), monitor) : true;\n    }\n    hover() {\n        const spec = this.spec;\n        const monitor = this.monitor;\n        if (spec.hover) {\n            spec.hover(monitor.getItem(), monitor);\n        }\n    }\n    drop() {\n        const spec = this.spec;\n        const monitor = this.monitor;\n        if (spec.drop) {\n            return spec.drop(monitor.getItem(), monitor);\n        }\n        return;\n    }\n    constructor(spec, monitor){\n        this.spec = spec;\n        this.monitor = monitor;\n    }\n}\n\n//# sourceMappingURL=DropTargetImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlRHJvcC9Ecm9wVGFyZ2V0SW1wbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb3dubG9hZHNcXGZhY3RvcnlnYW1lXFxub2RlX21vZHVsZXNcXHJlYWN0LWRuZFxcZGlzdFxcaG9va3NcXHVzZURyb3BcXERyb3BUYXJnZXRJbXBsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBEcm9wVGFyZ2V0SW1wbCB7XG4gICAgY2FuRHJvcCgpIHtcbiAgICAgICAgY29uc3Qgc3BlYyA9IHRoaXMuc3BlYztcbiAgICAgICAgY29uc3QgbW9uaXRvciA9IHRoaXMubW9uaXRvcjtcbiAgICAgICAgcmV0dXJuIHNwZWMuY2FuRHJvcCA/IHNwZWMuY2FuRHJvcChtb25pdG9yLmdldEl0ZW0oKSwgbW9uaXRvcikgOiB0cnVlO1xuICAgIH1cbiAgICBob3ZlcigpIHtcbiAgICAgICAgY29uc3Qgc3BlYyA9IHRoaXMuc3BlYztcbiAgICAgICAgY29uc3QgbW9uaXRvciA9IHRoaXMubW9uaXRvcjtcbiAgICAgICAgaWYgKHNwZWMuaG92ZXIpIHtcbiAgICAgICAgICAgIHNwZWMuaG92ZXIobW9uaXRvci5nZXRJdGVtKCksIG1vbml0b3IpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGRyb3AoKSB7XG4gICAgICAgIGNvbnN0IHNwZWMgPSB0aGlzLnNwZWM7XG4gICAgICAgIGNvbnN0IG1vbml0b3IgPSB0aGlzLm1vbml0b3I7XG4gICAgICAgIGlmIChzcGVjLmRyb3ApIHtcbiAgICAgICAgICAgIHJldHVybiBzcGVjLmRyb3AobW9uaXRvci5nZXRJdGVtKCksIG1vbml0b3IpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybjtcbiAgICB9XG4gICAgY29uc3RydWN0b3Ioc3BlYywgbW9uaXRvcil7XG4gICAgICAgIHRoaXMuc3BlYyA9IHNwZWM7XG4gICAgICAgIHRoaXMubW9uaXRvciA9IG1vbml0b3I7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1Ecm9wVGFyZ2V0SW1wbC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/DropTargetImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/connectors.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrop/connectors.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConnectDropTarget: () => (/* binding */ useConnectDropTarget)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction useConnectDropTarget(connector) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>connector.hooks.dropTarget()\n    , [\n        connector\n    ]);\n}\n\n//# sourceMappingURL=connectors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlRHJvcC9jb25uZWN0b3JzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdDO0FBQ3pCO0FBQ1AsV0FBVyw4Q0FBTztBQUNsQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb3dubG9hZHNcXGZhY3RvcnlnYW1lXFxub2RlX21vZHVsZXNcXHJlYWN0LWRuZFxcZGlzdFxcaG9va3NcXHVzZURyb3BcXGNvbm5lY3RvcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmV4cG9ydCBmdW5jdGlvbiB1c2VDb25uZWN0RHJvcFRhcmdldChjb25uZWN0b3IpIHtcbiAgICByZXR1cm4gdXNlTWVtbygoKT0+Y29ubmVjdG9yLmhvb2tzLmRyb3BUYXJnZXQoKVxuICAgICwgW1xuICAgICAgICBjb25uZWN0b3JcbiAgICBdKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29ubmVjdG9ycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/connectors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useAccept.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrop/useAccept.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAccept: () => (/* binding */ useAccept)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n/**\n * Internal utility hook to get an array-version of spec.accept.\n * The main utility here is that we aren't creating a new array on every render if a non-array spec.accept is passed in.\n * @param spec\n */ function useAccept(spec) {\n    const { accept  } = spec;\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(spec.accept != null, 'accept must be defined');\n        return Array.isArray(accept) ? accept : [\n            accept\n        ];\n    }, [\n        accept\n    ]);\n}\n\n//# sourceMappingURL=useAccept.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlRHJvcC91c2VBY2NlcHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlEO0FBQ2pCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBVztBQUNYLFlBQVksVUFBVTtBQUN0QixXQUFXLDhDQUFPO0FBQ2xCLFFBQVEsK0RBQVM7QUFDakI7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb3dubG9hZHNcXGZhY3RvcnlnYW1lXFxub2RlX21vZHVsZXNcXHJlYWN0LWRuZFxcZGlzdFxcaG9va3NcXHVzZURyb3BcXHVzZUFjY2VwdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpbnZhcmlhbnQgfSBmcm9tICdAcmVhY3QtZG5kL2ludmFyaWFudCc7XG5pbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuLyoqXG4gKiBJbnRlcm5hbCB1dGlsaXR5IGhvb2sgdG8gZ2V0IGFuIGFycmF5LXZlcnNpb24gb2Ygc3BlYy5hY2NlcHQuXG4gKiBUaGUgbWFpbiB1dGlsaXR5IGhlcmUgaXMgdGhhdCB3ZSBhcmVuJ3QgY3JlYXRpbmcgYSBuZXcgYXJyYXkgb24gZXZlcnkgcmVuZGVyIGlmIGEgbm9uLWFycmF5IHNwZWMuYWNjZXB0IGlzIHBhc3NlZCBpbi5cbiAqIEBwYXJhbSBzcGVjXG4gKi8gZXhwb3J0IGZ1bmN0aW9uIHVzZUFjY2VwdChzcGVjKSB7XG4gICAgY29uc3QgeyBhY2NlcHQgIH0gPSBzcGVjO1xuICAgIHJldHVybiB1c2VNZW1vKCgpPT57XG4gICAgICAgIGludmFyaWFudChzcGVjLmFjY2VwdCAhPSBudWxsLCAnYWNjZXB0IG11c3QgYmUgZGVmaW5lZCcpO1xuICAgICAgICByZXR1cm4gQXJyYXkuaXNBcnJheShhY2NlcHQpID8gYWNjZXB0IDogW1xuICAgICAgICAgICAgYWNjZXB0XG4gICAgICAgIF07XG4gICAgfSwgW1xuICAgICAgICBhY2NlcHRcbiAgICBdKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXNlQWNjZXB0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useAccept.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDrop.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrop/useDrop.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDrop: () => (/* binding */ useDrop)\n/* harmony export */ });\n/* harmony import */ var _useCollectedProps_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../useCollectedProps.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useCollectedProps.js\");\n/* harmony import */ var _useOptionalFactory_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useOptionalFactory.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useOptionalFactory.js\");\n/* harmony import */ var _connectors_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./connectors.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/connectors.js\");\n/* harmony import */ var _useDropTargetConnector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useDropTargetConnector.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDropTargetConnector.js\");\n/* harmony import */ var _useDropTargetMonitor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useDropTargetMonitor.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDropTargetMonitor.js\");\n/* harmony import */ var _useRegisteredDropTarget_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useRegisteredDropTarget.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useRegisteredDropTarget.js\");\n\n\n\n\n\n\n/**\n * useDropTarget Hook\n * @param spec The drop target specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */ function useDrop(specArg, deps) {\n    const spec = (0,_useOptionalFactory_js__WEBPACK_IMPORTED_MODULE_0__.useOptionalFactory)(specArg, deps);\n    const monitor = (0,_useDropTargetMonitor_js__WEBPACK_IMPORTED_MODULE_1__.useDropTargetMonitor)();\n    const connector = (0,_useDropTargetConnector_js__WEBPACK_IMPORTED_MODULE_2__.useDropTargetConnector)(spec.options);\n    (0,_useRegisteredDropTarget_js__WEBPACK_IMPORTED_MODULE_3__.useRegisteredDropTarget)(spec, monitor, connector);\n    return [\n        (0,_useCollectedProps_js__WEBPACK_IMPORTED_MODULE_4__.useCollectedProps)(spec.collect, monitor, connector),\n        (0,_connectors_js__WEBPACK_IMPORTED_MODULE_5__.useConnectDropTarget)(connector), \n    ];\n}\n\n//# sourceMappingURL=useDrop.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDrop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDropTarget.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrop/useDropTarget.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDropTarget: () => (/* binding */ useDropTarget)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _DropTargetImpl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DropTargetImpl.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/DropTargetImpl.js\");\n\n\nfunction useDropTarget(spec, monitor) {\n    const dropTarget = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new _DropTargetImpl_js__WEBPACK_IMPORTED_MODULE_1__.DropTargetImpl(spec, monitor)\n    , [\n        monitor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        dropTarget.spec = spec;\n    }, [\n        spec\n    ]);\n    return dropTarget;\n}\n\n//# sourceMappingURL=useDropTarget.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlRHJvcC91c2VEcm9wVGFyZ2V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyQztBQUNVO0FBQzlDO0FBQ1AsdUJBQXVCLDhDQUFPLFNBQVMsOERBQWM7QUFDckQ7QUFDQTtBQUNBO0FBQ0EsSUFBSSxnREFBUztBQUNiO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXG5vZGVfbW9kdWxlc1xccmVhY3QtZG5kXFxkaXN0XFxob29rc1xcdXNlRHJvcFxcdXNlRHJvcFRhcmdldC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QsIHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBEcm9wVGFyZ2V0SW1wbCB9IGZyb20gJy4vRHJvcFRhcmdldEltcGwuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIHVzZURyb3BUYXJnZXQoc3BlYywgbW9uaXRvcikge1xuICAgIGNvbnN0IGRyb3BUYXJnZXQgPSB1c2VNZW1vKCgpPT5uZXcgRHJvcFRhcmdldEltcGwoc3BlYywgbW9uaXRvcilcbiAgICAsIFtcbiAgICAgICAgbW9uaXRvclxuICAgIF0pO1xuICAgIHVzZUVmZmVjdCgoKT0+e1xuICAgICAgICBkcm9wVGFyZ2V0LnNwZWMgPSBzcGVjO1xuICAgIH0sIFtcbiAgICAgICAgc3BlY1xuICAgIF0pO1xuICAgIHJldHVybiBkcm9wVGFyZ2V0O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD11c2VEcm9wVGFyZ2V0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDropTarget.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDropTargetConnector.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrop/useDropTargetConnector.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDropTargetConnector: () => (/* binding */ useDropTargetConnector)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _internals_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internals/index.js */ \"(ssr)/./node_modules/react-dnd/dist/internals/TargetConnector.js\");\n/* harmony import */ var _useDragDropManager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useDragDropManager.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDragDropManager.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useIsomorphicLayoutEffect.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useIsomorphicLayoutEffect.js\");\n\n\n\n\nfunction useDropTargetConnector(options) {\n    const manager = (0,_useDragDropManager_js__WEBPACK_IMPORTED_MODULE_1__.useDragDropManager)();\n    const connector = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new _internals_index_js__WEBPACK_IMPORTED_MODULE_2__.TargetConnector(manager.getBackend())\n    , [\n        manager\n    ]);\n    (0,_useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n        connector.dropTargetOptions = options || null;\n        connector.reconnect();\n        return ()=>connector.disconnectDropTarget()\n        ;\n    }, [\n        options\n    ]);\n    return connector;\n}\n\n//# sourceMappingURL=useDropTargetConnector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlRHJvcC91c2VEcm9wVGFyZ2V0Q29ubmVjdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWdDO0FBQzJCO0FBQ0c7QUFDYztBQUNyRTtBQUNQLG9CQUFvQiwwRUFBa0I7QUFDdEMsc0JBQXNCLDhDQUFPLFNBQVMsZ0VBQWU7QUFDckQ7QUFDQTtBQUNBO0FBQ0EsSUFBSSx3RkFBeUI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1kbmRcXGRpc3RcXGhvb2tzXFx1c2VEcm9wXFx1c2VEcm9wVGFyZ2V0Q29ubmVjdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBUYXJnZXRDb25uZWN0b3IgfSBmcm9tICcuLi8uLi9pbnRlcm5hbHMvaW5kZXguanMnO1xuaW1wb3J0IHsgdXNlRHJhZ0Ryb3BNYW5hZ2VyIH0gZnJvbSAnLi4vdXNlRHJhZ0Ryb3BNYW5hZ2VyLmpzJztcbmltcG9ydCB7IHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QgfSBmcm9tICcuLi91c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0LmpzJztcbmV4cG9ydCBmdW5jdGlvbiB1c2VEcm9wVGFyZ2V0Q29ubmVjdG9yKG9wdGlvbnMpIHtcbiAgICBjb25zdCBtYW5hZ2VyID0gdXNlRHJhZ0Ryb3BNYW5hZ2VyKCk7XG4gICAgY29uc3QgY29ubmVjdG9yID0gdXNlTWVtbygoKT0+bmV3IFRhcmdldENvbm5lY3RvcihtYW5hZ2VyLmdldEJhY2tlbmQoKSlcbiAgICAsIFtcbiAgICAgICAgbWFuYWdlclxuICAgIF0pO1xuICAgIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCk9PntcbiAgICAgICAgY29ubmVjdG9yLmRyb3BUYXJnZXRPcHRpb25zID0gb3B0aW9ucyB8fCBudWxsO1xuICAgICAgICBjb25uZWN0b3IucmVjb25uZWN0KCk7XG4gICAgICAgIHJldHVybiAoKT0+Y29ubmVjdG9yLmRpc2Nvbm5lY3REcm9wVGFyZ2V0KClcbiAgICAgICAgO1xuICAgIH0sIFtcbiAgICAgICAgb3B0aW9uc1xuICAgIF0pO1xuICAgIHJldHVybiBjb25uZWN0b3I7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVzZURyb3BUYXJnZXRDb25uZWN0b3IuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDropTargetConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDropTargetMonitor.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrop/useDropTargetMonitor.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDropTargetMonitor: () => (/* binding */ useDropTargetMonitor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _internals_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internals/index.js */ \"(ssr)/./node_modules/react-dnd/dist/internals/DropTargetMonitorImpl.js\");\n/* harmony import */ var _useDragDropManager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useDragDropManager.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDragDropManager.js\");\n\n\n\nfunction useDropTargetMonitor() {\n    const manager = (0,_useDragDropManager_js__WEBPACK_IMPORTED_MODULE_1__.useDragDropManager)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new _internals_index_js__WEBPACK_IMPORTED_MODULE_2__.DropTargetMonitorImpl(manager)\n    , [\n        manager\n    ]);\n}\n\n//# sourceMappingURL=useDropTargetMonitor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlRHJvcC91c2VEcm9wVGFyZ2V0TW9uaXRvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdDO0FBQ2lDO0FBQ0g7QUFDdkQ7QUFDUCxvQkFBb0IsMEVBQWtCO0FBQ3RDLFdBQVcsOENBQU8sU0FBUyxzRUFBcUI7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG93bmxvYWRzXFxmYWN0b3J5Z2FtZVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1kbmRcXGRpc3RcXGhvb2tzXFx1c2VEcm9wXFx1c2VEcm9wVGFyZ2V0TW9uaXRvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgRHJvcFRhcmdldE1vbml0b3JJbXBsIH0gZnJvbSAnLi4vLi4vaW50ZXJuYWxzL2luZGV4LmpzJztcbmltcG9ydCB7IHVzZURyYWdEcm9wTWFuYWdlciB9IGZyb20gJy4uL3VzZURyYWdEcm9wTWFuYWdlci5qcyc7XG5leHBvcnQgZnVuY3Rpb24gdXNlRHJvcFRhcmdldE1vbml0b3IoKSB7XG4gICAgY29uc3QgbWFuYWdlciA9IHVzZURyYWdEcm9wTWFuYWdlcigpO1xuICAgIHJldHVybiB1c2VNZW1vKCgpPT5uZXcgRHJvcFRhcmdldE1vbml0b3JJbXBsKG1hbmFnZXIpXG4gICAgLCBbXG4gICAgICAgIG1hbmFnZXJcbiAgICBdKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXNlRHJvcFRhcmdldE1vbml0b3IuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDropTargetMonitor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useRegisteredDropTarget.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useDrop/useRegisteredDropTarget.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRegisteredDropTarget: () => (/* binding */ useRegisteredDropTarget)\n/* harmony export */ });\n/* harmony import */ var _internals_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internals/index.js */ \"(ssr)/./node_modules/react-dnd/dist/internals/registration.js\");\n/* harmony import */ var _useDragDropManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useDragDropManager.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDragDropManager.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useIsomorphicLayoutEffect.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useAccept_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useAccept.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useAccept.js\");\n/* harmony import */ var _useDropTarget_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useDropTarget.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useDropTarget.js\");\n\n\n\n\n\nfunction useRegisteredDropTarget(spec, monitor, connector) {\n    const manager = (0,_useDragDropManager_js__WEBPACK_IMPORTED_MODULE_0__.useDragDropManager)();\n    const dropTarget = (0,_useDropTarget_js__WEBPACK_IMPORTED_MODULE_1__.useDropTarget)(spec, monitor);\n    const accept = (0,_useAccept_js__WEBPACK_IMPORTED_MODULE_2__.useAccept)(spec);\n    (0,_useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(function registerDropTarget() {\n        const [handlerId, unregister] = (0,_internals_index_js__WEBPACK_IMPORTED_MODULE_4__.registerTarget)(accept, dropTarget, manager);\n        monitor.receiveHandlerId(handlerId);\n        connector.receiveHandlerId(handlerId);\n        return unregister;\n    }, [\n        manager,\n        monitor,\n        dropTarget,\n        connector,\n        accept.map((a)=>a.toString()\n        ).join('|'), \n    ]);\n}\n\n//# sourceMappingURL=useRegisteredDropTarget.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useDrop/useRegisteredDropTarget.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useIsomorphicLayoutEffect.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useIsomorphicLayoutEffect.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n// suppress the useLayoutEffect warning on server side.\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n//# sourceMappingURL=useIsomorphicLayoutEffect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRDtBQUNuRDtBQUNPLGtFQUFrRSxrREFBZSxHQUFHLDRDQUFTOztBQUVwRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb3dubG9hZHNcXGZhY3RvcnlnYW1lXFxub2RlX21vZHVsZXNcXHJlYWN0LWRuZFxcZGlzdFxcaG9va3NcXHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG4vLyBzdXBwcmVzcyB0aGUgdXNlTGF5b3V0RWZmZWN0IHdhcm5pbmcgb24gc2VydmVyIHNpZGUuXG5leHBvcnQgY29uc3QgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gdXNlTGF5b3V0RWZmZWN0IDogdXNlRWZmZWN0O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD11c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useIsomorphicLayoutEffect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useMonitorOutput.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useMonitorOutput.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMonitorOutput: () => (/* binding */ useMonitorOutput)\n/* harmony export */ });\n/* harmony import */ var _useCollector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useCollector.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useCollector.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect.js */ \"(ssr)/./node_modules/react-dnd/dist/hooks/useIsomorphicLayoutEffect.js\");\n\n\nfunction useMonitorOutput(monitor, collect, onCollect) {\n    const [collected, updateCollected] = (0,_useCollector_js__WEBPACK_IMPORTED_MODULE_0__.useCollector)(monitor, collect, onCollect);\n    (0,_useIsomorphicLayoutEffect_js__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(function subscribeToMonitorStateChange() {\n        const handlerId = monitor.getHandlerId();\n        if (handlerId == null) {\n            return;\n        }\n        return monitor.subscribeToStateChange(updateCollected, {\n            handlerIds: [\n                handlerId\n            ]\n        });\n    }, [\n        monitor,\n        updateCollected\n    ]);\n    return collected;\n}\n\n//# sourceMappingURL=useMonitorOutput.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlTW9uaXRvck91dHB1dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUQ7QUFDMEI7QUFDcEU7QUFDUCx5Q0FBeUMsOERBQVk7QUFDckQsSUFBSSx3RkFBeUI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb3dubG9hZHNcXGZhY3RvcnlnYW1lXFxub2RlX21vZHVsZXNcXHJlYWN0LWRuZFxcZGlzdFxcaG9va3NcXHVzZU1vbml0b3JPdXRwdXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ29sbGVjdG9yIH0gZnJvbSAnLi91c2VDb2xsZWN0b3IuanMnO1xuaW1wb3J0IHsgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCB9IGZyb20gJy4vdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdC5qcyc7XG5leHBvcnQgZnVuY3Rpb24gdXNlTW9uaXRvck91dHB1dChtb25pdG9yLCBjb2xsZWN0LCBvbkNvbGxlY3QpIHtcbiAgICBjb25zdCBbY29sbGVjdGVkLCB1cGRhdGVDb2xsZWN0ZWRdID0gdXNlQ29sbGVjdG9yKG1vbml0b3IsIGNvbGxlY3QsIG9uQ29sbGVjdCk7XG4gICAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdChmdW5jdGlvbiBzdWJzY3JpYmVUb01vbml0b3JTdGF0ZUNoYW5nZSgpIHtcbiAgICAgICAgY29uc3QgaGFuZGxlcklkID0gbW9uaXRvci5nZXRIYW5kbGVySWQoKTtcbiAgICAgICAgaWYgKGhhbmRsZXJJZCA9PSBudWxsKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG1vbml0b3Iuc3Vic2NyaWJlVG9TdGF0ZUNoYW5nZSh1cGRhdGVDb2xsZWN0ZWQsIHtcbiAgICAgICAgICAgIGhhbmRsZXJJZHM6IFtcbiAgICAgICAgICAgICAgICBoYW5kbGVySWRcbiAgICAgICAgICAgIF1cbiAgICAgICAgfSk7XG4gICAgfSwgW1xuICAgICAgICBtb25pdG9yLFxuICAgICAgICB1cGRhdGVDb2xsZWN0ZWRcbiAgICBdKTtcbiAgICByZXR1cm4gY29sbGVjdGVkO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD11c2VNb25pdG9yT3V0cHV0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useMonitorOutput.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/hooks/useOptionalFactory.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-dnd/dist/hooks/useOptionalFactory.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOptionalFactory: () => (/* binding */ useOptionalFactory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction useOptionalFactory(arg, deps) {\n    const memoDeps = [\n        ...deps || []\n    ];\n    if (deps == null && typeof arg !== 'function') {\n        memoDeps.push(arg);\n    }\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        return typeof arg === 'function' ? arg() : arg;\n    }, memoDeps);\n}\n\n//# sourceMappingURL=useOptionalFactory.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaG9va3MvdXNlT3B0aW9uYWxGYWN0b3J5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdDO0FBQ3pCO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyw4Q0FBTztBQUNsQjtBQUNBLEtBQUs7QUFDTDs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb3dubG9hZHNcXGZhY3RvcnlnYW1lXFxub2RlX21vZHVsZXNcXHJlYWN0LWRuZFxcZGlzdFxcaG9va3NcXHVzZU9wdGlvbmFsRmFjdG9yeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuZXhwb3J0IGZ1bmN0aW9uIHVzZU9wdGlvbmFsRmFjdG9yeShhcmcsIGRlcHMpIHtcbiAgICBjb25zdCBtZW1vRGVwcyA9IFtcbiAgICAgICAgLi4uZGVwcyB8fCBbXVxuICAgIF07XG4gICAgaWYgKGRlcHMgPT0gbnVsbCAmJiB0eXBlb2YgYXJnICE9PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIG1lbW9EZXBzLnB1c2goYXJnKTtcbiAgICB9XG4gICAgcmV0dXJuIHVzZU1lbW8oKCk9PntcbiAgICAgICAgcmV0dXJuIHR5cGVvZiBhcmcgPT09ICdmdW5jdGlvbicgPyBhcmcoKSA6IGFyZztcbiAgICB9LCBtZW1vRGVwcyk7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVzZU9wdGlvbmFsRmFjdG9yeS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/hooks/useOptionalFactory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/internals/DragSourceMonitorImpl.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/internals/DragSourceMonitorImpl.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragSourceMonitorImpl: () => (/* binding */ DragSourceMonitorImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n\nlet isCallingCanDrag = false;\nlet isCallingIsDragging = false;\nclass DragSourceMonitorImpl {\n    receiveHandlerId(sourceId) {\n        this.sourceId = sourceId;\n    }\n    getHandlerId() {\n        return this.sourceId;\n    }\n    canDrag() {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!isCallingCanDrag, 'You may not call monitor.canDrag() inside your canDrag() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n        try {\n            isCallingCanDrag = true;\n            return this.internalMonitor.canDragSource(this.sourceId);\n        } finally{\n            isCallingCanDrag = false;\n        }\n    }\n    isDragging() {\n        if (!this.sourceId) {\n            return false;\n        }\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!isCallingIsDragging, 'You may not call monitor.isDragging() inside your isDragging() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n        try {\n            isCallingIsDragging = true;\n            return this.internalMonitor.isDraggingSource(this.sourceId);\n        } finally{\n            isCallingIsDragging = false;\n        }\n    }\n    subscribeToStateChange(listener, options) {\n        return this.internalMonitor.subscribeToStateChange(listener, options);\n    }\n    isDraggingSource(sourceId) {\n        return this.internalMonitor.isDraggingSource(sourceId);\n    }\n    isOverTarget(targetId, options) {\n        return this.internalMonitor.isOverTarget(targetId, options);\n    }\n    getTargetIds() {\n        return this.internalMonitor.getTargetIds();\n    }\n    isSourcePublic() {\n        return this.internalMonitor.isSourcePublic();\n    }\n    getSourceId() {\n        return this.internalMonitor.getSourceId();\n    }\n    subscribeToOffsetChange(listener) {\n        return this.internalMonitor.subscribeToOffsetChange(listener);\n    }\n    canDragSource(sourceId) {\n        return this.internalMonitor.canDragSource(sourceId);\n    }\n    canDropOnTarget(targetId) {\n        return this.internalMonitor.canDropOnTarget(targetId);\n    }\n    getItemType() {\n        return this.internalMonitor.getItemType();\n    }\n    getItem() {\n        return this.internalMonitor.getItem();\n    }\n    getDropResult() {\n        return this.internalMonitor.getDropResult();\n    }\n    didDrop() {\n        return this.internalMonitor.didDrop();\n    }\n    getInitialClientOffset() {\n        return this.internalMonitor.getInitialClientOffset();\n    }\n    getInitialSourceClientOffset() {\n        return this.internalMonitor.getInitialSourceClientOffset();\n    }\n    getSourceClientOffset() {\n        return this.internalMonitor.getSourceClientOffset();\n    }\n    getClientOffset() {\n        return this.internalMonitor.getClientOffset();\n    }\n    getDifferenceFromInitialOffset() {\n        return this.internalMonitor.getDifferenceFromInitialOffset();\n    }\n    constructor(manager){\n        this.sourceId = null;\n        this.internalMonitor = manager.getMonitor();\n    }\n}\n\n//# sourceMappingURL=DragSourceMonitorImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/internals/DragSourceMonitorImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/internals/DropTargetMonitorImpl.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/internals/DropTargetMonitorImpl.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropTargetMonitorImpl: () => (/* binding */ DropTargetMonitorImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n\nlet isCallingCanDrop = false;\nclass DropTargetMonitorImpl {\n    receiveHandlerId(targetId) {\n        this.targetId = targetId;\n    }\n    getHandlerId() {\n        return this.targetId;\n    }\n    subscribeToStateChange(listener, options) {\n        return this.internalMonitor.subscribeToStateChange(listener, options);\n    }\n    canDrop() {\n        // Cut out early if the target id has not been set. This should prevent errors\n        // where the user has an older version of dnd-core like in\n        // https://github.com/react-dnd/react-dnd/issues/1310\n        if (!this.targetId) {\n            return false;\n        }\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!isCallingCanDrop, 'You may not call monitor.canDrop() inside your canDrop() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor');\n        try {\n            isCallingCanDrop = true;\n            return this.internalMonitor.canDropOnTarget(this.targetId);\n        } finally{\n            isCallingCanDrop = false;\n        }\n    }\n    isOver(options) {\n        if (!this.targetId) {\n            return false;\n        }\n        return this.internalMonitor.isOverTarget(this.targetId, options);\n    }\n    getItemType() {\n        return this.internalMonitor.getItemType();\n    }\n    getItem() {\n        return this.internalMonitor.getItem();\n    }\n    getDropResult() {\n        return this.internalMonitor.getDropResult();\n    }\n    didDrop() {\n        return this.internalMonitor.didDrop();\n    }\n    getInitialClientOffset() {\n        return this.internalMonitor.getInitialClientOffset();\n    }\n    getInitialSourceClientOffset() {\n        return this.internalMonitor.getInitialSourceClientOffset();\n    }\n    getSourceClientOffset() {\n        return this.internalMonitor.getSourceClientOffset();\n    }\n    getClientOffset() {\n        return this.internalMonitor.getClientOffset();\n    }\n    getDifferenceFromInitialOffset() {\n        return this.internalMonitor.getDifferenceFromInitialOffset();\n    }\n    constructor(manager){\n        this.targetId = null;\n        this.internalMonitor = manager.getMonitor();\n    }\n}\n\n//# sourceMappingURL=DropTargetMonitorImpl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/internals/DropTargetMonitorImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/internals/SourceConnector.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-dnd/dist/internals/SourceConnector.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SourceConnector: () => (/* binding */ SourceConnector)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_shallowequal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/shallowequal */ \"(ssr)/./node_modules/@react-dnd/shallowequal/dist/index.js\");\n/* harmony import */ var _isRef_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isRef.js */ \"(ssr)/./node_modules/react-dnd/dist/internals/isRef.js\");\n/* harmony import */ var _wrapConnectorHooks_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./wrapConnectorHooks.js */ \"(ssr)/./node_modules/react-dnd/dist/internals/wrapConnectorHooks.js\");\n\n\n\nclass SourceConnector {\n    receiveHandlerId(newHandlerId) {\n        if (this.handlerId === newHandlerId) {\n            return;\n        }\n        this.handlerId = newHandlerId;\n        this.reconnect();\n    }\n    get connectTarget() {\n        return this.dragSource;\n    }\n    get dragSourceOptions() {\n        return this.dragSourceOptionsInternal;\n    }\n    set dragSourceOptions(options) {\n        this.dragSourceOptionsInternal = options;\n    }\n    get dragPreviewOptions() {\n        return this.dragPreviewOptionsInternal;\n    }\n    set dragPreviewOptions(options) {\n        this.dragPreviewOptionsInternal = options;\n    }\n    reconnect() {\n        const didChange = this.reconnectDragSource();\n        this.reconnectDragPreview(didChange);\n    }\n    reconnectDragSource() {\n        const dragSource = this.dragSource;\n        // if nothing has changed then don't resubscribe\n        const didChange = this.didHandlerIdChange() || this.didConnectedDragSourceChange() || this.didDragSourceOptionsChange();\n        if (didChange) {\n            this.disconnectDragSource();\n        }\n        if (!this.handlerId) {\n            return didChange;\n        }\n        if (!dragSource) {\n            this.lastConnectedDragSource = dragSource;\n            return didChange;\n        }\n        if (didChange) {\n            this.lastConnectedHandlerId = this.handlerId;\n            this.lastConnectedDragSource = dragSource;\n            this.lastConnectedDragSourceOptions = this.dragSourceOptions;\n            this.dragSourceUnsubscribe = this.backend.connectDragSource(this.handlerId, dragSource, this.dragSourceOptions);\n        }\n        return didChange;\n    }\n    reconnectDragPreview(forceDidChange = false) {\n        const dragPreview = this.dragPreview;\n        // if nothing has changed then don't resubscribe\n        const didChange = forceDidChange || this.didHandlerIdChange() || this.didConnectedDragPreviewChange() || this.didDragPreviewOptionsChange();\n        if (didChange) {\n            this.disconnectDragPreview();\n        }\n        if (!this.handlerId) {\n            return;\n        }\n        if (!dragPreview) {\n            this.lastConnectedDragPreview = dragPreview;\n            return;\n        }\n        if (didChange) {\n            this.lastConnectedHandlerId = this.handlerId;\n            this.lastConnectedDragPreview = dragPreview;\n            this.lastConnectedDragPreviewOptions = this.dragPreviewOptions;\n            this.dragPreviewUnsubscribe = this.backend.connectDragPreview(this.handlerId, dragPreview, this.dragPreviewOptions);\n        }\n    }\n    didHandlerIdChange() {\n        return this.lastConnectedHandlerId !== this.handlerId;\n    }\n    didConnectedDragSourceChange() {\n        return this.lastConnectedDragSource !== this.dragSource;\n    }\n    didConnectedDragPreviewChange() {\n        return this.lastConnectedDragPreview !== this.dragPreview;\n    }\n    didDragSourceOptionsChange() {\n        return !(0,_react_dnd_shallowequal__WEBPACK_IMPORTED_MODULE_0__.shallowEqual)(this.lastConnectedDragSourceOptions, this.dragSourceOptions);\n    }\n    didDragPreviewOptionsChange() {\n        return !(0,_react_dnd_shallowequal__WEBPACK_IMPORTED_MODULE_0__.shallowEqual)(this.lastConnectedDragPreviewOptions, this.dragPreviewOptions);\n    }\n    disconnectDragSource() {\n        if (this.dragSourceUnsubscribe) {\n            this.dragSourceUnsubscribe();\n            this.dragSourceUnsubscribe = undefined;\n        }\n    }\n    disconnectDragPreview() {\n        if (this.dragPreviewUnsubscribe) {\n            this.dragPreviewUnsubscribe();\n            this.dragPreviewUnsubscribe = undefined;\n            this.dragPreviewNode = null;\n            this.dragPreviewRef = null;\n        }\n    }\n    get dragSource() {\n        return this.dragSourceNode || this.dragSourceRef && this.dragSourceRef.current;\n    }\n    get dragPreview() {\n        return this.dragPreviewNode || this.dragPreviewRef && this.dragPreviewRef.current;\n    }\n    clearDragSource() {\n        this.dragSourceNode = null;\n        this.dragSourceRef = null;\n    }\n    clearDragPreview() {\n        this.dragPreviewNode = null;\n        this.dragPreviewRef = null;\n    }\n    constructor(backend){\n        this.hooks = (0,_wrapConnectorHooks_js__WEBPACK_IMPORTED_MODULE_1__.wrapConnectorHooks)({\n            dragSource: (node, options)=>{\n                this.clearDragSource();\n                this.dragSourceOptions = options || null;\n                if ((0,_isRef_js__WEBPACK_IMPORTED_MODULE_2__.isRef)(node)) {\n                    this.dragSourceRef = node;\n                } else {\n                    this.dragSourceNode = node;\n                }\n                this.reconnectDragSource();\n            },\n            dragPreview: (node, options)=>{\n                this.clearDragPreview();\n                this.dragPreviewOptions = options || null;\n                if ((0,_isRef_js__WEBPACK_IMPORTED_MODULE_2__.isRef)(node)) {\n                    this.dragPreviewRef = node;\n                } else {\n                    this.dragPreviewNode = node;\n                }\n                this.reconnectDragPreview();\n            }\n        });\n        this.handlerId = null;\n        // The drop target may either be attached via ref or connect function\n        this.dragSourceRef = null;\n        this.dragSourceOptionsInternal = null;\n        // The drag preview may either be attached via ref or connect function\n        this.dragPreviewRef = null;\n        this.dragPreviewOptionsInternal = null;\n        this.lastConnectedHandlerId = null;\n        this.lastConnectedDragSource = null;\n        this.lastConnectedDragSourceOptions = null;\n        this.lastConnectedDragPreview = null;\n        this.lastConnectedDragPreviewOptions = null;\n        this.backend = backend;\n    }\n}\n\n//# sourceMappingURL=SourceConnector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/internals/SourceConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/internals/TargetConnector.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-dnd/dist/internals/TargetConnector.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TargetConnector: () => (/* binding */ TargetConnector)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_shallowequal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/shallowequal */ \"(ssr)/./node_modules/@react-dnd/shallowequal/dist/index.js\");\n/* harmony import */ var _isRef_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isRef.js */ \"(ssr)/./node_modules/react-dnd/dist/internals/isRef.js\");\n/* harmony import */ var _wrapConnectorHooks_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./wrapConnectorHooks.js */ \"(ssr)/./node_modules/react-dnd/dist/internals/wrapConnectorHooks.js\");\n\n\n\nclass TargetConnector {\n    get connectTarget() {\n        return this.dropTarget;\n    }\n    reconnect() {\n        // if nothing has changed then don't resubscribe\n        const didChange = this.didHandlerIdChange() || this.didDropTargetChange() || this.didOptionsChange();\n        if (didChange) {\n            this.disconnectDropTarget();\n        }\n        const dropTarget = this.dropTarget;\n        if (!this.handlerId) {\n            return;\n        }\n        if (!dropTarget) {\n            this.lastConnectedDropTarget = dropTarget;\n            return;\n        }\n        if (didChange) {\n            this.lastConnectedHandlerId = this.handlerId;\n            this.lastConnectedDropTarget = dropTarget;\n            this.lastConnectedDropTargetOptions = this.dropTargetOptions;\n            this.unsubscribeDropTarget = this.backend.connectDropTarget(this.handlerId, dropTarget, this.dropTargetOptions);\n        }\n    }\n    receiveHandlerId(newHandlerId) {\n        if (newHandlerId === this.handlerId) {\n            return;\n        }\n        this.handlerId = newHandlerId;\n        this.reconnect();\n    }\n    get dropTargetOptions() {\n        return this.dropTargetOptionsInternal;\n    }\n    set dropTargetOptions(options) {\n        this.dropTargetOptionsInternal = options;\n    }\n    didHandlerIdChange() {\n        return this.lastConnectedHandlerId !== this.handlerId;\n    }\n    didDropTargetChange() {\n        return this.lastConnectedDropTarget !== this.dropTarget;\n    }\n    didOptionsChange() {\n        return !(0,_react_dnd_shallowequal__WEBPACK_IMPORTED_MODULE_0__.shallowEqual)(this.lastConnectedDropTargetOptions, this.dropTargetOptions);\n    }\n    disconnectDropTarget() {\n        if (this.unsubscribeDropTarget) {\n            this.unsubscribeDropTarget();\n            this.unsubscribeDropTarget = undefined;\n        }\n    }\n    get dropTarget() {\n        return this.dropTargetNode || this.dropTargetRef && this.dropTargetRef.current;\n    }\n    clearDropTarget() {\n        this.dropTargetRef = null;\n        this.dropTargetNode = null;\n    }\n    constructor(backend){\n        this.hooks = (0,_wrapConnectorHooks_js__WEBPACK_IMPORTED_MODULE_1__.wrapConnectorHooks)({\n            dropTarget: (node, options)=>{\n                this.clearDropTarget();\n                this.dropTargetOptions = options;\n                if ((0,_isRef_js__WEBPACK_IMPORTED_MODULE_2__.isRef)(node)) {\n                    this.dropTargetRef = node;\n                } else {\n                    this.dropTargetNode = node;\n                }\n                this.reconnect();\n            }\n        });\n        this.handlerId = null;\n        // The drop target may either be attached via ref or connect function\n        this.dropTargetRef = null;\n        this.dropTargetOptionsInternal = null;\n        this.lastConnectedHandlerId = null;\n        this.lastConnectedDropTarget = null;\n        this.lastConnectedDropTargetOptions = null;\n        this.backend = backend;\n    }\n}\n\n//# sourceMappingURL=TargetConnector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/internals/TargetConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/internals/isRef.js":
/*!********************************************************!*\
  !*** ./node_modules/react-dnd/dist/internals/isRef.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isRef: () => (/* binding */ isRef)\n/* harmony export */ });\nfunction isRef(obj) {\n    return(// eslint-disable-next-line no-prototype-builtins\n    obj !== null && typeof obj === 'object' && Object.prototype.hasOwnProperty.call(obj, 'current'));\n}\n\n//# sourceMappingURL=isRef.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaW50ZXJuYWxzL2lzUmVmLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb3dubG9hZHNcXGZhY3RvcnlnYW1lXFxub2RlX21vZHVsZXNcXHJlYWN0LWRuZFxcZGlzdFxcaW50ZXJuYWxzXFxpc1JlZi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaXNSZWYob2JqKSB7XG4gICAgcmV0dXJuKC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1wcm90b3R5cGUtYnVpbHRpbnNcbiAgICBvYmogIT09IG51bGwgJiYgdHlwZW9mIG9iaiA9PT0gJ29iamVjdCcgJiYgT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKG9iaiwgJ2N1cnJlbnQnKSk7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWlzUmVmLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/internals/isRef.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/internals/registration.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-dnd/dist/internals/registration.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerSource: () => (/* binding */ registerSource),\n/* harmony export */   registerTarget: () => (/* binding */ registerTarget)\n/* harmony export */ });\nfunction registerTarget(type, target, manager) {\n    const registry = manager.getRegistry();\n    const targetId = registry.addTarget(type, target);\n    return [\n        targetId,\n        ()=>registry.removeTarget(targetId)\n    ];\n}\nfunction registerSource(type, source, manager) {\n    const registry = manager.getRegistry();\n    const sourceId = registry.addSource(type, source);\n    return [\n        sourceId,\n        ()=>registry.removeSource(sourceId)\n    ];\n}\n\n//# sourceMappingURL=registration.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvaW50ZXJuYWxzL3JlZ2lzdHJhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvd25sb2Fkc1xcZmFjdG9yeWdhbWVcXG5vZGVfbW9kdWxlc1xccmVhY3QtZG5kXFxkaXN0XFxpbnRlcm5hbHNcXHJlZ2lzdHJhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gcmVnaXN0ZXJUYXJnZXQodHlwZSwgdGFyZ2V0LCBtYW5hZ2VyKSB7XG4gICAgY29uc3QgcmVnaXN0cnkgPSBtYW5hZ2VyLmdldFJlZ2lzdHJ5KCk7XG4gICAgY29uc3QgdGFyZ2V0SWQgPSByZWdpc3RyeS5hZGRUYXJnZXQodHlwZSwgdGFyZ2V0KTtcbiAgICByZXR1cm4gW1xuICAgICAgICB0YXJnZXRJZCxcbiAgICAgICAgKCk9PnJlZ2lzdHJ5LnJlbW92ZVRhcmdldCh0YXJnZXRJZClcbiAgICBdO1xufVxuZXhwb3J0IGZ1bmN0aW9uIHJlZ2lzdGVyU291cmNlKHR5cGUsIHNvdXJjZSwgbWFuYWdlcikge1xuICAgIGNvbnN0IHJlZ2lzdHJ5ID0gbWFuYWdlci5nZXRSZWdpc3RyeSgpO1xuICAgIGNvbnN0IHNvdXJjZUlkID0gcmVnaXN0cnkuYWRkU291cmNlKHR5cGUsIHNvdXJjZSk7XG4gICAgcmV0dXJuIFtcbiAgICAgICAgc291cmNlSWQsXG4gICAgICAgICgpPT5yZWdpc3RyeS5yZW1vdmVTb3VyY2Uoc291cmNlSWQpXG4gICAgXTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVnaXN0cmF0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/internals/registration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/internals/wrapConnectorHooks.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/internals/wrapConnectorHooks.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrapConnectorHooks: () => (/* binding */ wrapConnectorHooks)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\nfunction throwIfCompositeComponentElement(element) {\n    // Custom components can no longer be wrapped directly in React DnD 2.0\n    // so that we don't need to depend on findDOMNode() from react-dom.\n    if (typeof element.type === 'string') {\n        return;\n    }\n    const displayName = element.type.displayName || element.type.name || 'the component';\n    throw new Error('Only native element nodes can now be passed to React DnD connectors.' + `You can either wrap ${displayName} into a <div>, or turn it into a ` + 'drag source or a drop target itself.');\n}\nfunction wrapHookToRecognizeElement(hook) {\n    return (elementOrNode = null, options = null)=>{\n        // When passed a node, call the hook straight away.\n        if (!(0,react__WEBPACK_IMPORTED_MODULE_1__.isValidElement)(elementOrNode)) {\n            const node = elementOrNode;\n            hook(node, options);\n            // return the node so it can be chained (e.g. when within callback refs\n            // <div ref={node => connectDragSource(connectDropTarget(node))}/>\n            return node;\n        }\n        // If passed a ReactElement, clone it and attach this function as a ref.\n        // This helps us achieve a neat API where user doesn't even know that refs\n        // are being used under the hood.\n        const element = elementOrNode;\n        throwIfCompositeComponentElement(element);\n        // When no options are passed, use the hook directly\n        const ref = options ? (node)=>hook(node, options)\n         : hook;\n        return cloneWithRef(element, ref);\n    };\n}\nfunction wrapConnectorHooks(hooks) {\n    const wrappedHooks = {};\n    Object.keys(hooks).forEach((key)=>{\n        const hook = hooks[key];\n        // ref objects should be passed straight through without wrapping\n        if (key.endsWith('Ref')) {\n            wrappedHooks[key] = hooks[key];\n        } else {\n            const wrappedHook = wrapHookToRecognizeElement(hook);\n            wrappedHooks[key] = ()=>wrappedHook\n            ;\n        }\n    });\n    return wrappedHooks;\n}\nfunction setRef(ref, node) {\n    if (typeof ref === 'function') {\n        ref(node);\n    } else {\n        ref.current = node;\n    }\n}\nfunction cloneWithRef(element, newRef) {\n    const previousRef = element.ref;\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof previousRef !== 'string', 'Cannot connect React DnD to an element with an existing string ref. ' + 'Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. ' + 'Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs');\n    if (!previousRef) {\n        // When there is no ref on the element, use the new ref directly\n        return (0,react__WEBPACK_IMPORTED_MODULE_1__.cloneElement)(element, {\n            ref: newRef\n        });\n    } else {\n        return (0,react__WEBPACK_IMPORTED_MODULE_1__.cloneElement)(element, {\n            ref: (node)=>{\n                setRef(previousRef, node);\n                setRef(newRef, node);\n            }\n        });\n    }\n}\n\n//# sourceMappingURL=wrapConnectorHooks.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/internals/wrapConnectorHooks.js\n");

/***/ })

};
;