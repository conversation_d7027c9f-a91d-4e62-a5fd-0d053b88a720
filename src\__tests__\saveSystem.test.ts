import { SaveSystem } from '@/utils/saveSystem';
import { GameState, ComponentType, ResourceType, Direction } from '@/types/game';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('SaveSystem', () => {
  let mockGameState: GameState;

  beforeEach(() => {
    localStorageMock.clear();
    
    mockGameState = {
      components: new Map([
        ['comp1', {
          id: 'comp1',
          type: ComponentType.MINER,
          position: { x: 0, y: 0 },
          direction: Direction.NORTH,
          inventory: new Map([[ResourceType.IRON_ORE, 10]]),
          connections: { inputs: [], outputs: ['comp2'] },
          isActive: true,
          lastProcessTime: Date.now(),
        }],
        ['comp2', {
          id: 'comp2',
          type: ComponentType.CONVEYOR,
          position: { x: 2, y: 0 },
          direction: Direction.EAST,
          inventory: new Map(),
          connections: { inputs: ['comp1'], outputs: [] },
          isActive: false,
          lastProcessTime: 0,
        }],
      ]),
      gridSize: { width: 50, height: 50 },
      isRunning: true,
      gameTime: 120000,
      resources: new Map([
        [ResourceType.IRON_ORE, 1000],
        [ResourceType.COPPER_ORE, 500],
      ]),
      statistics: {
        totalProduction: new Map([[ResourceType.IRON_ORE, 50]]),
        totalConsumption: new Map(),
        efficiency: 0.75,
        bottlenecks: [],
      },
    };
  });

  describe('Save and Load', () => {
    it('should save and load a game state', () => {
      const saveName = 'Test Save';
      
      // Save the game
      const saveResult = SaveSystem.saveGame(mockGameState, saveName);
      expect(saveResult).toBe(true);
      
      // Get save slots
      const saveSlots = SaveSystem.getSaveSlots();
      expect(saveSlots).toHaveLength(1);
      expect(saveSlots[0].name).toBe(saveName);
      expect(saveSlots[0].metadata.componentCount).toBe(2);
      expect(saveSlots[0].metadata.efficiency).toBe(0.75);
      
      // Load the game
      const loadedState = SaveSystem.loadGame(saveSlots[0].id);
      expect(loadedState).toBeTruthy();
      
      if (loadedState) {
        expect(loadedState.components.size).toBe(2);
        expect(loadedState.gridSize).toEqual({ width: 50, height: 50 });
        expect(loadedState.isRunning).toBe(true);
        expect(loadedState.gameTime).toBe(120000);
        expect(loadedState.resources.get(ResourceType.IRON_ORE)).toBe(1000);
        expect(loadedState.statistics.efficiency).toBe(0.75);
        
        // Check component details
        const comp1 = loadedState.components.get('comp1');
        expect(comp1?.type).toBe(ComponentType.MINER);
        expect(comp1?.position).toEqual({ x: 0, y: 0 });
        expect(comp1?.inventory.get(ResourceType.IRON_ORE)).toBe(10);
        expect(comp1?.connections.outputs).toEqual(['comp2']);
      }
    });

    it('should handle multiple saves', () => {
      // Save multiple games
      SaveSystem.saveGame(mockGameState, 'Save 1');
      SaveSystem.saveGame(mockGameState, 'Save 2');
      SaveSystem.saveGame(mockGameState, 'Save 3');
      
      const saveSlots = SaveSystem.getSaveSlots();
      expect(saveSlots).toHaveLength(3);
      expect(saveSlots.map(s => s.name)).toEqual(['Save 3', 'Save 2', 'Save 1']);
    });

    it('should replace existing save with same name', () => {
      const saveName = 'Duplicate Save';
      
      // Save twice with same name
      SaveSystem.saveGame(mockGameState, saveName);
      SaveSystem.saveGame(mockGameState, saveName);
      
      const saveSlots = SaveSystem.getSaveSlots();
      expect(saveSlots).toHaveLength(1);
      expect(saveSlots[0].name).toBe(saveName);
    });

    it('should delete saves', () => {
      SaveSystem.saveGame(mockGameState, 'Save to Delete');
      
      let saveSlots = SaveSystem.getSaveSlots();
      expect(saveSlots).toHaveLength(1);
      
      const deleteResult = SaveSystem.deleteSave(saveSlots[0].id);
      expect(deleteResult).toBe(true);
      
      saveSlots = SaveSystem.getSaveSlots();
      expect(saveSlots).toHaveLength(0);
    });
  });

  describe('Auto-save', () => {
    it('should auto-save and load', () => {
      // Auto-save
      const autoSaveResult = SaveSystem.autoSave(mockGameState);
      expect(autoSaveResult).toBe(true);
      
      // Check if auto-save exists
      expect(SaveSystem.hasAutoSave()).toBe(true);
      
      // Get auto-save metadata
      const metadata = SaveSystem.getAutoSaveMetadata();
      expect(metadata).toBeTruthy();
      expect(metadata.componentCount).toBe(2);
      expect(metadata.efficiency).toBe(0.75);
      
      // Load auto-save
      const loadedState = SaveSystem.loadAutoSave();
      expect(loadedState).toBeTruthy();
      
      if (loadedState) {
        expect(loadedState.components.size).toBe(2);
        expect(loadedState.statistics.efficiency).toBe(0.75);
      }
    });
  });

  describe('Error handling', () => {
    it('should handle invalid save IDs', () => {
      const result = SaveSystem.loadGame('invalid-id');
      expect(result).toBeNull();
    });

    it('should handle localStorage errors gracefully', () => {
      // Mock localStorage to throw an error
      const originalSetItem = localStorageMock.setItem;
      localStorageMock.setItem = () => {
        throw new Error('Storage full');
      };
      
      const result = SaveSystem.saveGame(mockGameState, 'Error Test');
      expect(result).toBe(false);
      
      // Restore original function
      localStorageMock.setItem = originalSetItem;
    });
  });
});
