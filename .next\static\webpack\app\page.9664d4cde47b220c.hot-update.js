"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/gameStore.ts":
/*!********************************!*\
  !*** ./src/store/gameStore.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGameStore: () => (/* binding */ useGameStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _types_game__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/game */ \"(app-pages-browser)/./src/types/game.ts\");\n/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/helpers */ \"(app-pages-browser)/./src/utils/helpers.ts\");\n/* harmony import */ var _engine_simulation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/engine/simulation */ \"(app-pages-browser)/./src/engine/simulation.ts\");\n/* harmony import */ var _analytics_performanceAnalyzer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/analytics/performanceAnalyzer */ \"(app-pages-browser)/./src/analytics/performanceAnalyzer.ts\");\n\n\n\n\n\n\nconst GRID_SIZE = {\n    width: 50,\n    height: 50\n};\nconst simulationEngine = new _engine_simulation__WEBPACK_IMPORTED_MODULE_2__.SimulationEngine();\nconst performanceAnalyzer = new _analytics_performanceAnalyzer__WEBPACK_IMPORTED_MODULE_3__.PerformanceAnalyzer();\nconst initialState = {\n    components: new Map(),\n    gridSize: GRID_SIZE,\n    isRunning: false,\n    gameTime: 0,\n    resources: new Map([\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.IRON_ORE,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COPPER_ORE,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COAL,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.IRON_PLATE,\n            100\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COPPER_PLATE,\n            100\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.GEAR,\n            50\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.CIRCUIT,\n            50\n        ]\n    ]),\n    statistics: {\n        totalProduction: new Map(),\n        totalConsumption: new Map(),\n        efficiency: 0,\n        bottlenecks: []\n    }\n};\nconst initialStoreState = {\n    ...initialState,\n    factoryAnalytics: null\n};\nconst useGameStore = (0,zustand__WEBPACK_IMPORTED_MODULE_4__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_5__.subscribeWithSelector)((set, get)=>({\n        ...initialStoreState,\n        addComponent: function(type, position) {\n            let direction = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.NORTH;\n            const state = get();\n            const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[type];\n            // Check if position is valid and not occupied\n            if (!isPositionValid(position, definition.size, state)) {\n                return null;\n            }\n            const id = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_1__.generateId)();\n            const component = {\n                id,\n                type,\n                position,\n                direction,\n                inventory: new Map(),\n                connections: {\n                    inputs: [],\n                    outputs: []\n                },\n                isActive: false,\n                lastProcessTime: 0\n            };\n            set((state)=>{\n                const newComponents = new Map(state.components).set(id, component);\n                // Auto-connect to adjacent components\n                autoConnectComponent(id, component, newComponents);\n                return {\n                    components: newComponents\n                };\n            });\n            return id;\n        },\n        removeComponent: (id)=>{\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const component = newComponents.get(id);\n                if (component) {\n                    // Remove all connections to this component\n                    newComponents.forEach((comp)=>{\n                        comp.connections.inputs = comp.connections.inputs.filter((connId)=>connId !== id);\n                        comp.connections.outputs = comp.connections.outputs.filter((connId)=>connId !== id);\n                    });\n                    newComponents.delete(id);\n                }\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        moveComponent: (id, position)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n                if (!isPositionValid(position, definition.size, state, id)) {\n                    return state;\n                }\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    position\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        rotateComponent: (id)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const newDirection = (component.direction + 1) % 4;\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    direction: newDirection\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        connectComponents: (fromId, toId)=>{\n            const state = get();\n            const fromComponent = state.components.get(fromId);\n            const toComponent = state.components.get(toId);\n            if (!fromComponent || !toComponent || fromId === toId) {\n                return false;\n            }\n            const fromDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[fromComponent.type];\n            const toDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[toComponent.type];\n            // Check connection limits\n            if (fromComponent.connections.outputs.length >= fromDef.maxOutputs || toComponent.connections.inputs.length >= toDef.maxInputs) {\n                return false;\n            }\n            // Check if already connected\n            if (fromComponent.connections.outputs.includes(toId)) {\n                return false;\n            }\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const newFromComponent = {\n                    ...fromComponent\n                };\n                const newToComponent = {\n                    ...toComponent\n                };\n                newFromComponent.connections.outputs.push(toId);\n                newToComponent.connections.inputs.push(fromId);\n                newComponents.set(fromId, newFromComponent);\n                newComponents.set(toId, newToComponent);\n                return {\n                    components: newComponents\n                };\n            });\n            return true;\n        },\n        disconnectComponents: (fromId, toId)=>{\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const fromComponent = newComponents.get(fromId);\n                const toComponent = newComponents.get(toId);\n                if (fromComponent && toComponent) {\n                    fromComponent.connections.outputs = fromComponent.connections.outputs.filter((id)=>id !== toId);\n                    toComponent.connections.inputs = toComponent.connections.inputs.filter((id)=>id !== fromId);\n                }\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        setComponentRecipe: (id, recipeId)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const recipe = _types_game__WEBPACK_IMPORTED_MODULE_0__.RECIPES[recipeId];\n                if (!recipe) return state;\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    recipe\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        toggleSimulation: ()=>{\n            set((state)=>({\n                    isRunning: !state.isRunning\n                }));\n        },\n        updateSimulation: ()=>{\n            const state = get();\n            if (!state.isRunning) return;\n            const updates = simulationEngine.updateSimulation(state);\n            if (Object.keys(updates).length > 0) {\n                const newState = {\n                    ...state,\n                    ...updates\n                };\n                const analytics = performanceAnalyzer.analyzeFactory(newState);\n                set((currentState)=>({\n                        ...currentState,\n                        ...updates,\n                        factoryAnalytics: analytics\n                    }));\n            }\n        },\n        getPerformanceMetrics: ()=>{\n            const state = get();\n            return {\n                throughput: state.statistics.totalProduction,\n                utilization: new Map(),\n                bottlenecks: state.statistics.bottlenecks,\n                efficiency: state.statistics.efficiency\n            };\n        },\n        getFactoryAnalytics: ()=>{\n            const state = get();\n            if (state.factoryAnalytics) {\n                return state.factoryAnalytics;\n            }\n            // Generate analytics on demand if not available\n            return performanceAnalyzer.analyzeFactory(state);\n        },\n        getHistoricalData: (key)=>{\n            return performanceAnalyzer.getHistoricalData(key);\n        },\n        exportGameState: ()=>{\n            const state = get();\n            const exportData = {\n                components: Array.from(state.components.entries()),\n                gridSize: state.gridSize,\n                gameTime: state.gameTime,\n                resources: Array.from(state.resources.entries())\n            };\n            return JSON.stringify(exportData, null, 2);\n        },\n        importGameState: (jsonState)=>{\n            try {\n                const data = JSON.parse(jsonState);\n                set({\n                    components: new Map(data.components),\n                    gridSize: data.gridSize || GRID_SIZE,\n                    gameTime: data.gameTime || 0,\n                    resources: new Map(data.resources),\n                    isRunning: false\n                });\n                return true;\n            } catch (error) {\n                console.error('Failed to import game state:', error);\n                return false;\n            }\n        },\n        resetGame: ()=>{\n            set(initialState);\n        }\n    })));\n// Helper functions\nfunction isPositionValid(position, size, state, excludeId) {\n    // Check bounds\n    if (position.x < 0 || position.y < 0 || position.x + size.width > state.gridSize.width || position.y + size.height > state.gridSize.height) {\n        return false;\n    }\n    // Check for overlaps with existing components\n    for (const [id, component] of state.components){\n        if (excludeId && id === excludeId) continue;\n        const compDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n        const compPos = component.position;\n        // Check if rectangles overlap\n        if (!(position.x >= compPos.x + compDef.size.width || position.x + size.width <= compPos.x || position.y >= compPos.y + compDef.size.height || position.y + size.height <= compPos.y)) {\n            return false;\n        }\n    }\n    return true;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/gameStore.ts\n"));

/***/ })

});