import { GameState } from '@/types/game';

export interface SaveSlot {
  id: string;
  name: string;
  timestamp: number;
  gameState: string; // JSON string
  thumbnail?: string; // Base64 encoded image
  metadata: {
    componentCount: number;
    efficiency: number;
    gameTime: number;
    version: string;
  };
}

export class SaveSystem {
  private static readonly STORAGE_KEY = 'factory-game-saves';
  private static readonly AUTO_SAVE_KEY = 'factory-game-autosave';
  private static readonly MAX_SAVES = 10;

  // Get all save slots
  static getSaveSlots(): SaveSlot[] {
    try {
      const saves = localStorage.getItem(this.STORAGE_KEY);
      return saves ? JSON.parse(saves) : [];
    } catch (error) {
      console.error('Failed to load save slots:', error);
      return [];
    }
  }

  // Save game to a specific slot
  static saveGame(gameState: GameState, slotName: string): boolean {
    try {
      const saves = this.getSaveSlots();
      const gameStateJson = this.serializeGameState(gameState);
      
      const saveSlot: SaveSlot = {
        id: this.generateSaveId(),
        name: slotName,
        timestamp: Date.now(),
        gameState: gameStateJson,
        metadata: {
          componentCount: gameState.components.size,
          efficiency: gameState.statistics.efficiency,
          gameTime: gameState.gameTime,
          version: '1.0.0',
        },
      };

      // Remove existing save with same name
      const filteredSaves = saves.filter(save => save.name !== slotName);
      
      // Add new save
      filteredSaves.unshift(saveSlot);
      
      // Keep only the most recent saves
      const limitedSaves = filteredSaves.slice(0, this.MAX_SAVES);
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(limitedSaves));
      return true;
    } catch (error) {
      console.error('Failed to save game:', error);
      return false;
    }
  }

  // Load game from a specific slot
  static loadGame(slotId: string): GameState | null {
    try {
      const saves = this.getSaveSlots();
      const saveSlot = saves.find(save => save.id === slotId);
      
      if (!saveSlot) {
        return null;
      }

      return this.deserializeGameState(saveSlot.gameState);
    } catch (error) {
      console.error('Failed to load game:', error);
      return null;
    }
  }

  // Delete a save slot
  static deleteSave(slotId: string): boolean {
    try {
      const saves = this.getSaveSlots();
      const filteredSaves = saves.filter(save => save.id !== slotId);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredSaves));
      return true;
    } catch (error) {
      console.error('Failed to delete save:', error);
      return false;
    }
  }

  // Auto-save functionality
  static autoSave(gameState: GameState): boolean {
    try {
      const gameStateJson = this.serializeGameState(gameState);
      const autoSave = {
        timestamp: Date.now(),
        gameState: gameStateJson,
        metadata: {
          componentCount: gameState.components.size,
          efficiency: gameState.statistics.efficiency,
          gameTime: gameState.gameTime,
        },
      };
      
      localStorage.setItem(this.AUTO_SAVE_KEY, JSON.stringify(autoSave));
      return true;
    } catch (error) {
      console.error('Failed to auto-save:', error);
      return false;
    }
  }

  // Load auto-save
  static loadAutoSave(): GameState | null {
    try {
      const autoSave = localStorage.getItem(this.AUTO_SAVE_KEY);
      if (!autoSave) return null;
      
      const parsed = JSON.parse(autoSave);
      return this.deserializeGameState(parsed.gameState);
    } catch (error) {
      console.error('Failed to load auto-save:', error);
      return null;
    }
  }

  // Check if auto-save exists
  static hasAutoSave(): boolean {
    return localStorage.getItem(this.AUTO_SAVE_KEY) !== null;
  }

  // Get auto-save metadata
  static getAutoSaveMetadata(): any {
    try {
      const autoSave = localStorage.getItem(this.AUTO_SAVE_KEY);
      if (!autoSave) return null;
      
      const parsed = JSON.parse(autoSave);
      return {
        timestamp: parsed.timestamp,
        ...parsed.metadata,
      };
    } catch (error) {
      return null;
    }
  }

  // Export save to file
  static exportSave(slotId: string): void {
    const saves = this.getSaveSlots();
    const saveSlot = saves.find(save => save.id === slotId);
    
    if (!saveSlot) return;
    
    const blob = new Blob([JSON.stringify(saveSlot, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${saveSlot.name}-${new Date(saveSlot.timestamp).toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }

  // Import save from file
  static async importSave(file: File): Promise<boolean> {
    try {
      const text = await file.text();
      const saveSlot: SaveSlot = JSON.parse(text);
      
      // Validate save slot structure
      if (!this.validateSaveSlot(saveSlot)) {
        throw new Error('Invalid save file format');
      }
      
      const saves = this.getSaveSlots();
      
      // Generate new ID to avoid conflicts
      saveSlot.id = this.generateSaveId();
      saveSlot.name = `${saveSlot.name} (Imported)`;
      
      saves.unshift(saveSlot);
      const limitedSaves = saves.slice(0, this.MAX_SAVES);
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(limitedSaves));
      return true;
    } catch (error) {
      console.error('Failed to import save:', error);
      return false;
    }
  }

  // Private helper methods
  private static generateSaveId(): string {
    return `save_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static serializeGameState(gameState: GameState): string {
    const serializable = {
      components: Array.from(gameState.components.entries()),
      gridSize: gameState.gridSize,
      isRunning: gameState.isRunning,
      gameTime: gameState.gameTime,
      resources: Array.from(gameState.resources.entries()),
      statistics: {
        totalProduction: Array.from(gameState.statistics.totalProduction.entries()),
        totalConsumption: Array.from(gameState.statistics.totalConsumption.entries()),
        efficiency: gameState.statistics.efficiency,
        bottlenecks: gameState.statistics.bottlenecks,
      },
    };
    
    return JSON.stringify(serializable);
  }

  private static deserializeGameState(gameStateJson: string): GameState {
    const data = JSON.parse(gameStateJson);
    
    return {
      components: new Map(data.components),
      gridSize: data.gridSize,
      isRunning: data.isRunning,
      gameTime: data.gameTime,
      resources: new Map(data.resources),
      statistics: {
        totalProduction: new Map(data.statistics.totalProduction),
        totalConsumption: new Map(data.statistics.totalConsumption),
        efficiency: data.statistics.efficiency,
        bottlenecks: data.statistics.bottlenecks,
      },
    };
  }

  private static validateSaveSlot(saveSlot: any): boolean {
    return (
      saveSlot &&
      typeof saveSlot.id === 'string' &&
      typeof saveSlot.name === 'string' &&
      typeof saveSlot.timestamp === 'number' &&
      typeof saveSlot.gameState === 'string' &&
      saveSlot.metadata &&
      typeof saveSlot.metadata.componentCount === 'number'
    );
  }
}
