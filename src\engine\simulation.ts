import {
  GameComponent,
  ComponentType,
  ResourceType,
  COMPONENT_DEFINITIONS,
  PerformanceMetrics,
  GameState,
} from '@/types/game';

export class SimulationEngine {
  private lastUpdateTime: number = 0;
  private readonly UPDATE_INTERVAL = 100; // 100ms updates

  constructor() {
    this.lastUpdateTime = Date.now();
  }

  public updateSimulation(gameState: GameState): Partial<GameState> {
    const currentTime = Date.now();
    const deltaTime = currentTime - this.lastUpdateTime;
    
    if (deltaTime < this.UPDATE_INTERVAL) {
      return {};
    }

    this.lastUpdateTime = currentTime;

    const updatedComponents = new Map(gameState.components);
    const updatedResources = new Map(gameState.resources);
    const productionStats = new Map<ResourceType, number>();
    const consumptionStats = new Map<ResourceType, number>();

    // Process each component
    for (const [id, component] of updatedComponents) {
      const updatedComponent = this.processComponent(
        component,
        updatedComponents,
        deltaTime,
        productionStats,
        consumptionStats
      );
      updatedComponents.set(id, updatedComponent);
    }

    // Update global resources (for miners)
    this.updateGlobalResources(updatedComponents, updatedResources, deltaTime);

    // Calculate performance metrics
    const metrics = this.calculatePerformanceMetrics(updatedComponents, productionStats, consumptionStats);

    return {
      components: updatedComponents,
      resources: updatedResources,
      gameTime: gameState.gameTime + deltaTime,
      statistics: {
        totalProduction: productionStats,
        totalConsumption: consumptionStats,
        efficiency: metrics.efficiency,
        bottlenecks: metrics.bottlenecks,
      },
    };
  }

  private processComponent(
    component: GameComponent,
    allComponents: Map<string, GameComponent>,
    deltaTime: number,
    productionStats: Map<ResourceType, number>,
    consumptionStats: Map<ResourceType, number>
  ): GameComponent {
    const definition = COMPONENT_DEFINITIONS[component.type];
    const updatedComponent = { ...component };

    switch (component.type) {
      case ComponentType.MINER:
        this.processMiner(updatedComponent, deltaTime, productionStats);
        break;
      case ComponentType.ASSEMBLER:
        this.processAssembler(updatedComponent, deltaTime, productionStats, consumptionStats);
        break;
      case ComponentType.CONVEYOR:
        this.processConveyor(updatedComponent, allComponents, deltaTime);
        break;
      case ComponentType.STORAGE:
        this.processStorage(updatedComponent, allComponents, deltaTime);
        break;
      case ComponentType.SPLITTER:
        this.processSplitter(updatedComponent, allComponents, deltaTime);
        break;
      case ComponentType.MERGER:
        this.processMerger(updatedComponent, allComponents, deltaTime);
        break;
    }

    return updatedComponent;
  }

  private processMiner(
    component: GameComponent,
    deltaTime: number,
    productionStats: Map<ResourceType, number>
  ): void {
    const definition = COMPONENT_DEFINITIONS[component.type];
    const timeSinceLastProcess = Date.now() - component.lastProcessTime;
    const processInterval = 1000 / definition.speed; // Convert speed to interval

    if (timeSinceLastProcess >= processInterval) {
      // For simplicity, miners produce iron ore
      const resourceType = ResourceType.IRON_ORE;
      const currentAmount = component.inventory.get(resourceType) || 0;
      const maxStorage = 50; // Max items a miner can store

      if (currentAmount < maxStorage) {
        component.inventory.set(resourceType, currentAmount + 1);
        component.lastProcessTime = Date.now();
        component.isActive = true;

        // Update production stats
        const currentProduction = productionStats.get(resourceType) || 0;
        productionStats.set(resourceType, currentProduction + 1);
      } else {
        component.isActive = false; // Storage full
      }
    }
  }

  private processAssembler(
    component: GameComponent,
    deltaTime: number,
    productionStats: Map<ResourceType, number>,
    consumptionStats: Map<ResourceType, number>
  ): void {
    if (!component.recipe) {
      component.isActive = false;
      return;
    }

    const timeSinceLastProcess = Date.now() - component.lastProcessTime;
    
    if (timeSinceLastProcess >= component.recipe.processingTime) {
      // Check if we have enough inputs
      const canProcess = component.recipe.inputs.every(input => {
        const available = component.inventory.get(input.resource) || 0;
        return available >= input.amount;
      });

      if (canProcess) {
        // Consume inputs
        component.recipe.inputs.forEach(input => {
          const current = component.inventory.get(input.resource) || 0;
          component.inventory.set(input.resource, current - input.amount);
          
          // Update consumption stats
          const currentConsumption = consumptionStats.get(input.resource) || 0;
          consumptionStats.set(input.resource, currentConsumption + input.amount);
        });

        // Produce outputs
        component.recipe.outputs.forEach(output => {
          const current = component.inventory.get(output.resource) || 0;
          component.inventory.set(output.resource, current + output.amount);
          
          // Update production stats
          const currentProduction = productionStats.get(output.resource) || 0;
          productionStats.set(output.resource, currentProduction + output.amount);
        });

        component.lastProcessTime = Date.now();
        component.isActive = true;
      } else {
        component.isActive = false; // Waiting for inputs
      }
    }
  }

  private processConveyor(
    component: GameComponent,
    allComponents: Map<string, GameComponent>,
    deltaTime: number
  ): void {
    const definition = COMPONENT_DEFINITIONS[component.type];
    const transferRate = definition.speed * (deltaTime / 1000); // Items per update
    let hasTransferred = false;

    // Try to move items to connected outputs
    component.connections.outputs.forEach(outputId => {
      const outputComponent = allComponents.get(outputId);
      if (!outputComponent) return;

      // Transfer items from this component to the output
      for (const [resourceType, amount] of component.inventory) {
        if (amount > 0) {
          const transferAmount = Math.min(amount, Math.floor(transferRate));
          if (transferAmount > 0) {
            const outputCurrent = outputComponent.inventory.get(resourceType) || 0;
            const maxCapacity = this.getComponentCapacity(outputComponent);
            const canAccept = Math.min(transferAmount, maxCapacity - outputCurrent);

            if (canAccept > 0) {
              component.inventory.set(resourceType, amount - canAccept);
              outputComponent.inventory.set(resourceType, outputCurrent + canAccept);
              hasTransferred = true;
            }
          }
          break; // Only transfer one resource type per update
        }
      }
    });

    // Also try to pull items from connected inputs if we have space
    if (!hasTransferred) {
      component.connections.inputs.forEach(inputId => {
        const inputComponent = allComponents.get(inputId);
        if (!inputComponent) return;

        for (const [resourceType, amount] of inputComponent.inventory) {
          if (amount > 0) {
            const transferAmount = Math.min(amount, Math.floor(transferRate));
            if (transferAmount > 0) {
              const currentAmount = component.inventory.get(resourceType) || 0;
              const maxCapacity = this.getComponentCapacity(component);
              const canAccept = Math.min(transferAmount, maxCapacity - currentAmount);

              if (canAccept > 0) {
                inputComponent.inventory.set(resourceType, amount - canAccept);
                component.inventory.set(resourceType, currentAmount + canAccept);
                hasTransferred = true;
              }
            }
            break; // Only transfer one resource type per update
          }
        }
      });
    }

    component.isActive = hasTransferred;
  }

  private processStorage(
    component: GameComponent,
    allComponents: Map<string, GameComponent>,
    deltaTime: number
  ): void {
    // Storage just holds items, but can pass them through
    this.processConveyor(component, allComponents, deltaTime);
  }

  private processSplitter(
    component: GameComponent,
    allComponents: Map<string, GameComponent>,
    deltaTime: number
  ): void {
    const definition = COMPONENT_DEFINITIONS[component.type];
    const transferRate = definition.speed * (deltaTime / 1000);

    // Split items evenly between outputs
    const outputs = component.connections.outputs
      .map(id => allComponents.get(id))
      .filter(comp => comp !== undefined);

    if (outputs.length === 0) return;

    for (const [resourceType, amount] of component.inventory) {
      if (amount > 0) {
        const transferAmount = Math.min(amount, Math.floor(transferRate));
        const perOutput = Math.floor(transferAmount / outputs.length);

        if (perOutput > 0) {
          outputs.forEach(output => {
            const outputCurrent = output!.inventory.get(resourceType) || 0;
            const maxCapacity = this.getComponentCapacity(output!);
            const canAccept = Math.min(perOutput, maxCapacity - outputCurrent);

            if (canAccept > 0) {
              const currentAmount = component.inventory.get(resourceType) || 0;
              component.inventory.set(resourceType, currentAmount - canAccept);
              output!.inventory.set(resourceType, outputCurrent + canAccept);
              component.isActive = true;
            }
          });
        }
        break; // Only process one resource type per update
      }
    }
  }

  private processMerger(
    component: GameComponent,
    allComponents: Map<string, GameComponent>,
    deltaTime: number
  ): void {
    // Merger just passes items through like a conveyor
    this.processConveyor(component, allComponents, deltaTime);
  }

  private getComponentCapacity(component: GameComponent): number {
    // Return max capacity for different component types
    switch (component.type) {
      case ComponentType.STORAGE:
        return 1000;
      case ComponentType.ASSEMBLER:
        return 100;
      case ComponentType.MINER:
        return 50;
      default:
        return 10; // Default for conveyors, splitters, etc.
    }
  }

  private updateGlobalResources(
    components: Map<string, GameComponent>,
    resources: Map<ResourceType, number>,
    deltaTime: number
  ): void {
    // This could be used for global resource depletion, but for now we keep it simple
  }

  private calculatePerformanceMetrics(
    components: Map<string, GameComponent>,
    productionStats: Map<ResourceType, number>,
    consumptionStats: Map<ResourceType, number>
  ): PerformanceMetrics {
    const throughput = new Map<ResourceType, number>();
    const utilization = new Map<string, number>();
    const bottlenecks: string[] = [];

    // Calculate throughput (items per minute)
    for (const [resource, amount] of productionStats) {
      throughput.set(resource, amount * 600); // Convert to per minute
    }

    // Calculate utilization for each component
    let totalUtilization = 0;
    let activeComponents = 0;

    for (const [id, component] of components) {
      const util = component.isActive ? 100 : 0;
      utilization.set(id, util);
      totalUtilization += util;
      activeComponents++;

      // Identify bottlenecks (components that are inactive due to full outputs or empty inputs)
      if (!component.isActive && component.type !== ComponentType.STORAGE) {
        bottlenecks.push(id);
      }
    }

    const efficiency = activeComponents > 0 ? totalUtilization / activeComponents / 100 : 0;

    return {
      throughput,
      utilization,
      bottlenecks,
      efficiency,
    };
  }
}
