import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import {
  GameState,
  GameComponent,
  ComponentType,
  Position,
  Direction,
  ResourceType,
  PerformanceMetrics,
  COMPONENT_DEFINITIONS,
  RECIPES,
} from '@/types/game';
import { generateId, getOppositeDirection } from '@/utils/helpers';
import { SimulationEngine } from '@/engine/simulation';
import { PerformanceAnalyzer, FactoryAnalytics } from '@/analytics/performanceAnalyzer';

interface GameStore extends GameState {
  // Analytics
  factoryAnalytics: FactoryAnalytics | null;

  // Actions
  addComponent: (type: ComponentType, position: Position, direction?: Direction) => string | null;
  removeComponent: (id: string) => void;
  moveComponent: (id: string, position: Position) => void;
  rotateComponent: (id: string) => void;
  connectComponents: (fromId: string, toId: string) => boolean;
  disconnectComponents: (fromId: string, toId: string) => void;
  setComponentRecipe: (id: string, recipeId: string) => void;
  toggleSimulation: () => void;
  updateSimulation: () => void;
  getPerformanceMetrics: () => PerformanceMetrics;
  getFactoryAnalytics: () => FactoryAnalytics;
  getHistoricalData: (key: string) => number[];
  exportGameState: () => string;
  importGameState: (jsonState: string) => boolean;
  resetGame: () => void;
}

const GRID_SIZE = { width: 50, height: 50 };
const simulationEngine = new SimulationEngine();
const performanceAnalyzer = new PerformanceAnalyzer();

const initialState: GameState = {
  components: new Map(),
  gridSize: GRID_SIZE,
  isRunning: false,
  gameTime: 0,
  resources: new Map([
    [ResourceType.IRON_ORE, 1000],
    [ResourceType.COPPER_ORE, 1000],
    [ResourceType.COAL, 1000],
    [ResourceType.IRON_PLATE, 100],
    [ResourceType.COPPER_PLATE, 100],
    [ResourceType.GEAR, 50],
    [ResourceType.CIRCUIT, 50],
  ]),
  statistics: {
    totalProduction: new Map(),
    totalConsumption: new Map(),
    efficiency: 0,
    bottlenecks: [],
  },
};

const initialStoreState = {
  ...initialState,
  factoryAnalytics: null as FactoryAnalytics | null,
};

export const useGameStore = create<GameStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialStoreState,

    addComponent: (type: ComponentType, position: Position, direction = Direction.NORTH) => {
      const state = get();
      const definition = COMPONENT_DEFINITIONS[type];

      // Check if position is valid and not occupied
      if (!isPositionValid(position, definition.size, state)) {
        return null;
      }

      const id = generateId();
      const component: GameComponent = {
        id,
        type,
        position,
        direction,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      set((state) => {
        const newComponents = new Map(state.components).set(id, component);

        // Auto-connect to adjacent components
        autoConnectComponent(id, component, newComponents);

        return { components: newComponents };
      });

      return id;
    },

    removeComponent: (id: string) => {
      set((state) => {
        const newComponents = new Map(state.components);
        const component = newComponents.get(id);
        
        if (component) {
          // Remove all connections to this component
          newComponents.forEach((comp) => {
            comp.connections.inputs = comp.connections.inputs.filter(connId => connId !== id);
            comp.connections.outputs = comp.connections.outputs.filter(connId => connId !== id);
          });
          
          newComponents.delete(id);
        }

        return { components: newComponents };
      });
    },

    moveComponent: (id: string, position: Position) => {
      set((state) => {
        const component = state.components.get(id);
        if (!component) return state;

        const definition = COMPONENT_DEFINITIONS[component.type];
        if (!isPositionValid(position, definition.size, state, id)) {
          return state;
        }

        const newComponents = new Map(state.components);
        newComponents.set(id, { ...component, position });
        return { components: newComponents };
      });
    },

    rotateComponent: (id: string) => {
      set((state) => {
        const component = state.components.get(id);
        if (!component) return state;

        const newDirection = (component.direction + 1) % 4;
        const newComponents = new Map(state.components);
        newComponents.set(id, { ...component, direction: newDirection });
        return { components: newComponents };
      });
    },

    connectComponents: (fromId: string, toId: string) => {
      const state = get();
      const fromComponent = state.components.get(fromId);
      const toComponent = state.components.get(toId);

      if (!fromComponent || !toComponent || fromId === toId) {
        return false;
      }

      const fromDef = COMPONENT_DEFINITIONS[fromComponent.type];
      const toDef = COMPONENT_DEFINITIONS[toComponent.type];

      // Check connection limits
      if (fromComponent.connections.outputs.length >= fromDef.maxOutputs ||
          toComponent.connections.inputs.length >= toDef.maxInputs) {
        return false;
      }

      // Check if already connected
      if (fromComponent.connections.outputs.includes(toId)) {
        return false;
      }

      set((state) => {
        const newComponents = new Map(state.components);
        const newFromComponent = { ...fromComponent };
        const newToComponent = { ...toComponent };

        newFromComponent.connections.outputs.push(toId);
        newToComponent.connections.inputs.push(fromId);

        newComponents.set(fromId, newFromComponent);
        newComponents.set(toId, newToComponent);

        return { components: newComponents };
      });

      return true;
    },

    disconnectComponents: (fromId: string, toId: string) => {
      set((state) => {
        const newComponents = new Map(state.components);
        const fromComponent = newComponents.get(fromId);
        const toComponent = newComponents.get(toId);

        if (fromComponent && toComponent) {
          fromComponent.connections.outputs = fromComponent.connections.outputs.filter(id => id !== toId);
          toComponent.connections.inputs = toComponent.connections.inputs.filter(id => id !== fromId);
        }

        return { components: newComponents };
      });
    },

    setComponentRecipe: (id: string, recipeId: string) => {
      set((state) => {
        const component = state.components.get(id);
        if (!component) return state;

        const recipe = RECIPES[recipeId];
        if (!recipe) return state;

        const newComponents = new Map(state.components);
        newComponents.set(id, { ...component, recipe });
        return { components: newComponents };
      });
    },

    toggleSimulation: () => {
      set((state) => ({ isRunning: !state.isRunning }));
    },

    updateSimulation: () => {
      const state = get();
      if (!state.isRunning) return;

      const updates = simulationEngine.updateSimulation(state);
      if (Object.keys(updates).length > 0) {
        const newState = { ...state, ...updates };
        const analytics = performanceAnalyzer.analyzeFactory(newState);
        set((currentState) => ({
          ...currentState,
          ...updates,
          factoryAnalytics: analytics
        }));
      }
    },

    getPerformanceMetrics: (): PerformanceMetrics => {
      const state = get();
      return {
        throughput: state.statistics.totalProduction,
        utilization: new Map(),
        bottlenecks: state.statistics.bottlenecks,
        efficiency: state.statistics.efficiency,
      };
    },

    getFactoryAnalytics: (): FactoryAnalytics => {
      const state = get();
      if (state.factoryAnalytics) {
        return state.factoryAnalytics;
      }
      // Generate analytics on demand if not available
      return performanceAnalyzer.analyzeFactory(state);
    },

    getHistoricalData: (key: string): number[] => {
      return performanceAnalyzer.getHistoricalData(key);
    },

    exportGameState: () => {
      const state = get();
      const exportData = {
        components: Array.from(state.components.entries()),
        gridSize: state.gridSize,
        gameTime: state.gameTime,
        resources: Array.from(state.resources.entries()),
      };
      return JSON.stringify(exportData, null, 2);
    },

    importGameState: (jsonState: string) => {
      try {
        const data = JSON.parse(jsonState);
        set({
          components: new Map(data.components),
          gridSize: data.gridSize || GRID_SIZE,
          gameTime: data.gameTime || 0,
          resources: new Map(data.resources),
          isRunning: false,
        });
        return true;
      } catch (error) {
        console.error('Failed to import game state:', error);
        return false;
      }
    },

    resetGame: () => {
      set(initialState);
    },
  }))
);

// Helper functions
function isPositionValid(
  position: Position,
  size: { width: number; height: number },
  state: GameState,
  excludeId?: string
): boolean {
  // Check bounds
  if (position.x < 0 || position.y < 0 ||
      position.x + size.width > state.gridSize.width ||
      position.y + size.height > state.gridSize.height) {
    return false;
  }

  // Check for overlaps with existing components
  for (const [id, component] of state.components) {
    if (excludeId && id === excludeId) continue;
    
    const compDef = COMPONENT_DEFINITIONS[component.type];
    const compPos = component.position;
    
    // Check if rectangles overlap
    if (!(position.x >= compPos.x + compDef.size.width ||
          position.x + size.width <= compPos.x ||
          position.y >= compPos.y + compDef.size.height ||
          position.y + size.height <= compPos.y)) {
      return false;
    }
  }

  return true;
}

// Auto-connect components based on adjacency and direction
function autoConnectComponent(
  newId: string,
  newComponent: GameComponent,
  components: Map<string, GameComponent>
): void {
  const newDef = COMPONENT_DEFINITIONS[newComponent.type];

  // Find adjacent components
  for (const [existingId, existingComponent] of components) {
    if (existingId === newId) continue;

    const existingDef = COMPONENT_DEFINITIONS[existingComponent.type];

    // Check if components are adjacent and can connect
    if (areComponentsAdjacent(newComponent, existingComponent)) {
      const connectionDirection = getConnectionDirection(newComponent, existingComponent);

      // Determine which component should be input/output based on direction and type
      const shouldConnect = shouldAutoConnect(
        newComponent, newDef,
        existingComponent, existingDef,
        connectionDirection
      );

      if (shouldConnect.connect) {
        if (shouldConnect.newIsOutput) {
          // New component outputs to existing component
          if (newComponent.connections.outputs.length < newDef.maxOutputs &&
              existingComponent.connections.inputs.length < existingDef.maxInputs) {
            newComponent.connections.outputs.push(existingId);
            existingComponent.connections.inputs.push(newId);
          }
        } else {
          // Existing component outputs to new component
          if (existingComponent.connections.outputs.length < existingDef.maxOutputs &&
              newComponent.connections.inputs.length < newDef.maxInputs) {
            existingComponent.connections.outputs.push(newId);
            newComponent.connections.inputs.push(existingId);
          }
        }
      }
    }
  }
}

// Check if two components are adjacent (within 1 grid unit)
function areComponentsAdjacent(comp1: GameComponent, comp2: GameComponent): boolean {
  const def1 = COMPONENT_DEFINITIONS[comp1.type];
  const def2 = COMPONENT_DEFINITIONS[comp2.type];

  // Calculate component bounds
  const comp1Bounds = {
    left: comp1.position.x,
    right: comp1.position.x + def1.size.width,
    top: comp1.position.y,
    bottom: comp1.position.y + def1.size.height,
  };

  const comp2Bounds = {
    left: comp2.position.x,
    right: comp2.position.x + def2.size.width,
    top: comp2.position.y,
    bottom: comp2.position.y + def2.size.height,
  };

  // Check if they're adjacent (touching but not overlapping)
  const horizontallyAdjacent =
    (comp1Bounds.right === comp2Bounds.left || comp2Bounds.right === comp1Bounds.left) &&
    !(comp1Bounds.bottom <= comp2Bounds.top || comp2Bounds.bottom <= comp1Bounds.top);

  const verticallyAdjacent =
    (comp1Bounds.bottom === comp2Bounds.top || comp2Bounds.bottom === comp1Bounds.top) &&
    !(comp1Bounds.right <= comp2Bounds.left || comp2Bounds.right <= comp1Bounds.left);

  return horizontallyAdjacent || verticallyAdjacent;
}

// Get the direction from comp1 to comp2
function getConnectionDirection(comp1: GameComponent, comp2: GameComponent): Direction {
  const dx = comp2.position.x - comp1.position.x;
  const dy = comp2.position.y - comp1.position.y;

  if (Math.abs(dx) > Math.abs(dy)) {
    return dx > 0 ? Direction.EAST : Direction.WEST;
  } else {
    return dy > 0 ? Direction.SOUTH : Direction.NORTH;
  }
}

// Determine if components should auto-connect and in which direction
function shouldAutoConnect(
  comp1: GameComponent, def1: ComponentDefinition,
  comp2: GameComponent, def2: ComponentDefinition,
  direction: Direction
): { connect: boolean; newIsOutput: boolean } {
  // Miners always output
  if (comp1.type === ComponentType.MINER && def2.maxInputs > 0) {
    return { connect: true, newIsOutput: true };
  }
  if (comp2.type === ComponentType.MINER && def1.maxInputs > 0) {
    return { connect: true, newIsOutput: false };
  }

  // Assemblers prefer to output to storage or conveyors
  if (comp1.type === ComponentType.ASSEMBLER &&
      (comp2.type === ComponentType.STORAGE || comp2.type === ComponentType.CONVEYOR)) {
    return { connect: true, newIsOutput: true };
  }
  if (comp2.type === ComponentType.ASSEMBLER &&
      (comp1.type === ComponentType.STORAGE || comp1.type === ComponentType.CONVEYOR)) {
    return { connect: true, newIsOutput: false };
  }

  // Conveyors connect in the direction they're facing
  if (comp1.type === ComponentType.CONVEYOR && comp1.direction === direction && def2.maxInputs > 0) {
    return { connect: true, newIsOutput: true };
  }
  if (comp2.type === ComponentType.CONVEYOR && comp2.direction === getOppositeDirection(direction) && def1.maxInputs > 0) {
    return { connect: true, newIsOutput: false };
  }

  // Default: don't auto-connect
  return { connect: false, newIsOutput: false };
}
