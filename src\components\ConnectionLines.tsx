'use client';

import React from 'react';
import { GameComponent, COMPONENT_DEFINITIONS } from '@/types/game';

interface ConnectionLinesProps {
  components: Map<string, GameComponent>;
  cellSize: number;
  viewOffset: { x: number; y: number };
}

const ConnectionLines: React.FC<ConnectionLinesProps> = ({ 
  components, 
  cellSize, 
  viewOffset 
}) => {
  const lines: JSX.Element[] = [];

  // Generate connection lines
  components.forEach((component, componentId) => {
    const definition = COMPONENT_DEFINITIONS[component.type];
    
    // Calculate component center
    const fromX = (component.position.x + definition.size.width / 2) * cellSize;
    const fromY = (component.position.y + definition.size.height / 2) * cellSize;

    // Draw lines to all connected outputs
    component.connections.outputs.forEach((outputId, index) => {
      const outputComponent = components.get(outputId);
      if (!outputComponent) return;

      const outputDef = COMPONENT_DEFINITIONS[outputComponent.type];
      const toX = (outputComponent.position.x + outputDef.size.width / 2) * cellSize;
      const toY = (outputComponent.position.y + outputDef.size.height / 2) * cellSize;

      // Create unique key for this connection
      const lineKey = `${componentId}-${outputId}`;
      
      // Determine line color based on activity
      const isActive = component.isActive && outputComponent.isActive;
      const lineColor = isActive ? '#10b981' : '#6b7280'; // green if active, gray if not
      const lineWidth = isActive ? 3 : 2;
      const opacity = isActive ? 1 : 0.6;

      lines.push(
        <g key={lineKey}>
          {/* Main connection line */}
          <line
            x1={fromX}
            y1={fromY}
            x2={toX}
            y2={toY}
            stroke={lineColor}
            strokeWidth={lineWidth}
            opacity={opacity}
            strokeDasharray={isActive ? undefined : "5,5"}
          />
          
          {/* Arrow head */}
          <polygon
            points={`${toX-8},${toY-4} ${toX},${toY} ${toX-8},${toY+4}`}
            fill={lineColor}
            opacity={opacity}
            transform={`rotate(${Math.atan2(toY - fromY, toX - fromX) * 180 / Math.PI}, ${toX}, ${toY})`}
          />
          
          {/* Flow animation for active connections */}
          {isActive && (
            <circle
              r="3"
              fill="#10b981"
              opacity="0.8"
            >
              <animateMotion
                dur="2s"
                repeatCount="indefinite"
                path={`M ${fromX} ${fromY} L ${toX} ${toY}`}
              />
            </circle>
          )}
        </g>
      );
    });
  });

  return (
    <svg
      className="absolute inset-0 pointer-events-none"
      style={{
        transform: `translate(${viewOffset.x}px, ${viewOffset.y}px)`,
        zIndex: 1,
      }}
    >
      {lines}
    </svg>
  );
};

export default ConnectionLines;
