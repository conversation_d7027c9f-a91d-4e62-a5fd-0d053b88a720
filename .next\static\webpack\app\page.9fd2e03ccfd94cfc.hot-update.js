"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/gameStore.ts":
/*!********************************!*\
  !*** ./src/store/gameStore.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGameStore: () => (/* binding */ useGameStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _types_game__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/game */ \"(app-pages-browser)/./src/types/game.ts\");\n/* harmony import */ var _utils_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/helpers */ \"(app-pages-browser)/./src/utils/helpers.ts\");\n/* harmony import */ var _engine_simulation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/engine/simulation */ \"(app-pages-browser)/./src/engine/simulation.ts\");\n/* harmony import */ var _analytics_performanceAnalyzer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/analytics/performanceAnalyzer */ \"(app-pages-browser)/./src/analytics/performanceAnalyzer.ts\");\n\n\n\n\n\n\nconst GRID_SIZE = {\n    width: 50,\n    height: 50\n};\nconst simulationEngine = new _engine_simulation__WEBPACK_IMPORTED_MODULE_2__.SimulationEngine();\nconst performanceAnalyzer = new _analytics_performanceAnalyzer__WEBPACK_IMPORTED_MODULE_3__.PerformanceAnalyzer();\nconst initialState = {\n    components: new Map(),\n    gridSize: GRID_SIZE,\n    isRunning: false,\n    gameTime: 0,\n    resources: new Map([\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.IRON_ORE,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COPPER_ORE,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COAL,\n            1000\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.IRON_PLATE,\n            100\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.COPPER_PLATE,\n            100\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.GEAR,\n            50\n        ],\n        [\n            _types_game__WEBPACK_IMPORTED_MODULE_0__.ResourceType.CIRCUIT,\n            50\n        ]\n    ]),\n    statistics: {\n        totalProduction: new Map(),\n        totalConsumption: new Map(),\n        efficiency: 0,\n        bottlenecks: []\n    }\n};\nconst initialStoreState = {\n    ...initialState,\n    factoryAnalytics: null\n};\nconst useGameStore = (0,zustand__WEBPACK_IMPORTED_MODULE_4__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_5__.subscribeWithSelector)((set, get)=>({\n        ...initialStoreState,\n        addComponent: function(type, position) {\n            let direction = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.NORTH;\n            const state = get();\n            const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[type];\n            // Check if position is valid and not occupied\n            if (!isPositionValid(position, definition.size, state)) {\n                return null;\n            }\n            const id = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_1__.generateId)();\n            const component = {\n                id,\n                type,\n                position,\n                direction,\n                inventory: new Map(),\n                connections: {\n                    inputs: [],\n                    outputs: []\n                },\n                isActive: false,\n                lastProcessTime: 0\n            };\n            set((state)=>{\n                const newComponents = new Map(state.components).set(id, component);\n                // Auto-connect to adjacent components\n                autoConnectComponent(id, component, newComponents);\n                return {\n                    components: newComponents\n                };\n            });\n            return id;\n        },\n        removeComponent: (id)=>{\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const component = newComponents.get(id);\n                if (component) {\n                    // Remove all connections to this component\n                    newComponents.forEach((comp)=>{\n                        comp.connections.inputs = comp.connections.inputs.filter((connId)=>connId !== id);\n                        comp.connections.outputs = comp.connections.outputs.filter((connId)=>connId !== id);\n                    });\n                    newComponents.delete(id);\n                }\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        moveComponent: (id, position)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const definition = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n                if (!isPositionValid(position, definition.size, state, id)) {\n                    return state;\n                }\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    position\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        rotateComponent: (id)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const newDirection = (component.direction + 1) % 4;\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    direction: newDirection\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        connectComponents: (fromId, toId)=>{\n            const state = get();\n            const fromComponent = state.components.get(fromId);\n            const toComponent = state.components.get(toId);\n            if (!fromComponent || !toComponent || fromId === toId) {\n                return false;\n            }\n            const fromDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[fromComponent.type];\n            const toDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[toComponent.type];\n            // Check connection limits\n            if (fromComponent.connections.outputs.length >= fromDef.maxOutputs || toComponent.connections.inputs.length >= toDef.maxInputs) {\n                return false;\n            }\n            // Check if already connected\n            if (fromComponent.connections.outputs.includes(toId)) {\n                return false;\n            }\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const newFromComponent = {\n                    ...fromComponent\n                };\n                const newToComponent = {\n                    ...toComponent\n                };\n                newFromComponent.connections.outputs.push(toId);\n                newToComponent.connections.inputs.push(fromId);\n                newComponents.set(fromId, newFromComponent);\n                newComponents.set(toId, newToComponent);\n                return {\n                    components: newComponents\n                };\n            });\n            return true;\n        },\n        disconnectComponents: (fromId, toId)=>{\n            set((state)=>{\n                const newComponents = new Map(state.components);\n                const fromComponent = newComponents.get(fromId);\n                const toComponent = newComponents.get(toId);\n                if (fromComponent && toComponent) {\n                    fromComponent.connections.outputs = fromComponent.connections.outputs.filter((id)=>id !== toId);\n                    toComponent.connections.inputs = toComponent.connections.inputs.filter((id)=>id !== fromId);\n                }\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        setComponentRecipe: (id, recipeId)=>{\n            set((state)=>{\n                const component = state.components.get(id);\n                if (!component) return state;\n                const recipe = _types_game__WEBPACK_IMPORTED_MODULE_0__.RECIPES[recipeId];\n                if (!recipe) return state;\n                const newComponents = new Map(state.components);\n                newComponents.set(id, {\n                    ...component,\n                    recipe\n                });\n                return {\n                    components: newComponents\n                };\n            });\n        },\n        toggleSimulation: ()=>{\n            set((state)=>({\n                    isRunning: !state.isRunning\n                }));\n        },\n        updateSimulation: ()=>{\n            const state = get();\n            if (!state.isRunning) return;\n            const updates = simulationEngine.updateSimulation(state);\n            if (Object.keys(updates).length > 0) {\n                const newState = {\n                    ...state,\n                    ...updates\n                };\n                const analytics = performanceAnalyzer.analyzeFactory(newState);\n                set((currentState)=>({\n                        ...currentState,\n                        ...updates,\n                        factoryAnalytics: analytics\n                    }));\n            }\n        },\n        getPerformanceMetrics: ()=>{\n            const state = get();\n            return {\n                throughput: state.statistics.totalProduction,\n                utilization: new Map(),\n                bottlenecks: state.statistics.bottlenecks,\n                efficiency: state.statistics.efficiency\n            };\n        },\n        getFactoryAnalytics: ()=>{\n            const state = get();\n            if (state.factoryAnalytics) {\n                return state.factoryAnalytics;\n            }\n            // Generate analytics on demand if not available\n            return performanceAnalyzer.analyzeFactory(state);\n        },\n        getHistoricalData: (key)=>{\n            return performanceAnalyzer.getHistoricalData(key);\n        },\n        exportGameState: ()=>{\n            const state = get();\n            const exportData = {\n                components: Array.from(state.components.entries()),\n                gridSize: state.gridSize,\n                gameTime: state.gameTime,\n                resources: Array.from(state.resources.entries())\n            };\n            return JSON.stringify(exportData, null, 2);\n        },\n        importGameState: (jsonState)=>{\n            try {\n                const data = JSON.parse(jsonState);\n                set({\n                    components: new Map(data.components),\n                    gridSize: data.gridSize || GRID_SIZE,\n                    gameTime: data.gameTime || 0,\n                    resources: new Map(data.resources),\n                    isRunning: false\n                });\n                return true;\n            } catch (error) {\n                console.error('Failed to import game state:', error);\n                return false;\n            }\n        },\n        resetGame: ()=>{\n            set(initialState);\n        }\n    })));\n// Helper functions\nfunction isPositionValid(position, size, state, excludeId) {\n    // Check bounds\n    if (position.x < 0 || position.y < 0 || position.x + size.width > state.gridSize.width || position.y + size.height > state.gridSize.height) {\n        return false;\n    }\n    // Check for overlaps with existing components\n    for (const [id, component] of state.components){\n        if (excludeId && id === excludeId) continue;\n        const compDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[component.type];\n        const compPos = component.position;\n        // Check if rectangles overlap\n        if (!(position.x >= compPos.x + compDef.size.width || position.x + size.width <= compPos.x || position.y >= compPos.y + compDef.size.height || position.y + size.height <= compPos.y)) {\n            return false;\n        }\n    }\n    return true;\n}\n// Auto-connect components based on adjacency and direction\nfunction autoConnectComponent(newId, newComponent, components) {\n    const newDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[newComponent.type];\n    // Find adjacent components\n    for (const [existingId, existingComponent] of components){\n        if (existingId === newId) continue;\n        const existingDef = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[existingComponent.type];\n        // Check if components are adjacent and can connect\n        if (areComponentsAdjacent(newComponent, existingComponent)) {\n            const connectionDirection = getConnectionDirection(newComponent, existingComponent);\n            // Determine which component should be input/output based on direction and type\n            const shouldConnect = shouldAutoConnect(newComponent, newDef, existingComponent, existingDef, connectionDirection);\n            if (shouldConnect.connect) {\n                if (shouldConnect.newIsOutput) {\n                    // New component outputs to existing component\n                    if (newComponent.connections.outputs.length < newDef.maxOutputs && existingComponent.connections.inputs.length < existingDef.maxInputs) {\n                        newComponent.connections.outputs.push(existingId);\n                        existingComponent.connections.inputs.push(newId);\n                    }\n                } else {\n                    // Existing component outputs to new component\n                    if (existingComponent.connections.outputs.length < existingDef.maxOutputs && newComponent.connections.inputs.length < newDef.maxInputs) {\n                        existingComponent.connections.outputs.push(newId);\n                        newComponent.connections.inputs.push(existingId);\n                    }\n                }\n            }\n        }\n    }\n}\n// Check if two components are adjacent (within 1 grid unit)\nfunction areComponentsAdjacent(comp1, comp2) {\n    const def1 = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[comp1.type];\n    const def2 = _types_game__WEBPACK_IMPORTED_MODULE_0__.COMPONENT_DEFINITIONS[comp2.type];\n    // Calculate component bounds\n    const comp1Bounds = {\n        left: comp1.position.x,\n        right: comp1.position.x + def1.size.width,\n        top: comp1.position.y,\n        bottom: comp1.position.y + def1.size.height\n    };\n    const comp2Bounds = {\n        left: comp2.position.x,\n        right: comp2.position.x + def2.size.width,\n        top: comp2.position.y,\n        bottom: comp2.position.y + def2.size.height\n    };\n    // Check if they're adjacent (touching but not overlapping)\n    const horizontallyAdjacent = (comp1Bounds.right === comp2Bounds.left || comp2Bounds.right === comp1Bounds.left) && !(comp1Bounds.bottom <= comp2Bounds.top || comp2Bounds.bottom <= comp1Bounds.top);\n    const verticallyAdjacent = (comp1Bounds.bottom === comp2Bounds.top || comp2Bounds.bottom === comp1Bounds.top) && !(comp1Bounds.right <= comp2Bounds.left || comp2Bounds.right <= comp1Bounds.left);\n    return horizontallyAdjacent || verticallyAdjacent;\n}\n// Get the direction from comp1 to comp2\nfunction getConnectionDirection(comp1, comp2) {\n    const dx = comp2.position.x - comp1.position.x;\n    const dy = comp2.position.y - comp1.position.y;\n    if (Math.abs(dx) > Math.abs(dy)) {\n        return dx > 0 ? _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.EAST : _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.WEST;\n    } else {\n        return dy > 0 ? _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.SOUTH : _types_game__WEBPACK_IMPORTED_MODULE_0__.Direction.NORTH;\n    }\n}\n// Determine if components should auto-connect and in which direction\nfunction shouldAutoConnect(comp1, def1, comp2, def2, direction) {\n    // Miners always output\n    if (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MINER && def2.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: true\n        };\n    }\n    if (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.MINER && def1.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: false\n        };\n    }\n    // Assemblers prefer to output to storage or conveyors\n    if (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.ASSEMBLER && (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.STORAGE || comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR)) {\n        return {\n            connect: true,\n            newIsOutput: true\n        };\n    }\n    if (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.ASSEMBLER && (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.STORAGE || comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR)) {\n        return {\n            connect: true,\n            newIsOutput: false\n        };\n    }\n    // Conveyors connect in the direction they're facing\n    if (comp1.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR && comp1.direction === direction && def2.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: true\n        };\n    }\n    if (comp2.type === _types_game__WEBPACK_IMPORTED_MODULE_0__.ComponentType.CONVEYOR && comp2.direction === (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_1__.getOppositeDirection)(direction) && def1.maxInputs > 0) {\n        return {\n            connect: true,\n            newIsOutput: false\n        };\n    }\n    // Default: don't auto-connect\n    return {\n        connect: false,\n        newIsOutput: false\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdG9yZS9nYW1lU3RvcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFpQztBQUMwQjtBQVdyQztBQUM2QztBQUNaO0FBQ2lDO0FBd0J4RixNQUFNVyxZQUFZO0lBQUVDLE9BQU87SUFBSUMsUUFBUTtBQUFHO0FBQzFDLE1BQU1DLG1CQUFtQixJQUFJTCxnRUFBZ0JBO0FBQzdDLE1BQU1NLHNCQUFzQixJQUFJTCwrRUFBbUJBO0FBRW5ELE1BQU1NLGVBQTBCO0lBQzlCQyxZQUFZLElBQUlDO0lBQ2hCQyxVQUFVUjtJQUNWUyxXQUFXO0lBQ1hDLFVBQVU7SUFDVkMsV0FBVyxJQUFJSixJQUFJO1FBQ2pCO1lBQUNkLHFEQUFZQSxDQUFDbUIsUUFBUTtZQUFFO1NBQUs7UUFDN0I7WUFBQ25CLHFEQUFZQSxDQUFDb0IsVUFBVTtZQUFFO1NBQUs7UUFDL0I7WUFBQ3BCLHFEQUFZQSxDQUFDcUIsSUFBSTtZQUFFO1NBQUs7UUFDekI7WUFBQ3JCLHFEQUFZQSxDQUFDc0IsVUFBVTtZQUFFO1NBQUk7UUFDOUI7WUFBQ3RCLHFEQUFZQSxDQUFDdUIsWUFBWTtZQUFFO1NBQUk7UUFDaEM7WUFBQ3ZCLHFEQUFZQSxDQUFDd0IsSUFBSTtZQUFFO1NBQUc7UUFDdkI7WUFBQ3hCLHFEQUFZQSxDQUFDeUIsT0FBTztZQUFFO1NBQUc7S0FDM0I7SUFDREMsWUFBWTtRQUNWQyxpQkFBaUIsSUFBSWI7UUFDckJjLGtCQUFrQixJQUFJZDtRQUN0QmUsWUFBWTtRQUNaQyxhQUFhLEVBQUU7SUFDakI7QUFDRjtBQUVBLE1BQU1DLG9CQUFvQjtJQUN4QixHQUFHbkIsWUFBWTtJQUNmb0Isa0JBQWtCO0FBQ3BCO0FBRU8sTUFBTUMsZUFBZXJDLCtDQUFNQSxHQUNoQ0MseUVBQXFCQSxDQUFDLENBQUNxQyxLQUFLQyxNQUFTO1FBQ25DLEdBQUdKLGlCQUFpQjtRQUVwQkssY0FBYyxTQUFDQyxNQUFxQkM7Z0JBQW9CQyw2RUFBWXhDLGtEQUFTQSxDQUFDeUMsS0FBSztZQUNqRixNQUFNQyxRQUFRTjtZQUNkLE1BQU1PLGFBQWF6Qyw4REFBcUIsQ0FBQ29DLEtBQUs7WUFFOUMsOENBQThDO1lBQzlDLElBQUksQ0FBQ00sZ0JBQWdCTCxVQUFVSSxXQUFXRSxJQUFJLEVBQUVILFFBQVE7Z0JBQ3RELE9BQU87WUFDVDtZQUVBLE1BQU1JLEtBQUsxQywwREFBVUE7WUFDckIsTUFBTTJDLFlBQTJCO2dCQUMvQkQ7Z0JBQ0FSO2dCQUNBQztnQkFDQUM7Z0JBQ0FRLFdBQVcsSUFBSWpDO2dCQUNma0MsYUFBYTtvQkFBRUMsUUFBUSxFQUFFO29CQUFFQyxTQUFTLEVBQUU7Z0JBQUM7Z0JBQ3ZDQyxVQUFVO2dCQUNWQyxpQkFBaUI7WUFDbkI7WUFFQWxCLElBQUksQ0FBQ087Z0JBQ0gsTUFBTVksZ0JBQWdCLElBQUl2QyxJQUFJMkIsTUFBTTVCLFVBQVUsRUFBRXFCLEdBQUcsQ0FBQ1csSUFBSUM7Z0JBRXhELHNDQUFzQztnQkFDdENRLHFCQUFxQlQsSUFBSUMsV0FBV087Z0JBRXBDLE9BQU87b0JBQUV4QyxZQUFZd0M7Z0JBQWM7WUFDckM7WUFFQSxPQUFPUjtRQUNUO1FBRUFVLGlCQUFpQixDQUFDVjtZQUNoQlgsSUFBSSxDQUFDTztnQkFDSCxNQUFNWSxnQkFBZ0IsSUFBSXZDLElBQUkyQixNQUFNNUIsVUFBVTtnQkFDOUMsTUFBTWlDLFlBQVlPLGNBQWNsQixHQUFHLENBQUNVO2dCQUVwQyxJQUFJQyxXQUFXO29CQUNiLDJDQUEyQztvQkFDM0NPLGNBQWNHLE9BQU8sQ0FBQyxDQUFDQzt3QkFDckJBLEtBQUtULFdBQVcsQ0FBQ0MsTUFBTSxHQUFHUSxLQUFLVCxXQUFXLENBQUNDLE1BQU0sQ0FBQ1MsTUFBTSxDQUFDQyxDQUFBQSxTQUFVQSxXQUFXZDt3QkFDOUVZLEtBQUtULFdBQVcsQ0FBQ0UsT0FBTyxHQUFHTyxLQUFLVCxXQUFXLENBQUNFLE9BQU8sQ0FBQ1EsTUFBTSxDQUFDQyxDQUFBQSxTQUFVQSxXQUFXZDtvQkFDbEY7b0JBRUFRLGNBQWNPLE1BQU0sQ0FBQ2Y7Z0JBQ3ZCO2dCQUVBLE9BQU87b0JBQUVoQyxZQUFZd0M7Z0JBQWM7WUFDckM7UUFDRjtRQUVBUSxlQUFlLENBQUNoQixJQUFZUDtZQUMxQkosSUFBSSxDQUFDTztnQkFDSCxNQUFNSyxZQUFZTCxNQUFNNUIsVUFBVSxDQUFDc0IsR0FBRyxDQUFDVTtnQkFDdkMsSUFBSSxDQUFDQyxXQUFXLE9BQU9MO2dCQUV2QixNQUFNQyxhQUFhekMsOERBQXFCLENBQUM2QyxVQUFVVCxJQUFJLENBQUM7Z0JBQ3hELElBQUksQ0FBQ00sZ0JBQWdCTCxVQUFVSSxXQUFXRSxJQUFJLEVBQUVILE9BQU9JLEtBQUs7b0JBQzFELE9BQU9KO2dCQUNUO2dCQUVBLE1BQU1ZLGdCQUFnQixJQUFJdkMsSUFBSTJCLE1BQU01QixVQUFVO2dCQUM5Q3dDLGNBQWNuQixHQUFHLENBQUNXLElBQUk7b0JBQUUsR0FBR0MsU0FBUztvQkFBRVI7Z0JBQVM7Z0JBQy9DLE9BQU87b0JBQUV6QixZQUFZd0M7Z0JBQWM7WUFDckM7UUFDRjtRQUVBUyxpQkFBaUIsQ0FBQ2pCO1lBQ2hCWCxJQUFJLENBQUNPO2dCQUNILE1BQU1LLFlBQVlMLE1BQU01QixVQUFVLENBQUNzQixHQUFHLENBQUNVO2dCQUN2QyxJQUFJLENBQUNDLFdBQVcsT0FBT0w7Z0JBRXZCLE1BQU1zQixlQUFlLENBQUNqQixVQUFVUCxTQUFTLEdBQUcsS0FBSztnQkFDakQsTUFBTWMsZ0JBQWdCLElBQUl2QyxJQUFJMkIsTUFBTTVCLFVBQVU7Z0JBQzlDd0MsY0FBY25CLEdBQUcsQ0FBQ1csSUFBSTtvQkFBRSxHQUFHQyxTQUFTO29CQUFFUCxXQUFXd0I7Z0JBQWE7Z0JBQzlELE9BQU87b0JBQUVsRCxZQUFZd0M7Z0JBQWM7WUFDckM7UUFDRjtRQUVBVyxtQkFBbUIsQ0FBQ0MsUUFBZ0JDO1lBQ2xDLE1BQU16QixRQUFRTjtZQUNkLE1BQU1nQyxnQkFBZ0IxQixNQUFNNUIsVUFBVSxDQUFDc0IsR0FBRyxDQUFDOEI7WUFDM0MsTUFBTUcsY0FBYzNCLE1BQU01QixVQUFVLENBQUNzQixHQUFHLENBQUMrQjtZQUV6QyxJQUFJLENBQUNDLGlCQUFpQixDQUFDQyxlQUFlSCxXQUFXQyxNQUFNO2dCQUNyRCxPQUFPO1lBQ1Q7WUFFQSxNQUFNRyxVQUFVcEUsOERBQXFCLENBQUNrRSxjQUFjOUIsSUFBSSxDQUFDO1lBQ3pELE1BQU1pQyxRQUFRckUsOERBQXFCLENBQUNtRSxZQUFZL0IsSUFBSSxDQUFDO1lBRXJELDBCQUEwQjtZQUMxQixJQUFJOEIsY0FBY25CLFdBQVcsQ0FBQ0UsT0FBTyxDQUFDcUIsTUFBTSxJQUFJRixRQUFRRyxVQUFVLElBQzlESixZQUFZcEIsV0FBVyxDQUFDQyxNQUFNLENBQUNzQixNQUFNLElBQUlELE1BQU1HLFNBQVMsRUFBRTtnQkFDNUQsT0FBTztZQUNUO1lBRUEsNkJBQTZCO1lBQzdCLElBQUlOLGNBQWNuQixXQUFXLENBQUNFLE9BQU8sQ0FBQ3dCLFFBQVEsQ0FBQ1IsT0FBTztnQkFDcEQsT0FBTztZQUNUO1lBRUFoQyxJQUFJLENBQUNPO2dCQUNILE1BQU1ZLGdCQUFnQixJQUFJdkMsSUFBSTJCLE1BQU01QixVQUFVO2dCQUM5QyxNQUFNOEQsbUJBQW1CO29CQUFFLEdBQUdSLGFBQWE7Z0JBQUM7Z0JBQzVDLE1BQU1TLGlCQUFpQjtvQkFBRSxHQUFHUixXQUFXO2dCQUFDO2dCQUV4Q08saUJBQWlCM0IsV0FBVyxDQUFDRSxPQUFPLENBQUMyQixJQUFJLENBQUNYO2dCQUMxQ1UsZUFBZTVCLFdBQVcsQ0FBQ0MsTUFBTSxDQUFDNEIsSUFBSSxDQUFDWjtnQkFFdkNaLGNBQWNuQixHQUFHLENBQUMrQixRQUFRVTtnQkFDMUJ0QixjQUFjbkIsR0FBRyxDQUFDZ0MsTUFBTVU7Z0JBRXhCLE9BQU87b0JBQUUvRCxZQUFZd0M7Z0JBQWM7WUFDckM7WUFFQSxPQUFPO1FBQ1Q7UUFFQXlCLHNCQUFzQixDQUFDYixRQUFnQkM7WUFDckNoQyxJQUFJLENBQUNPO2dCQUNILE1BQU1ZLGdCQUFnQixJQUFJdkMsSUFBSTJCLE1BQU01QixVQUFVO2dCQUM5QyxNQUFNc0QsZ0JBQWdCZCxjQUFjbEIsR0FBRyxDQUFDOEI7Z0JBQ3hDLE1BQU1HLGNBQWNmLGNBQWNsQixHQUFHLENBQUMrQjtnQkFFdEMsSUFBSUMsaUJBQWlCQyxhQUFhO29CQUNoQ0QsY0FBY25CLFdBQVcsQ0FBQ0UsT0FBTyxHQUFHaUIsY0FBY25CLFdBQVcsQ0FBQ0UsT0FBTyxDQUFDUSxNQUFNLENBQUNiLENBQUFBLEtBQU1BLE9BQU9xQjtvQkFDMUZFLFlBQVlwQixXQUFXLENBQUNDLE1BQU0sR0FBR21CLFlBQVlwQixXQUFXLENBQUNDLE1BQU0sQ0FBQ1MsTUFBTSxDQUFDYixDQUFBQSxLQUFNQSxPQUFPb0I7Z0JBQ3RGO2dCQUVBLE9BQU87b0JBQUVwRCxZQUFZd0M7Z0JBQWM7WUFDckM7UUFDRjtRQUVBMEIsb0JBQW9CLENBQUNsQyxJQUFZbUM7WUFDL0I5QyxJQUFJLENBQUNPO2dCQUNILE1BQU1LLFlBQVlMLE1BQU01QixVQUFVLENBQUNzQixHQUFHLENBQUNVO2dCQUN2QyxJQUFJLENBQUNDLFdBQVcsT0FBT0w7Z0JBRXZCLE1BQU13QyxTQUFTL0UsZ0RBQU8sQ0FBQzhFLFNBQVM7Z0JBQ2hDLElBQUksQ0FBQ0MsUUFBUSxPQUFPeEM7Z0JBRXBCLE1BQU1ZLGdCQUFnQixJQUFJdkMsSUFBSTJCLE1BQU01QixVQUFVO2dCQUM5Q3dDLGNBQWNuQixHQUFHLENBQUNXLElBQUk7b0JBQUUsR0FBR0MsU0FBUztvQkFBRW1DO2dCQUFPO2dCQUM3QyxPQUFPO29CQUFFcEUsWUFBWXdDO2dCQUFjO1lBQ3JDO1FBQ0Y7UUFFQTZCLGtCQUFrQjtZQUNoQmhELElBQUksQ0FBQ08sUUFBVztvQkFBRXpCLFdBQVcsQ0FBQ3lCLE1BQU16QixTQUFTO2dCQUFDO1FBQ2hEO1FBRUFtRSxrQkFBa0I7WUFDaEIsTUFBTTFDLFFBQVFOO1lBQ2QsSUFBSSxDQUFDTSxNQUFNekIsU0FBUyxFQUFFO1lBRXRCLE1BQU1vRSxVQUFVMUUsaUJBQWlCeUUsZ0JBQWdCLENBQUMxQztZQUNsRCxJQUFJNEMsT0FBT0MsSUFBSSxDQUFDRixTQUFTYixNQUFNLEdBQUcsR0FBRztnQkFDbkMsTUFBTWdCLFdBQVc7b0JBQUUsR0FBRzlDLEtBQUs7b0JBQUUsR0FBRzJDLE9BQU87Z0JBQUM7Z0JBQ3hDLE1BQU1JLFlBQVk3RSxvQkFBb0I4RSxjQUFjLENBQUNGO2dCQUNyRHJELElBQUksQ0FBQ3dELGVBQWtCO3dCQUNyQixHQUFHQSxZQUFZO3dCQUNmLEdBQUdOLE9BQU87d0JBQ1ZwRCxrQkFBa0J3RDtvQkFDcEI7WUFDRjtRQUNGO1FBRUFHLHVCQUF1QjtZQUNyQixNQUFNbEQsUUFBUU47WUFDZCxPQUFPO2dCQUNMeUQsWUFBWW5ELE1BQU1mLFVBQVUsQ0FBQ0MsZUFBZTtnQkFDNUNrRSxhQUFhLElBQUkvRTtnQkFDakJnQixhQUFhVyxNQUFNZixVQUFVLENBQUNJLFdBQVc7Z0JBQ3pDRCxZQUFZWSxNQUFNZixVQUFVLENBQUNHLFVBQVU7WUFDekM7UUFDRjtRQUVBaUUscUJBQXFCO1lBQ25CLE1BQU1yRCxRQUFRTjtZQUNkLElBQUlNLE1BQU1ULGdCQUFnQixFQUFFO2dCQUMxQixPQUFPUyxNQUFNVCxnQkFBZ0I7WUFDL0I7WUFDQSxnREFBZ0Q7WUFDaEQsT0FBT3JCLG9CQUFvQjhFLGNBQWMsQ0FBQ2hEO1FBQzVDO1FBRUFzRCxtQkFBbUIsQ0FBQ0M7WUFDbEIsT0FBT3JGLG9CQUFvQm9GLGlCQUFpQixDQUFDQztRQUMvQztRQUVBQyxpQkFBaUI7WUFDZixNQUFNeEQsUUFBUU47WUFDZCxNQUFNK0QsYUFBYTtnQkFDakJyRixZQUFZc0YsTUFBTUMsSUFBSSxDQUFDM0QsTUFBTTVCLFVBQVUsQ0FBQ3dGLE9BQU87Z0JBQy9DdEYsVUFBVTBCLE1BQU0xQixRQUFRO2dCQUN4QkUsVUFBVXdCLE1BQU14QixRQUFRO2dCQUN4QkMsV0FBV2lGLE1BQU1DLElBQUksQ0FBQzNELE1BQU12QixTQUFTLENBQUNtRixPQUFPO1lBQy9DO1lBQ0EsT0FBT0MsS0FBS0MsU0FBUyxDQUFDTCxZQUFZLE1BQU07UUFDMUM7UUFFQU0saUJBQWlCLENBQUNDO1lBQ2hCLElBQUk7Z0JBQ0YsTUFBTUMsT0FBT0osS0FBS0ssS0FBSyxDQUFDRjtnQkFDeEJ2RSxJQUFJO29CQUNGckIsWUFBWSxJQUFJQyxJQUFJNEYsS0FBSzdGLFVBQVU7b0JBQ25DRSxVQUFVMkYsS0FBSzNGLFFBQVEsSUFBSVI7b0JBQzNCVSxVQUFVeUYsS0FBS3pGLFFBQVEsSUFBSTtvQkFDM0JDLFdBQVcsSUFBSUosSUFBSTRGLEtBQUt4RixTQUFTO29CQUNqQ0YsV0FBVztnQkFDYjtnQkFDQSxPQUFPO1lBQ1QsRUFBRSxPQUFPNEYsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7Z0JBQzlDLE9BQU87WUFDVDtRQUNGO1FBRUFFLFdBQVc7WUFDVDVFLElBQUl0QjtRQUNOO0lBQ0YsS0FDQTtBQUVGLG1CQUFtQjtBQUNuQixTQUFTK0IsZ0JBQ1BMLFFBQWtCLEVBQ2xCTSxJQUF1QyxFQUN2Q0gsS0FBZ0IsRUFDaEJzRSxTQUFrQjtJQUVsQixlQUFlO0lBQ2YsSUFBSXpFLFNBQVMwRSxDQUFDLEdBQUcsS0FBSzFFLFNBQVMyRSxDQUFDLEdBQUcsS0FDL0IzRSxTQUFTMEUsQ0FBQyxHQUFHcEUsS0FBS3BDLEtBQUssR0FBR2lDLE1BQU0xQixRQUFRLENBQUNQLEtBQUssSUFDOUM4QixTQUFTMkUsQ0FBQyxHQUFHckUsS0FBS25DLE1BQU0sR0FBR2dDLE1BQU0xQixRQUFRLENBQUNOLE1BQU0sRUFBRTtRQUNwRCxPQUFPO0lBQ1Q7SUFFQSw4Q0FBOEM7SUFDOUMsS0FBSyxNQUFNLENBQUNvQyxJQUFJQyxVQUFVLElBQUlMLE1BQU01QixVQUFVLENBQUU7UUFDOUMsSUFBSWtHLGFBQWFsRSxPQUFPa0UsV0FBVztRQUVuQyxNQUFNRyxVQUFVakgsOERBQXFCLENBQUM2QyxVQUFVVCxJQUFJLENBQUM7UUFDckQsTUFBTThFLFVBQVVyRSxVQUFVUixRQUFRO1FBRWxDLDhCQUE4QjtRQUM5QixJQUFJLENBQUVBLENBQUFBLFNBQVMwRSxDQUFDLElBQUlHLFFBQVFILENBQUMsR0FBR0UsUUFBUXRFLElBQUksQ0FBQ3BDLEtBQUssSUFDNUM4QixTQUFTMEUsQ0FBQyxHQUFHcEUsS0FBS3BDLEtBQUssSUFBSTJHLFFBQVFILENBQUMsSUFDcEMxRSxTQUFTMkUsQ0FBQyxJQUFJRSxRQUFRRixDQUFDLEdBQUdDLFFBQVF0RSxJQUFJLENBQUNuQyxNQUFNLElBQzdDNkIsU0FBUzJFLENBQUMsR0FBR3JFLEtBQUtuQyxNQUFNLElBQUkwRyxRQUFRRixDQUFDLEdBQUc7WUFDNUMsT0FBTztRQUNUO0lBQ0Y7SUFFQSxPQUFPO0FBQ1Q7QUFFQSwyREFBMkQ7QUFDM0QsU0FBUzNELHFCQUNQOEQsS0FBYSxFQUNiQyxZQUEyQixFQUMzQnhHLFVBQXNDO0lBRXRDLE1BQU15RyxTQUFTckgsOERBQXFCLENBQUNvSCxhQUFhaEYsSUFBSSxDQUFDO0lBRXZELDJCQUEyQjtJQUMzQixLQUFLLE1BQU0sQ0FBQ2tGLFlBQVlDLGtCQUFrQixJQUFJM0csV0FBWTtRQUN4RCxJQUFJMEcsZUFBZUgsT0FBTztRQUUxQixNQUFNSyxjQUFjeEgsOERBQXFCLENBQUN1SCxrQkFBa0JuRixJQUFJLENBQUM7UUFFakUsbURBQW1EO1FBQ25ELElBQUlxRixzQkFBc0JMLGNBQWNHLG9CQUFvQjtZQUMxRCxNQUFNRyxzQkFBc0JDLHVCQUF1QlAsY0FBY0c7WUFFakUsK0VBQStFO1lBQy9FLE1BQU1LLGdCQUFnQkMsa0JBQ3BCVCxjQUFjQyxRQUNkRSxtQkFBbUJDLGFBQ25CRTtZQUdGLElBQUlFLGNBQWNFLE9BQU8sRUFBRTtnQkFDekIsSUFBSUYsY0FBY0csV0FBVyxFQUFFO29CQUM3Qiw4Q0FBOEM7b0JBQzlDLElBQUlYLGFBQWFyRSxXQUFXLENBQUNFLE9BQU8sQ0FBQ3FCLE1BQU0sR0FBRytDLE9BQU85QyxVQUFVLElBQzNEZ0Qsa0JBQWtCeEUsV0FBVyxDQUFDQyxNQUFNLENBQUNzQixNQUFNLEdBQUdrRCxZQUFZaEQsU0FBUyxFQUFFO3dCQUN2RTRDLGFBQWFyRSxXQUFXLENBQUNFLE9BQU8sQ0FBQzJCLElBQUksQ0FBQzBDO3dCQUN0Q0Msa0JBQWtCeEUsV0FBVyxDQUFDQyxNQUFNLENBQUM0QixJQUFJLENBQUN1QztvQkFDNUM7Z0JBQ0YsT0FBTztvQkFDTCw4Q0FBOEM7b0JBQzlDLElBQUlJLGtCQUFrQnhFLFdBQVcsQ0FBQ0UsT0FBTyxDQUFDcUIsTUFBTSxHQUFHa0QsWUFBWWpELFVBQVUsSUFDckU2QyxhQUFhckUsV0FBVyxDQUFDQyxNQUFNLENBQUNzQixNQUFNLEdBQUcrQyxPQUFPN0MsU0FBUyxFQUFFO3dCQUM3RCtDLGtCQUFrQnhFLFdBQVcsQ0FBQ0UsT0FBTyxDQUFDMkIsSUFBSSxDQUFDdUM7d0JBQzNDQyxhQUFhckUsV0FBVyxDQUFDQyxNQUFNLENBQUM0QixJQUFJLENBQUMwQztvQkFDdkM7Z0JBQ0Y7WUFDRjtRQUNGO0lBQ0Y7QUFDRjtBQUVBLDREQUE0RDtBQUM1RCxTQUFTRyxzQkFBc0JPLEtBQW9CLEVBQUVDLEtBQW9CO0lBQ3ZFLE1BQU1DLE9BQU9sSSw4REFBcUIsQ0FBQ2dJLE1BQU01RixJQUFJLENBQUM7SUFDOUMsTUFBTStGLE9BQU9uSSw4REFBcUIsQ0FBQ2lJLE1BQU03RixJQUFJLENBQUM7SUFFOUMsNkJBQTZCO0lBQzdCLE1BQU1nRyxjQUFjO1FBQ2xCQyxNQUFNTCxNQUFNM0YsUUFBUSxDQUFDMEUsQ0FBQztRQUN0QnVCLE9BQU9OLE1BQU0zRixRQUFRLENBQUMwRSxDQUFDLEdBQUdtQixLQUFLdkYsSUFBSSxDQUFDcEMsS0FBSztRQUN6Q2dJLEtBQUtQLE1BQU0zRixRQUFRLENBQUMyRSxDQUFDO1FBQ3JCd0IsUUFBUVIsTUFBTTNGLFFBQVEsQ0FBQzJFLENBQUMsR0FBR2tCLEtBQUt2RixJQUFJLENBQUNuQyxNQUFNO0lBQzdDO0lBRUEsTUFBTWlJLGNBQWM7UUFDbEJKLE1BQU1KLE1BQU01RixRQUFRLENBQUMwRSxDQUFDO1FBQ3RCdUIsT0FBT0wsTUFBTTVGLFFBQVEsQ0FBQzBFLENBQUMsR0FBR29CLEtBQUt4RixJQUFJLENBQUNwQyxLQUFLO1FBQ3pDZ0ksS0FBS04sTUFBTTVGLFFBQVEsQ0FBQzJFLENBQUM7UUFDckJ3QixRQUFRUCxNQUFNNUYsUUFBUSxDQUFDMkUsQ0FBQyxHQUFHbUIsS0FBS3hGLElBQUksQ0FBQ25DLE1BQU07SUFDN0M7SUFFQSwyREFBMkQ7SUFDM0QsTUFBTWtJLHVCQUNKLENBQUNOLFlBQVlFLEtBQUssS0FBS0csWUFBWUosSUFBSSxJQUFJSSxZQUFZSCxLQUFLLEtBQUtGLFlBQVlDLElBQUksS0FDakYsQ0FBRUQsQ0FBQUEsWUFBWUksTUFBTSxJQUFJQyxZQUFZRixHQUFHLElBQUlFLFlBQVlELE1BQU0sSUFBSUosWUFBWUcsR0FBRztJQUVsRixNQUFNSSxxQkFDSixDQUFDUCxZQUFZSSxNQUFNLEtBQUtDLFlBQVlGLEdBQUcsSUFBSUUsWUFBWUQsTUFBTSxLQUFLSixZQUFZRyxHQUFHLEtBQ2pGLENBQUVILENBQUFBLFlBQVlFLEtBQUssSUFBSUcsWUFBWUosSUFBSSxJQUFJSSxZQUFZSCxLQUFLLElBQUlGLFlBQVlDLElBQUk7SUFFbEYsT0FBT0ssd0JBQXdCQztBQUNqQztBQUVBLHdDQUF3QztBQUN4QyxTQUFTaEIsdUJBQXVCSyxLQUFvQixFQUFFQyxLQUFvQjtJQUN4RSxNQUFNVyxLQUFLWCxNQUFNNUYsUUFBUSxDQUFDMEUsQ0FBQyxHQUFHaUIsTUFBTTNGLFFBQVEsQ0FBQzBFLENBQUM7SUFDOUMsTUFBTThCLEtBQUtaLE1BQU01RixRQUFRLENBQUMyRSxDQUFDLEdBQUdnQixNQUFNM0YsUUFBUSxDQUFDMkUsQ0FBQztJQUU5QyxJQUFJOEIsS0FBS0MsR0FBRyxDQUFDSCxNQUFNRSxLQUFLQyxHQUFHLENBQUNGLEtBQUs7UUFDL0IsT0FBT0QsS0FBSyxJQUFJOUksa0RBQVNBLENBQUNrSixJQUFJLEdBQUdsSixrREFBU0EsQ0FBQ21KLElBQUk7SUFDakQsT0FBTztRQUNMLE9BQU9KLEtBQUssSUFBSS9JLGtEQUFTQSxDQUFDb0osS0FBSyxHQUFHcEosa0RBQVNBLENBQUN5QyxLQUFLO0lBQ25EO0FBQ0Y7QUFFQSxxRUFBcUU7QUFDckUsU0FBU3NGLGtCQUNQRyxLQUFvQixFQUFFRSxJQUF5QixFQUMvQ0QsS0FBb0IsRUFBRUUsSUFBeUIsRUFDL0M3RixTQUFvQjtJQUVwQix1QkFBdUI7SUFDdkIsSUFBSTBGLE1BQU01RixJQUFJLEtBQUt2QyxzREFBYUEsQ0FBQ3NKLEtBQUssSUFBSWhCLEtBQUszRCxTQUFTLEdBQUcsR0FBRztRQUM1RCxPQUFPO1lBQUVzRCxTQUFTO1lBQU1DLGFBQWE7UUFBSztJQUM1QztJQUNBLElBQUlFLE1BQU03RixJQUFJLEtBQUt2QyxzREFBYUEsQ0FBQ3NKLEtBQUssSUFBSWpCLEtBQUsxRCxTQUFTLEdBQUcsR0FBRztRQUM1RCxPQUFPO1lBQUVzRCxTQUFTO1lBQU1DLGFBQWE7UUFBTTtJQUM3QztJQUVBLHNEQUFzRDtJQUN0RCxJQUFJQyxNQUFNNUYsSUFBSSxLQUFLdkMsc0RBQWFBLENBQUN1SixTQUFTLElBQ3JDbkIsQ0FBQUEsTUFBTTdGLElBQUksS0FBS3ZDLHNEQUFhQSxDQUFDd0osT0FBTyxJQUFJcEIsTUFBTTdGLElBQUksS0FBS3ZDLHNEQUFhQSxDQUFDeUosUUFBUSxHQUFHO1FBQ25GLE9BQU87WUFBRXhCLFNBQVM7WUFBTUMsYUFBYTtRQUFLO0lBQzVDO0lBQ0EsSUFBSUUsTUFBTTdGLElBQUksS0FBS3ZDLHNEQUFhQSxDQUFDdUosU0FBUyxJQUNyQ3BCLENBQUFBLE1BQU01RixJQUFJLEtBQUt2QyxzREFBYUEsQ0FBQ3dKLE9BQU8sSUFBSXJCLE1BQU01RixJQUFJLEtBQUt2QyxzREFBYUEsQ0FBQ3lKLFFBQVEsR0FBRztRQUNuRixPQUFPO1lBQUV4QixTQUFTO1lBQU1DLGFBQWE7UUFBTTtJQUM3QztJQUVBLG9EQUFvRDtJQUNwRCxJQUFJQyxNQUFNNUYsSUFBSSxLQUFLdkMsc0RBQWFBLENBQUN5SixRQUFRLElBQUl0QixNQUFNMUYsU0FBUyxLQUFLQSxhQUFhNkYsS0FBSzNELFNBQVMsR0FBRyxHQUFHO1FBQ2hHLE9BQU87WUFBRXNELFNBQVM7WUFBTUMsYUFBYTtRQUFLO0lBQzVDO0lBQ0EsSUFBSUUsTUFBTTdGLElBQUksS0FBS3ZDLHNEQUFhQSxDQUFDeUosUUFBUSxJQUFJckIsTUFBTTNGLFNBQVMsS0FBS25DLG9FQUFvQkEsQ0FBQ21DLGNBQWM0RixLQUFLMUQsU0FBUyxHQUFHLEdBQUc7UUFDdEgsT0FBTztZQUFFc0QsU0FBUztZQUFNQyxhQUFhO1FBQU07SUFDN0M7SUFFQSw4QkFBOEI7SUFDOUIsT0FBTztRQUFFRCxTQUFTO1FBQU9DLGFBQWE7SUFBTTtBQUM5QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb3dubG9hZHNcXGZhY3RvcnlnYW1lXFxzcmNcXHN0b3JlXFxnYW1lU3RvcmUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlIH0gZnJvbSAnenVzdGFuZCc7XG5pbXBvcnQgeyBzdWJzY3JpYmVXaXRoU2VsZWN0b3IgfSBmcm9tICd6dXN0YW5kL21pZGRsZXdhcmUnO1xuaW1wb3J0IHtcbiAgR2FtZVN0YXRlLFxuICBHYW1lQ29tcG9uZW50LFxuICBDb21wb25lbnRUeXBlLFxuICBQb3NpdGlvbixcbiAgRGlyZWN0aW9uLFxuICBSZXNvdXJjZVR5cGUsXG4gIFBlcmZvcm1hbmNlTWV0cmljcyxcbiAgQ09NUE9ORU5UX0RFRklOSVRJT05TLFxuICBSRUNJUEVTLFxufSBmcm9tICdAL3R5cGVzL2dhbWUnO1xuaW1wb3J0IHsgZ2VuZXJhdGVJZCwgZ2V0T3Bwb3NpdGVEaXJlY3Rpb24gfSBmcm9tICdAL3V0aWxzL2hlbHBlcnMnO1xuaW1wb3J0IHsgU2ltdWxhdGlvbkVuZ2luZSB9IGZyb20gJ0AvZW5naW5lL3NpbXVsYXRpb24nO1xuaW1wb3J0IHsgUGVyZm9ybWFuY2VBbmFseXplciwgRmFjdG9yeUFuYWx5dGljcyB9IGZyb20gJ0AvYW5hbHl0aWNzL3BlcmZvcm1hbmNlQW5hbHl6ZXInO1xuXG5pbnRlcmZhY2UgR2FtZVN0b3JlIGV4dGVuZHMgR2FtZVN0YXRlIHtcbiAgLy8gQW5hbHl0aWNzXG4gIGZhY3RvcnlBbmFseXRpY3M6IEZhY3RvcnlBbmFseXRpY3MgfCBudWxsO1xuXG4gIC8vIEFjdGlvbnNcbiAgYWRkQ29tcG9uZW50OiAodHlwZTogQ29tcG9uZW50VHlwZSwgcG9zaXRpb246IFBvc2l0aW9uLCBkaXJlY3Rpb24/OiBEaXJlY3Rpb24pID0+IHN0cmluZyB8IG51bGw7XG4gIHJlbW92ZUNvbXBvbmVudDogKGlkOiBzdHJpbmcpID0+IHZvaWQ7XG4gIG1vdmVDb21wb25lbnQ6IChpZDogc3RyaW5nLCBwb3NpdGlvbjogUG9zaXRpb24pID0+IHZvaWQ7XG4gIHJvdGF0ZUNvbXBvbmVudDogKGlkOiBzdHJpbmcpID0+IHZvaWQ7XG4gIGNvbm5lY3RDb21wb25lbnRzOiAoZnJvbUlkOiBzdHJpbmcsIHRvSWQ6IHN0cmluZykgPT4gYm9vbGVhbjtcbiAgZGlzY29ubmVjdENvbXBvbmVudHM6IChmcm9tSWQ6IHN0cmluZywgdG9JZDogc3RyaW5nKSA9PiB2b2lkO1xuICBzZXRDb21wb25lbnRSZWNpcGU6IChpZDogc3RyaW5nLCByZWNpcGVJZDogc3RyaW5nKSA9PiB2b2lkO1xuICB0b2dnbGVTaW11bGF0aW9uOiAoKSA9PiB2b2lkO1xuICB1cGRhdGVTaW11bGF0aW9uOiAoKSA9PiB2b2lkO1xuICBnZXRQZXJmb3JtYW5jZU1ldHJpY3M6ICgpID0+IFBlcmZvcm1hbmNlTWV0cmljcztcbiAgZ2V0RmFjdG9yeUFuYWx5dGljczogKCkgPT4gRmFjdG9yeUFuYWx5dGljcztcbiAgZ2V0SGlzdG9yaWNhbERhdGE6IChrZXk6IHN0cmluZykgPT4gbnVtYmVyW107XG4gIGV4cG9ydEdhbWVTdGF0ZTogKCkgPT4gc3RyaW5nO1xuICBpbXBvcnRHYW1lU3RhdGU6IChqc29uU3RhdGU6IHN0cmluZykgPT4gYm9vbGVhbjtcbiAgcmVzZXRHYW1lOiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBHUklEX1NJWkUgPSB7IHdpZHRoOiA1MCwgaGVpZ2h0OiA1MCB9O1xuY29uc3Qgc2ltdWxhdGlvbkVuZ2luZSA9IG5ldyBTaW11bGF0aW9uRW5naW5lKCk7XG5jb25zdCBwZXJmb3JtYW5jZUFuYWx5emVyID0gbmV3IFBlcmZvcm1hbmNlQW5hbHl6ZXIoKTtcblxuY29uc3QgaW5pdGlhbFN0YXRlOiBHYW1lU3RhdGUgPSB7XG4gIGNvbXBvbmVudHM6IG5ldyBNYXAoKSxcbiAgZ3JpZFNpemU6IEdSSURfU0laRSxcbiAgaXNSdW5uaW5nOiBmYWxzZSxcbiAgZ2FtZVRpbWU6IDAsXG4gIHJlc291cmNlczogbmV3IE1hcChbXG4gICAgW1Jlc291cmNlVHlwZS5JUk9OX09SRSwgMTAwMF0sXG4gICAgW1Jlc291cmNlVHlwZS5DT1BQRVJfT1JFLCAxMDAwXSxcbiAgICBbUmVzb3VyY2VUeXBlLkNPQUwsIDEwMDBdLFxuICAgIFtSZXNvdXJjZVR5cGUuSVJPTl9QTEFURSwgMTAwXSxcbiAgICBbUmVzb3VyY2VUeXBlLkNPUFBFUl9QTEFURSwgMTAwXSxcbiAgICBbUmVzb3VyY2VUeXBlLkdFQVIsIDUwXSxcbiAgICBbUmVzb3VyY2VUeXBlLkNJUkNVSVQsIDUwXSxcbiAgXSksXG4gIHN0YXRpc3RpY3M6IHtcbiAgICB0b3RhbFByb2R1Y3Rpb246IG5ldyBNYXAoKSxcbiAgICB0b3RhbENvbnN1bXB0aW9uOiBuZXcgTWFwKCksXG4gICAgZWZmaWNpZW5jeTogMCxcbiAgICBib3R0bGVuZWNrczogW10sXG4gIH0sXG59O1xuXG5jb25zdCBpbml0aWFsU3RvcmVTdGF0ZSA9IHtcbiAgLi4uaW5pdGlhbFN0YXRlLFxuICBmYWN0b3J5QW5hbHl0aWNzOiBudWxsIGFzIEZhY3RvcnlBbmFseXRpY3MgfCBudWxsLFxufTtcblxuZXhwb3J0IGNvbnN0IHVzZUdhbWVTdG9yZSA9IGNyZWF0ZTxHYW1lU3RvcmU+KCkoXG4gIHN1YnNjcmliZVdpdGhTZWxlY3Rvcigoc2V0LCBnZXQpID0+ICh7XG4gICAgLi4uaW5pdGlhbFN0b3JlU3RhdGUsXG5cbiAgICBhZGRDb21wb25lbnQ6ICh0eXBlOiBDb21wb25lbnRUeXBlLCBwb3NpdGlvbjogUG9zaXRpb24sIGRpcmVjdGlvbiA9IERpcmVjdGlvbi5OT1JUSCkgPT4ge1xuICAgICAgY29uc3Qgc3RhdGUgPSBnZXQoKTtcbiAgICAgIGNvbnN0IGRlZmluaXRpb24gPSBDT01QT05FTlRfREVGSU5JVElPTlNbdHlwZV07XG5cbiAgICAgIC8vIENoZWNrIGlmIHBvc2l0aW9uIGlzIHZhbGlkIGFuZCBub3Qgb2NjdXBpZWRcbiAgICAgIGlmICghaXNQb3NpdGlvblZhbGlkKHBvc2l0aW9uLCBkZWZpbml0aW9uLnNpemUsIHN0YXRlKSkge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cblxuICAgICAgY29uc3QgaWQgPSBnZW5lcmF0ZUlkKCk7XG4gICAgICBjb25zdCBjb21wb25lbnQ6IEdhbWVDb21wb25lbnQgPSB7XG4gICAgICAgIGlkLFxuICAgICAgICB0eXBlLFxuICAgICAgICBwb3NpdGlvbixcbiAgICAgICAgZGlyZWN0aW9uLFxuICAgICAgICBpbnZlbnRvcnk6IG5ldyBNYXAoKSxcbiAgICAgICAgY29ubmVjdGlvbnM6IHsgaW5wdXRzOiBbXSwgb3V0cHV0czogW10gfSxcbiAgICAgICAgaXNBY3RpdmU6IGZhbHNlLFxuICAgICAgICBsYXN0UHJvY2Vzc1RpbWU6IDAsXG4gICAgICB9O1xuXG4gICAgICBzZXQoKHN0YXRlKSA9PiB7XG4gICAgICAgIGNvbnN0IG5ld0NvbXBvbmVudHMgPSBuZXcgTWFwKHN0YXRlLmNvbXBvbmVudHMpLnNldChpZCwgY29tcG9uZW50KTtcblxuICAgICAgICAvLyBBdXRvLWNvbm5lY3QgdG8gYWRqYWNlbnQgY29tcG9uZW50c1xuICAgICAgICBhdXRvQ29ubmVjdENvbXBvbmVudChpZCwgY29tcG9uZW50LCBuZXdDb21wb25lbnRzKTtcblxuICAgICAgICByZXR1cm4geyBjb21wb25lbnRzOiBuZXdDb21wb25lbnRzIH07XG4gICAgICB9KTtcblxuICAgICAgcmV0dXJuIGlkO1xuICAgIH0sXG5cbiAgICByZW1vdmVDb21wb25lbnQ6IChpZDogc3RyaW5nKSA9PiB7XG4gICAgICBzZXQoKHN0YXRlKSA9PiB7XG4gICAgICAgIGNvbnN0IG5ld0NvbXBvbmVudHMgPSBuZXcgTWFwKHN0YXRlLmNvbXBvbmVudHMpO1xuICAgICAgICBjb25zdCBjb21wb25lbnQgPSBuZXdDb21wb25lbnRzLmdldChpZCk7XG4gICAgICAgIFxuICAgICAgICBpZiAoY29tcG9uZW50KSB7XG4gICAgICAgICAgLy8gUmVtb3ZlIGFsbCBjb25uZWN0aW9ucyB0byB0aGlzIGNvbXBvbmVudFxuICAgICAgICAgIG5ld0NvbXBvbmVudHMuZm9yRWFjaCgoY29tcCkgPT4ge1xuICAgICAgICAgICAgY29tcC5jb25uZWN0aW9ucy5pbnB1dHMgPSBjb21wLmNvbm5lY3Rpb25zLmlucHV0cy5maWx0ZXIoY29ubklkID0+IGNvbm5JZCAhPT0gaWQpO1xuICAgICAgICAgICAgY29tcC5jb25uZWN0aW9ucy5vdXRwdXRzID0gY29tcC5jb25uZWN0aW9ucy5vdXRwdXRzLmZpbHRlcihjb25uSWQgPT4gY29ubklkICE9PSBpZCk7XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgXG4gICAgICAgICAgbmV3Q29tcG9uZW50cy5kZWxldGUoaWQpO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHsgY29tcG9uZW50czogbmV3Q29tcG9uZW50cyB9O1xuICAgICAgfSk7XG4gICAgfSxcblxuICAgIG1vdmVDb21wb25lbnQ6IChpZDogc3RyaW5nLCBwb3NpdGlvbjogUG9zaXRpb24pID0+IHtcbiAgICAgIHNldCgoc3RhdGUpID0+IHtcbiAgICAgICAgY29uc3QgY29tcG9uZW50ID0gc3RhdGUuY29tcG9uZW50cy5nZXQoaWQpO1xuICAgICAgICBpZiAoIWNvbXBvbmVudCkgcmV0dXJuIHN0YXRlO1xuXG4gICAgICAgIGNvbnN0IGRlZmluaXRpb24gPSBDT01QT05FTlRfREVGSU5JVElPTlNbY29tcG9uZW50LnR5cGVdO1xuICAgICAgICBpZiAoIWlzUG9zaXRpb25WYWxpZChwb3NpdGlvbiwgZGVmaW5pdGlvbi5zaXplLCBzdGF0ZSwgaWQpKSB7XG4gICAgICAgICAgcmV0dXJuIHN0YXRlO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgbmV3Q29tcG9uZW50cyA9IG5ldyBNYXAoc3RhdGUuY29tcG9uZW50cyk7XG4gICAgICAgIG5ld0NvbXBvbmVudHMuc2V0KGlkLCB7IC4uLmNvbXBvbmVudCwgcG9zaXRpb24gfSk7XG4gICAgICAgIHJldHVybiB7IGNvbXBvbmVudHM6IG5ld0NvbXBvbmVudHMgfTtcbiAgICAgIH0pO1xuICAgIH0sXG5cbiAgICByb3RhdGVDb21wb25lbnQ6IChpZDogc3RyaW5nKSA9PiB7XG4gICAgICBzZXQoKHN0YXRlKSA9PiB7XG4gICAgICAgIGNvbnN0IGNvbXBvbmVudCA9IHN0YXRlLmNvbXBvbmVudHMuZ2V0KGlkKTtcbiAgICAgICAgaWYgKCFjb21wb25lbnQpIHJldHVybiBzdGF0ZTtcblxuICAgICAgICBjb25zdCBuZXdEaXJlY3Rpb24gPSAoY29tcG9uZW50LmRpcmVjdGlvbiArIDEpICUgNDtcbiAgICAgICAgY29uc3QgbmV3Q29tcG9uZW50cyA9IG5ldyBNYXAoc3RhdGUuY29tcG9uZW50cyk7XG4gICAgICAgIG5ld0NvbXBvbmVudHMuc2V0KGlkLCB7IC4uLmNvbXBvbmVudCwgZGlyZWN0aW9uOiBuZXdEaXJlY3Rpb24gfSk7XG4gICAgICAgIHJldHVybiB7IGNvbXBvbmVudHM6IG5ld0NvbXBvbmVudHMgfTtcbiAgICAgIH0pO1xuICAgIH0sXG5cbiAgICBjb25uZWN0Q29tcG9uZW50czogKGZyb21JZDogc3RyaW5nLCB0b0lkOiBzdHJpbmcpID0+IHtcbiAgICAgIGNvbnN0IHN0YXRlID0gZ2V0KCk7XG4gICAgICBjb25zdCBmcm9tQ29tcG9uZW50ID0gc3RhdGUuY29tcG9uZW50cy5nZXQoZnJvbUlkKTtcbiAgICAgIGNvbnN0IHRvQ29tcG9uZW50ID0gc3RhdGUuY29tcG9uZW50cy5nZXQodG9JZCk7XG5cbiAgICAgIGlmICghZnJvbUNvbXBvbmVudCB8fCAhdG9Db21wb25lbnQgfHwgZnJvbUlkID09PSB0b0lkKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgZnJvbURlZiA9IENPTVBPTkVOVF9ERUZJTklUSU9OU1tmcm9tQ29tcG9uZW50LnR5cGVdO1xuICAgICAgY29uc3QgdG9EZWYgPSBDT01QT05FTlRfREVGSU5JVElPTlNbdG9Db21wb25lbnQudHlwZV07XG5cbiAgICAgIC8vIENoZWNrIGNvbm5lY3Rpb24gbGltaXRzXG4gICAgICBpZiAoZnJvbUNvbXBvbmVudC5jb25uZWN0aW9ucy5vdXRwdXRzLmxlbmd0aCA+PSBmcm9tRGVmLm1heE91dHB1dHMgfHxcbiAgICAgICAgICB0b0NvbXBvbmVudC5jb25uZWN0aW9ucy5pbnB1dHMubGVuZ3RoID49IHRvRGVmLm1heElucHV0cykge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIC8vIENoZWNrIGlmIGFscmVhZHkgY29ubmVjdGVkXG4gICAgICBpZiAoZnJvbUNvbXBvbmVudC5jb25uZWN0aW9ucy5vdXRwdXRzLmluY2x1ZGVzKHRvSWQpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgc2V0KChzdGF0ZSkgPT4ge1xuICAgICAgICBjb25zdCBuZXdDb21wb25lbnRzID0gbmV3IE1hcChzdGF0ZS5jb21wb25lbnRzKTtcbiAgICAgICAgY29uc3QgbmV3RnJvbUNvbXBvbmVudCA9IHsgLi4uZnJvbUNvbXBvbmVudCB9O1xuICAgICAgICBjb25zdCBuZXdUb0NvbXBvbmVudCA9IHsgLi4udG9Db21wb25lbnQgfTtcblxuICAgICAgICBuZXdGcm9tQ29tcG9uZW50LmNvbm5lY3Rpb25zLm91dHB1dHMucHVzaCh0b0lkKTtcbiAgICAgICAgbmV3VG9Db21wb25lbnQuY29ubmVjdGlvbnMuaW5wdXRzLnB1c2goZnJvbUlkKTtcblxuICAgICAgICBuZXdDb21wb25lbnRzLnNldChmcm9tSWQsIG5ld0Zyb21Db21wb25lbnQpO1xuICAgICAgICBuZXdDb21wb25lbnRzLnNldCh0b0lkLCBuZXdUb0NvbXBvbmVudCk7XG5cbiAgICAgICAgcmV0dXJuIHsgY29tcG9uZW50czogbmV3Q29tcG9uZW50cyB9O1xuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH0sXG5cbiAgICBkaXNjb25uZWN0Q29tcG9uZW50czogKGZyb21JZDogc3RyaW5nLCB0b0lkOiBzdHJpbmcpID0+IHtcbiAgICAgIHNldCgoc3RhdGUpID0+IHtcbiAgICAgICAgY29uc3QgbmV3Q29tcG9uZW50cyA9IG5ldyBNYXAoc3RhdGUuY29tcG9uZW50cyk7XG4gICAgICAgIGNvbnN0IGZyb21Db21wb25lbnQgPSBuZXdDb21wb25lbnRzLmdldChmcm9tSWQpO1xuICAgICAgICBjb25zdCB0b0NvbXBvbmVudCA9IG5ld0NvbXBvbmVudHMuZ2V0KHRvSWQpO1xuXG4gICAgICAgIGlmIChmcm9tQ29tcG9uZW50ICYmIHRvQ29tcG9uZW50KSB7XG4gICAgICAgICAgZnJvbUNvbXBvbmVudC5jb25uZWN0aW9ucy5vdXRwdXRzID0gZnJvbUNvbXBvbmVudC5jb25uZWN0aW9ucy5vdXRwdXRzLmZpbHRlcihpZCA9PiBpZCAhPT0gdG9JZCk7XG4gICAgICAgICAgdG9Db21wb25lbnQuY29ubmVjdGlvbnMuaW5wdXRzID0gdG9Db21wb25lbnQuY29ubmVjdGlvbnMuaW5wdXRzLmZpbHRlcihpZCA9PiBpZCAhPT0gZnJvbUlkKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiB7IGNvbXBvbmVudHM6IG5ld0NvbXBvbmVudHMgfTtcbiAgICAgIH0pO1xuICAgIH0sXG5cbiAgICBzZXRDb21wb25lbnRSZWNpcGU6IChpZDogc3RyaW5nLCByZWNpcGVJZDogc3RyaW5nKSA9PiB7XG4gICAgICBzZXQoKHN0YXRlKSA9PiB7XG4gICAgICAgIGNvbnN0IGNvbXBvbmVudCA9IHN0YXRlLmNvbXBvbmVudHMuZ2V0KGlkKTtcbiAgICAgICAgaWYgKCFjb21wb25lbnQpIHJldHVybiBzdGF0ZTtcblxuICAgICAgICBjb25zdCByZWNpcGUgPSBSRUNJUEVTW3JlY2lwZUlkXTtcbiAgICAgICAgaWYgKCFyZWNpcGUpIHJldHVybiBzdGF0ZTtcblxuICAgICAgICBjb25zdCBuZXdDb21wb25lbnRzID0gbmV3IE1hcChzdGF0ZS5jb21wb25lbnRzKTtcbiAgICAgICAgbmV3Q29tcG9uZW50cy5zZXQoaWQsIHsgLi4uY29tcG9uZW50LCByZWNpcGUgfSk7XG4gICAgICAgIHJldHVybiB7IGNvbXBvbmVudHM6IG5ld0NvbXBvbmVudHMgfTtcbiAgICAgIH0pO1xuICAgIH0sXG5cbiAgICB0b2dnbGVTaW11bGF0aW9uOiAoKSA9PiB7XG4gICAgICBzZXQoKHN0YXRlKSA9PiAoeyBpc1J1bm5pbmc6ICFzdGF0ZS5pc1J1bm5pbmcgfSkpO1xuICAgIH0sXG5cbiAgICB1cGRhdGVTaW11bGF0aW9uOiAoKSA9PiB7XG4gICAgICBjb25zdCBzdGF0ZSA9IGdldCgpO1xuICAgICAgaWYgKCFzdGF0ZS5pc1J1bm5pbmcpIHJldHVybjtcblxuICAgICAgY29uc3QgdXBkYXRlcyA9IHNpbXVsYXRpb25FbmdpbmUudXBkYXRlU2ltdWxhdGlvbihzdGF0ZSk7XG4gICAgICBpZiAoT2JqZWN0LmtleXModXBkYXRlcykubGVuZ3RoID4gMCkge1xuICAgICAgICBjb25zdCBuZXdTdGF0ZSA9IHsgLi4uc3RhdGUsIC4uLnVwZGF0ZXMgfTtcbiAgICAgICAgY29uc3QgYW5hbHl0aWNzID0gcGVyZm9ybWFuY2VBbmFseXplci5hbmFseXplRmFjdG9yeShuZXdTdGF0ZSk7XG4gICAgICAgIHNldCgoY3VycmVudFN0YXRlKSA9PiAoe1xuICAgICAgICAgIC4uLmN1cnJlbnRTdGF0ZSxcbiAgICAgICAgICAuLi51cGRhdGVzLFxuICAgICAgICAgIGZhY3RvcnlBbmFseXRpY3M6IGFuYWx5dGljc1xuICAgICAgICB9KSk7XG4gICAgICB9XG4gICAgfSxcblxuICAgIGdldFBlcmZvcm1hbmNlTWV0cmljczogKCk6IFBlcmZvcm1hbmNlTWV0cmljcyA9PiB7XG4gICAgICBjb25zdCBzdGF0ZSA9IGdldCgpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgdGhyb3VnaHB1dDogc3RhdGUuc3RhdGlzdGljcy50b3RhbFByb2R1Y3Rpb24sXG4gICAgICAgIHV0aWxpemF0aW9uOiBuZXcgTWFwKCksXG4gICAgICAgIGJvdHRsZW5lY2tzOiBzdGF0ZS5zdGF0aXN0aWNzLmJvdHRsZW5lY2tzLFxuICAgICAgICBlZmZpY2llbmN5OiBzdGF0ZS5zdGF0aXN0aWNzLmVmZmljaWVuY3ksXG4gICAgICB9O1xuICAgIH0sXG5cbiAgICBnZXRGYWN0b3J5QW5hbHl0aWNzOiAoKTogRmFjdG9yeUFuYWx5dGljcyA9PiB7XG4gICAgICBjb25zdCBzdGF0ZSA9IGdldCgpO1xuICAgICAgaWYgKHN0YXRlLmZhY3RvcnlBbmFseXRpY3MpIHtcbiAgICAgICAgcmV0dXJuIHN0YXRlLmZhY3RvcnlBbmFseXRpY3M7XG4gICAgICB9XG4gICAgICAvLyBHZW5lcmF0ZSBhbmFseXRpY3Mgb24gZGVtYW5kIGlmIG5vdCBhdmFpbGFibGVcbiAgICAgIHJldHVybiBwZXJmb3JtYW5jZUFuYWx5emVyLmFuYWx5emVGYWN0b3J5KHN0YXRlKTtcbiAgICB9LFxuXG4gICAgZ2V0SGlzdG9yaWNhbERhdGE6IChrZXk6IHN0cmluZyk6IG51bWJlcltdID0+IHtcbiAgICAgIHJldHVybiBwZXJmb3JtYW5jZUFuYWx5emVyLmdldEhpc3RvcmljYWxEYXRhKGtleSk7XG4gICAgfSxcblxuICAgIGV4cG9ydEdhbWVTdGF0ZTogKCkgPT4ge1xuICAgICAgY29uc3Qgc3RhdGUgPSBnZXQoKTtcbiAgICAgIGNvbnN0IGV4cG9ydERhdGEgPSB7XG4gICAgICAgIGNvbXBvbmVudHM6IEFycmF5LmZyb20oc3RhdGUuY29tcG9uZW50cy5lbnRyaWVzKCkpLFxuICAgICAgICBncmlkU2l6ZTogc3RhdGUuZ3JpZFNpemUsXG4gICAgICAgIGdhbWVUaW1lOiBzdGF0ZS5nYW1lVGltZSxcbiAgICAgICAgcmVzb3VyY2VzOiBBcnJheS5mcm9tKHN0YXRlLnJlc291cmNlcy5lbnRyaWVzKCkpLFxuICAgICAgfTtcbiAgICAgIHJldHVybiBKU09OLnN0cmluZ2lmeShleHBvcnREYXRhLCBudWxsLCAyKTtcbiAgICB9LFxuXG4gICAgaW1wb3J0R2FtZVN0YXRlOiAoanNvblN0YXRlOiBzdHJpbmcpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBKU09OLnBhcnNlKGpzb25TdGF0ZSk7XG4gICAgICAgIHNldCh7XG4gICAgICAgICAgY29tcG9uZW50czogbmV3IE1hcChkYXRhLmNvbXBvbmVudHMpLFxuICAgICAgICAgIGdyaWRTaXplOiBkYXRhLmdyaWRTaXplIHx8IEdSSURfU0laRSxcbiAgICAgICAgICBnYW1lVGltZTogZGF0YS5nYW1lVGltZSB8fCAwLFxuICAgICAgICAgIHJlc291cmNlczogbmV3IE1hcChkYXRhLnJlc291cmNlcyksXG4gICAgICAgICAgaXNSdW5uaW5nOiBmYWxzZSxcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGltcG9ydCBnYW1lIHN0YXRlOicsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuICAgIH0sXG5cbiAgICByZXNldEdhbWU6ICgpID0+IHtcbiAgICAgIHNldChpbml0aWFsU3RhdGUpO1xuICAgIH0sXG4gIH0pKVxuKTtcblxuLy8gSGVscGVyIGZ1bmN0aW9uc1xuZnVuY3Rpb24gaXNQb3NpdGlvblZhbGlkKFxuICBwb3NpdGlvbjogUG9zaXRpb24sXG4gIHNpemU6IHsgd2lkdGg6IG51bWJlcjsgaGVpZ2h0OiBudW1iZXIgfSxcbiAgc3RhdGU6IEdhbWVTdGF0ZSxcbiAgZXhjbHVkZUlkPzogc3RyaW5nXG4pOiBib29sZWFuIHtcbiAgLy8gQ2hlY2sgYm91bmRzXG4gIGlmIChwb3NpdGlvbi54IDwgMCB8fCBwb3NpdGlvbi55IDwgMCB8fFxuICAgICAgcG9zaXRpb24ueCArIHNpemUud2lkdGggPiBzdGF0ZS5ncmlkU2l6ZS53aWR0aCB8fFxuICAgICAgcG9zaXRpb24ueSArIHNpemUuaGVpZ2h0ID4gc3RhdGUuZ3JpZFNpemUuaGVpZ2h0KSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgLy8gQ2hlY2sgZm9yIG92ZXJsYXBzIHdpdGggZXhpc3RpbmcgY29tcG9uZW50c1xuICBmb3IgKGNvbnN0IFtpZCwgY29tcG9uZW50XSBvZiBzdGF0ZS5jb21wb25lbnRzKSB7XG4gICAgaWYgKGV4Y2x1ZGVJZCAmJiBpZCA9PT0gZXhjbHVkZUlkKSBjb250aW51ZTtcbiAgICBcbiAgICBjb25zdCBjb21wRGVmID0gQ09NUE9ORU5UX0RFRklOSVRJT05TW2NvbXBvbmVudC50eXBlXTtcbiAgICBjb25zdCBjb21wUG9zID0gY29tcG9uZW50LnBvc2l0aW9uO1xuICAgIFxuICAgIC8vIENoZWNrIGlmIHJlY3RhbmdsZXMgb3ZlcmxhcFxuICAgIGlmICghKHBvc2l0aW9uLnggPj0gY29tcFBvcy54ICsgY29tcERlZi5zaXplLndpZHRoIHx8XG4gICAgICAgICAgcG9zaXRpb24ueCArIHNpemUud2lkdGggPD0gY29tcFBvcy54IHx8XG4gICAgICAgICAgcG9zaXRpb24ueSA+PSBjb21wUG9zLnkgKyBjb21wRGVmLnNpemUuaGVpZ2h0IHx8XG4gICAgICAgICAgcG9zaXRpb24ueSArIHNpemUuaGVpZ2h0IDw9IGNvbXBQb3MueSkpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICByZXR1cm4gdHJ1ZTtcbn1cblxuLy8gQXV0by1jb25uZWN0IGNvbXBvbmVudHMgYmFzZWQgb24gYWRqYWNlbmN5IGFuZCBkaXJlY3Rpb25cbmZ1bmN0aW9uIGF1dG9Db25uZWN0Q29tcG9uZW50KFxuICBuZXdJZDogc3RyaW5nLFxuICBuZXdDb21wb25lbnQ6IEdhbWVDb21wb25lbnQsXG4gIGNvbXBvbmVudHM6IE1hcDxzdHJpbmcsIEdhbWVDb21wb25lbnQ+XG4pOiB2b2lkIHtcbiAgY29uc3QgbmV3RGVmID0gQ09NUE9ORU5UX0RFRklOSVRJT05TW25ld0NvbXBvbmVudC50eXBlXTtcblxuICAvLyBGaW5kIGFkamFjZW50IGNvbXBvbmVudHNcbiAgZm9yIChjb25zdCBbZXhpc3RpbmdJZCwgZXhpc3RpbmdDb21wb25lbnRdIG9mIGNvbXBvbmVudHMpIHtcbiAgICBpZiAoZXhpc3RpbmdJZCA9PT0gbmV3SWQpIGNvbnRpbnVlO1xuXG4gICAgY29uc3QgZXhpc3RpbmdEZWYgPSBDT01QT05FTlRfREVGSU5JVElPTlNbZXhpc3RpbmdDb21wb25lbnQudHlwZV07XG5cbiAgICAvLyBDaGVjayBpZiBjb21wb25lbnRzIGFyZSBhZGphY2VudCBhbmQgY2FuIGNvbm5lY3RcbiAgICBpZiAoYXJlQ29tcG9uZW50c0FkamFjZW50KG5ld0NvbXBvbmVudCwgZXhpc3RpbmdDb21wb25lbnQpKSB7XG4gICAgICBjb25zdCBjb25uZWN0aW9uRGlyZWN0aW9uID0gZ2V0Q29ubmVjdGlvbkRpcmVjdGlvbihuZXdDb21wb25lbnQsIGV4aXN0aW5nQ29tcG9uZW50KTtcblxuICAgICAgLy8gRGV0ZXJtaW5lIHdoaWNoIGNvbXBvbmVudCBzaG91bGQgYmUgaW5wdXQvb3V0cHV0IGJhc2VkIG9uIGRpcmVjdGlvbiBhbmQgdHlwZVxuICAgICAgY29uc3Qgc2hvdWxkQ29ubmVjdCA9IHNob3VsZEF1dG9Db25uZWN0KFxuICAgICAgICBuZXdDb21wb25lbnQsIG5ld0RlZixcbiAgICAgICAgZXhpc3RpbmdDb21wb25lbnQsIGV4aXN0aW5nRGVmLFxuICAgICAgICBjb25uZWN0aW9uRGlyZWN0aW9uXG4gICAgICApO1xuXG4gICAgICBpZiAoc2hvdWxkQ29ubmVjdC5jb25uZWN0KSB7XG4gICAgICAgIGlmIChzaG91bGRDb25uZWN0Lm5ld0lzT3V0cHV0KSB7XG4gICAgICAgICAgLy8gTmV3IGNvbXBvbmVudCBvdXRwdXRzIHRvIGV4aXN0aW5nIGNvbXBvbmVudFxuICAgICAgICAgIGlmIChuZXdDb21wb25lbnQuY29ubmVjdGlvbnMub3V0cHV0cy5sZW5ndGggPCBuZXdEZWYubWF4T3V0cHV0cyAmJlxuICAgICAgICAgICAgICBleGlzdGluZ0NvbXBvbmVudC5jb25uZWN0aW9ucy5pbnB1dHMubGVuZ3RoIDwgZXhpc3RpbmdEZWYubWF4SW5wdXRzKSB7XG4gICAgICAgICAgICBuZXdDb21wb25lbnQuY29ubmVjdGlvbnMub3V0cHV0cy5wdXNoKGV4aXN0aW5nSWQpO1xuICAgICAgICAgICAgZXhpc3RpbmdDb21wb25lbnQuY29ubmVjdGlvbnMuaW5wdXRzLnB1c2gobmV3SWQpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBFeGlzdGluZyBjb21wb25lbnQgb3V0cHV0cyB0byBuZXcgY29tcG9uZW50XG4gICAgICAgICAgaWYgKGV4aXN0aW5nQ29tcG9uZW50LmNvbm5lY3Rpb25zLm91dHB1dHMubGVuZ3RoIDwgZXhpc3RpbmdEZWYubWF4T3V0cHV0cyAmJlxuICAgICAgICAgICAgICBuZXdDb21wb25lbnQuY29ubmVjdGlvbnMuaW5wdXRzLmxlbmd0aCA8IG5ld0RlZi5tYXhJbnB1dHMpIHtcbiAgICAgICAgICAgIGV4aXN0aW5nQ29tcG9uZW50LmNvbm5lY3Rpb25zLm91dHB1dHMucHVzaChuZXdJZCk7XG4gICAgICAgICAgICBuZXdDb21wb25lbnQuY29ubmVjdGlvbnMuaW5wdXRzLnB1c2goZXhpc3RpbmdJZCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIENoZWNrIGlmIHR3byBjb21wb25lbnRzIGFyZSBhZGphY2VudCAod2l0aGluIDEgZ3JpZCB1bml0KVxuZnVuY3Rpb24gYXJlQ29tcG9uZW50c0FkamFjZW50KGNvbXAxOiBHYW1lQ29tcG9uZW50LCBjb21wMjogR2FtZUNvbXBvbmVudCk6IGJvb2xlYW4ge1xuICBjb25zdCBkZWYxID0gQ09NUE9ORU5UX0RFRklOSVRJT05TW2NvbXAxLnR5cGVdO1xuICBjb25zdCBkZWYyID0gQ09NUE9ORU5UX0RFRklOSVRJT05TW2NvbXAyLnR5cGVdO1xuXG4gIC8vIENhbGN1bGF0ZSBjb21wb25lbnQgYm91bmRzXG4gIGNvbnN0IGNvbXAxQm91bmRzID0ge1xuICAgIGxlZnQ6IGNvbXAxLnBvc2l0aW9uLngsXG4gICAgcmlnaHQ6IGNvbXAxLnBvc2l0aW9uLnggKyBkZWYxLnNpemUud2lkdGgsXG4gICAgdG9wOiBjb21wMS5wb3NpdGlvbi55LFxuICAgIGJvdHRvbTogY29tcDEucG9zaXRpb24ueSArIGRlZjEuc2l6ZS5oZWlnaHQsXG4gIH07XG5cbiAgY29uc3QgY29tcDJCb3VuZHMgPSB7XG4gICAgbGVmdDogY29tcDIucG9zaXRpb24ueCxcbiAgICByaWdodDogY29tcDIucG9zaXRpb24ueCArIGRlZjIuc2l6ZS53aWR0aCxcbiAgICB0b3A6IGNvbXAyLnBvc2l0aW9uLnksXG4gICAgYm90dG9tOiBjb21wMi5wb3NpdGlvbi55ICsgZGVmMi5zaXplLmhlaWdodCxcbiAgfTtcblxuICAvLyBDaGVjayBpZiB0aGV5J3JlIGFkamFjZW50ICh0b3VjaGluZyBidXQgbm90IG92ZXJsYXBwaW5nKVxuICBjb25zdCBob3Jpem9udGFsbHlBZGphY2VudCA9XG4gICAgKGNvbXAxQm91bmRzLnJpZ2h0ID09PSBjb21wMkJvdW5kcy5sZWZ0IHx8IGNvbXAyQm91bmRzLnJpZ2h0ID09PSBjb21wMUJvdW5kcy5sZWZ0KSAmJlxuICAgICEoY29tcDFCb3VuZHMuYm90dG9tIDw9IGNvbXAyQm91bmRzLnRvcCB8fCBjb21wMkJvdW5kcy5ib3R0b20gPD0gY29tcDFCb3VuZHMudG9wKTtcblxuICBjb25zdCB2ZXJ0aWNhbGx5QWRqYWNlbnQgPVxuICAgIChjb21wMUJvdW5kcy5ib3R0b20gPT09IGNvbXAyQm91bmRzLnRvcCB8fCBjb21wMkJvdW5kcy5ib3R0b20gPT09IGNvbXAxQm91bmRzLnRvcCkgJiZcbiAgICAhKGNvbXAxQm91bmRzLnJpZ2h0IDw9IGNvbXAyQm91bmRzLmxlZnQgfHwgY29tcDJCb3VuZHMucmlnaHQgPD0gY29tcDFCb3VuZHMubGVmdCk7XG5cbiAgcmV0dXJuIGhvcml6b250YWxseUFkamFjZW50IHx8IHZlcnRpY2FsbHlBZGphY2VudDtcbn1cblxuLy8gR2V0IHRoZSBkaXJlY3Rpb24gZnJvbSBjb21wMSB0byBjb21wMlxuZnVuY3Rpb24gZ2V0Q29ubmVjdGlvbkRpcmVjdGlvbihjb21wMTogR2FtZUNvbXBvbmVudCwgY29tcDI6IEdhbWVDb21wb25lbnQpOiBEaXJlY3Rpb24ge1xuICBjb25zdCBkeCA9IGNvbXAyLnBvc2l0aW9uLnggLSBjb21wMS5wb3NpdGlvbi54O1xuICBjb25zdCBkeSA9IGNvbXAyLnBvc2l0aW9uLnkgLSBjb21wMS5wb3NpdGlvbi55O1xuXG4gIGlmIChNYXRoLmFicyhkeCkgPiBNYXRoLmFicyhkeSkpIHtcbiAgICByZXR1cm4gZHggPiAwID8gRGlyZWN0aW9uLkVBU1QgOiBEaXJlY3Rpb24uV0VTVDtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gZHkgPiAwID8gRGlyZWN0aW9uLlNPVVRIIDogRGlyZWN0aW9uLk5PUlRIO1xuICB9XG59XG5cbi8vIERldGVybWluZSBpZiBjb21wb25lbnRzIHNob3VsZCBhdXRvLWNvbm5lY3QgYW5kIGluIHdoaWNoIGRpcmVjdGlvblxuZnVuY3Rpb24gc2hvdWxkQXV0b0Nvbm5lY3QoXG4gIGNvbXAxOiBHYW1lQ29tcG9uZW50LCBkZWYxOiBDb21wb25lbnREZWZpbml0aW9uLFxuICBjb21wMjogR2FtZUNvbXBvbmVudCwgZGVmMjogQ29tcG9uZW50RGVmaW5pdGlvbixcbiAgZGlyZWN0aW9uOiBEaXJlY3Rpb25cbik6IHsgY29ubmVjdDogYm9vbGVhbjsgbmV3SXNPdXRwdXQ6IGJvb2xlYW4gfSB7XG4gIC8vIE1pbmVycyBhbHdheXMgb3V0cHV0XG4gIGlmIChjb21wMS50eXBlID09PSBDb21wb25lbnRUeXBlLk1JTkVSICYmIGRlZjIubWF4SW5wdXRzID4gMCkge1xuICAgIHJldHVybiB7IGNvbm5lY3Q6IHRydWUsIG5ld0lzT3V0cHV0OiB0cnVlIH07XG4gIH1cbiAgaWYgKGNvbXAyLnR5cGUgPT09IENvbXBvbmVudFR5cGUuTUlORVIgJiYgZGVmMS5tYXhJbnB1dHMgPiAwKSB7XG4gICAgcmV0dXJuIHsgY29ubmVjdDogdHJ1ZSwgbmV3SXNPdXRwdXQ6IGZhbHNlIH07XG4gIH1cblxuICAvLyBBc3NlbWJsZXJzIHByZWZlciB0byBvdXRwdXQgdG8gc3RvcmFnZSBvciBjb252ZXlvcnNcbiAgaWYgKGNvbXAxLnR5cGUgPT09IENvbXBvbmVudFR5cGUuQVNTRU1CTEVSICYmXG4gICAgICAoY29tcDIudHlwZSA9PT0gQ29tcG9uZW50VHlwZS5TVE9SQUdFIHx8IGNvbXAyLnR5cGUgPT09IENvbXBvbmVudFR5cGUuQ09OVkVZT1IpKSB7XG4gICAgcmV0dXJuIHsgY29ubmVjdDogdHJ1ZSwgbmV3SXNPdXRwdXQ6IHRydWUgfTtcbiAgfVxuICBpZiAoY29tcDIudHlwZSA9PT0gQ29tcG9uZW50VHlwZS5BU1NFTUJMRVIgJiZcbiAgICAgIChjb21wMS50eXBlID09PSBDb21wb25lbnRUeXBlLlNUT1JBR0UgfHwgY29tcDEudHlwZSA9PT0gQ29tcG9uZW50VHlwZS5DT05WRVlPUikpIHtcbiAgICByZXR1cm4geyBjb25uZWN0OiB0cnVlLCBuZXdJc091dHB1dDogZmFsc2UgfTtcbiAgfVxuXG4gIC8vIENvbnZleW9ycyBjb25uZWN0IGluIHRoZSBkaXJlY3Rpb24gdGhleSdyZSBmYWNpbmdcbiAgaWYgKGNvbXAxLnR5cGUgPT09IENvbXBvbmVudFR5cGUuQ09OVkVZT1IgJiYgY29tcDEuZGlyZWN0aW9uID09PSBkaXJlY3Rpb24gJiYgZGVmMi5tYXhJbnB1dHMgPiAwKSB7XG4gICAgcmV0dXJuIHsgY29ubmVjdDogdHJ1ZSwgbmV3SXNPdXRwdXQ6IHRydWUgfTtcbiAgfVxuICBpZiAoY29tcDIudHlwZSA9PT0gQ29tcG9uZW50VHlwZS5DT05WRVlPUiAmJiBjb21wMi5kaXJlY3Rpb24gPT09IGdldE9wcG9zaXRlRGlyZWN0aW9uKGRpcmVjdGlvbikgJiYgZGVmMS5tYXhJbnB1dHMgPiAwKSB7XG4gICAgcmV0dXJuIHsgY29ubmVjdDogdHJ1ZSwgbmV3SXNPdXRwdXQ6IGZhbHNlIH07XG4gIH1cblxuICAvLyBEZWZhdWx0OiBkb24ndCBhdXRvLWNvbm5lY3RcbiAgcmV0dXJuIHsgY29ubmVjdDogZmFsc2UsIG5ld0lzT3V0cHV0OiBmYWxzZSB9O1xufVxuIl0sIm5hbWVzIjpbImNyZWF0ZSIsInN1YnNjcmliZVdpdGhTZWxlY3RvciIsIkNvbXBvbmVudFR5cGUiLCJEaXJlY3Rpb24iLCJSZXNvdXJjZVR5cGUiLCJDT01QT05FTlRfREVGSU5JVElPTlMiLCJSRUNJUEVTIiwiZ2VuZXJhdGVJZCIsImdldE9wcG9zaXRlRGlyZWN0aW9uIiwiU2ltdWxhdGlvbkVuZ2luZSIsIlBlcmZvcm1hbmNlQW5hbHl6ZXIiLCJHUklEX1NJWkUiLCJ3aWR0aCIsImhlaWdodCIsInNpbXVsYXRpb25FbmdpbmUiLCJwZXJmb3JtYW5jZUFuYWx5emVyIiwiaW5pdGlhbFN0YXRlIiwiY29tcG9uZW50cyIsIk1hcCIsImdyaWRTaXplIiwiaXNSdW5uaW5nIiwiZ2FtZVRpbWUiLCJyZXNvdXJjZXMiLCJJUk9OX09SRSIsIkNPUFBFUl9PUkUiLCJDT0FMIiwiSVJPTl9QTEFURSIsIkNPUFBFUl9QTEFURSIsIkdFQVIiLCJDSVJDVUlUIiwic3RhdGlzdGljcyIsInRvdGFsUHJvZHVjdGlvbiIsInRvdGFsQ29uc3VtcHRpb24iLCJlZmZpY2llbmN5IiwiYm90dGxlbmVja3MiLCJpbml0aWFsU3RvcmVTdGF0ZSIsImZhY3RvcnlBbmFseXRpY3MiLCJ1c2VHYW1lU3RvcmUiLCJzZXQiLCJnZXQiLCJhZGRDb21wb25lbnQiLCJ0eXBlIiwicG9zaXRpb24iLCJkaXJlY3Rpb24iLCJOT1JUSCIsInN0YXRlIiwiZGVmaW5pdGlvbiIsImlzUG9zaXRpb25WYWxpZCIsInNpemUiLCJpZCIsImNvbXBvbmVudCIsImludmVudG9yeSIsImNvbm5lY3Rpb25zIiwiaW5wdXRzIiwib3V0cHV0cyIsImlzQWN0aXZlIiwibGFzdFByb2Nlc3NUaW1lIiwibmV3Q29tcG9uZW50cyIsImF1dG9Db25uZWN0Q29tcG9uZW50IiwicmVtb3ZlQ29tcG9uZW50IiwiZm9yRWFjaCIsImNvbXAiLCJmaWx0ZXIiLCJjb25uSWQiLCJkZWxldGUiLCJtb3ZlQ29tcG9uZW50Iiwicm90YXRlQ29tcG9uZW50IiwibmV3RGlyZWN0aW9uIiwiY29ubmVjdENvbXBvbmVudHMiLCJmcm9tSWQiLCJ0b0lkIiwiZnJvbUNvbXBvbmVudCIsInRvQ29tcG9uZW50IiwiZnJvbURlZiIsInRvRGVmIiwibGVuZ3RoIiwibWF4T3V0cHV0cyIsIm1heElucHV0cyIsImluY2x1ZGVzIiwibmV3RnJvbUNvbXBvbmVudCIsIm5ld1RvQ29tcG9uZW50IiwicHVzaCIsImRpc2Nvbm5lY3RDb21wb25lbnRzIiwic2V0Q29tcG9uZW50UmVjaXBlIiwicmVjaXBlSWQiLCJyZWNpcGUiLCJ0b2dnbGVTaW11bGF0aW9uIiwidXBkYXRlU2ltdWxhdGlvbiIsInVwZGF0ZXMiLCJPYmplY3QiLCJrZXlzIiwibmV3U3RhdGUiLCJhbmFseXRpY3MiLCJhbmFseXplRmFjdG9yeSIsImN1cnJlbnRTdGF0ZSIsImdldFBlcmZvcm1hbmNlTWV0cmljcyIsInRocm91Z2hwdXQiLCJ1dGlsaXphdGlvbiIsImdldEZhY3RvcnlBbmFseXRpY3MiLCJnZXRIaXN0b3JpY2FsRGF0YSIsImtleSIsImV4cG9ydEdhbWVTdGF0ZSIsImV4cG9ydERhdGEiLCJBcnJheSIsImZyb20iLCJlbnRyaWVzIiwiSlNPTiIsInN0cmluZ2lmeSIsImltcG9ydEdhbWVTdGF0ZSIsImpzb25TdGF0ZSIsImRhdGEiLCJwYXJzZSIsImVycm9yIiwiY29uc29sZSIsInJlc2V0R2FtZSIsImV4Y2x1ZGVJZCIsIngiLCJ5IiwiY29tcERlZiIsImNvbXBQb3MiLCJuZXdJZCIsIm5ld0NvbXBvbmVudCIsIm5ld0RlZiIsImV4aXN0aW5nSWQiLCJleGlzdGluZ0NvbXBvbmVudCIsImV4aXN0aW5nRGVmIiwiYXJlQ29tcG9uZW50c0FkamFjZW50IiwiY29ubmVjdGlvbkRpcmVjdGlvbiIsImdldENvbm5lY3Rpb25EaXJlY3Rpb24iLCJzaG91bGRDb25uZWN0Iiwic2hvdWxkQXV0b0Nvbm5lY3QiLCJjb25uZWN0IiwibmV3SXNPdXRwdXQiLCJjb21wMSIsImNvbXAyIiwiZGVmMSIsImRlZjIiLCJjb21wMUJvdW5kcyIsImxlZnQiLCJyaWdodCIsInRvcCIsImJvdHRvbSIsImNvbXAyQm91bmRzIiwiaG9yaXpvbnRhbGx5QWRqYWNlbnQiLCJ2ZXJ0aWNhbGx5QWRqYWNlbnQiLCJkeCIsImR5IiwiTWF0aCIsImFicyIsIkVBU1QiLCJXRVNUIiwiU09VVEgiLCJNSU5FUiIsIkFTU0VNQkxFUiIsIlNUT1JBR0UiLCJDT05WRVlPUiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/gameStore.ts\n"));

/***/ })

});