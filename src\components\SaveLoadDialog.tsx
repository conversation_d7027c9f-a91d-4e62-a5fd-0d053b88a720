'use client';

import React, { useState, useEffect } from 'react';
import { useGameStore } from '@/store/gameStore';
import { SaveSlot } from '@/utils/saveSystem';
import { formatTime } from '@/utils/helpers';
import {
  X,
  Save,
  FolderOpen,
  Trash2,
  Download,
  Upload,
  Clock,
  Cog,
  Zap,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';
import clsx from 'clsx';

interface SaveLoadDialogProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'save' | 'load';
}

const SaveLoadDialog: React.FC<SaveLoadDialogProps> = ({ isOpen, onClose, mode }) => {
  const {
    saveGame,
    loadGame,
    getSaveSlots,
    deleteSave,
    hasAutoSave,
    loadAutoSave,
  } = useGameStore();

  const [saveSlots, setSaveSlots] = useState<SaveSlot[]>([]);
  const [newSaveName, setNewSaveName] = useState('');
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    if (isOpen) {
      setSaveSlots(getSaveSlots());
      setNewSaveName('');
      setSelectedSlot(null);
      setMessage(null);
    }
  }, [isOpen, getSaveSlots]);

  const handleSave = () => {
    if (!newSaveName.trim()) {
      setMessage({ type: 'error', text: 'Please enter a save name' });
      return;
    }

    const success = saveGame(newSaveName.trim());
    if (success) {
      setMessage({ type: 'success', text: 'Game saved successfully!' });
      setSaveSlots(getSaveSlots());
      setNewSaveName('');
      setTimeout(() => onClose(), 1500);
    } else {
      setMessage({ type: 'error', text: 'Failed to save game' });
    }
  };

  const handleLoad = (slotId: string) => {
    const success = loadGame(slotId);
    if (success) {
      setMessage({ type: 'success', text: 'Game loaded successfully!' });
      setTimeout(() => onClose(), 1000);
    } else {
      setMessage({ type: 'error', text: 'Failed to load game' });
    }
  };

  const handleLoadAutoSave = () => {
    const success = loadAutoSave();
    if (success) {
      setMessage({ type: 'success', text: 'Auto-save loaded successfully!' });
      setTimeout(() => onClose(), 1000);
    } else {
      setMessage({ type: 'error', text: 'Failed to load auto-save' });
    }
  };

  const handleDelete = (slotId: string) => {
    if (confirm('Are you sure you want to delete this save?')) {
      const success = deleteSave(slotId);
      if (success) {
        setSaveSlots(getSaveSlots());
        setMessage({ type: 'success', text: 'Save deleted' });
      } else {
        setMessage({ type: 'error', text: 'Failed to delete save' });
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white flex items-center gap-2">
            {mode === 'save' ? (
              <>
                <Save className="w-5 h-5" />
                Save Game
              </>
            ) : (
              <>
                <FolderOpen className="w-5 h-5" />
                Load Game
              </>
            )}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Message */}
        {message && (
          <div className={clsx(
            'mb-4 p-3 rounded-lg flex items-center gap-2',
            {
              'bg-green-900 text-green-300': message.type === 'success',
              'bg-red-900 text-red-300': message.type === 'error',
            }
          )}>
            {message.type === 'success' ? (
              <CheckCircle className="w-4 h-4" />
            ) : (
              <AlertCircle className="w-4 h-4" />
            )}
            {message.text}
          </div>
        )}

        {/* Save Input (Save mode only) */}
        {mode === 'save' && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Save Name
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={newSaveName}
                onChange={(e) => setNewSaveName(e.target.value)}
                placeholder="Enter save name..."
                className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                onKeyPress={(e) => e.key === 'Enter' && handleSave()}
              />
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                Save
              </button>
            </div>
          </div>
        )}

        {/* Auto-save (Load mode only) */}
        {mode === 'load' && hasAutoSave() && (
          <div className="mb-4 p-3 bg-gray-700 rounded-lg">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-medium text-white">Auto-save</h3>
                <p className="text-sm text-gray-300">Most recent automatic save</p>
              </div>
              <button
                onClick={handleLoadAutoSave}
                className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
              >
                Load
              </button>
            </div>
          </div>
        )}

        {/* Save Slots */}
        <div className="flex-1 overflow-y-auto">
          <h3 className="text-lg font-semibold text-white mb-3">
            {mode === 'save' ? 'Existing Saves' : 'Saved Games'}
          </h3>
          
          {saveSlots.length === 0 ? (
            <div className="text-center text-gray-400 py-8">
              <FolderOpen className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>No saved games found</p>
            </div>
          ) : (
            <div className="space-y-2">
              {saveSlots.map((slot) => (
                <div
                  key={slot.id}
                  className={clsx(
                    'p-3 bg-gray-700 rounded-lg border transition-colors',
                    {
                      'border-blue-500': selectedSlot === slot.id,
                      'border-gray-600 hover:border-gray-500': selectedSlot !== slot.id,
                    }
                  )}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium text-white">{slot.name}</h4>
                      <div className="text-sm text-gray-300 mt-1 space-y-1">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {new Date(slot.timestamp).toLocaleString()}
                          </span>
                          <span className="flex items-center gap-1">
                            <Cog className="w-3 h-3" />
                            {slot.metadata.componentCount} components
                          </span>
                          <span className="flex items-center gap-1">
                            <Zap className="w-3 h-3" />
                            {(slot.metadata.efficiency * 100).toFixed(1)}% efficiency
                          </span>
                        </div>
                        <div className="text-xs text-gray-400">
                          Game time: {formatTime(slot.metadata.gameTime)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 ml-4">
                      {mode === 'load' && (
                        <button
                          onClick={() => handleLoad(slot.id)}
                          className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
                        >
                          Load
                        </button>
                      )}
                      <button
                        onClick={() => handleDelete(slot.id)}
                        className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                        title="Delete save"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-4 pt-4 border-t border-gray-700 flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default SaveLoadDialog;
